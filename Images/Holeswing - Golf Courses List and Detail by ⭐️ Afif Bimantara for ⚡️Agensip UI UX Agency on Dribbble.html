<!DOCTYPE html>
<!-- saved from url=(0074)https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail -->
<html lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>Holeswing - Golf Courses List and Detail by ⭐️ <PERSON>fi<PERSON> Bimantara for ⚡️Agensip UI UX Agency on Dribbble</title>
  <meta name="description" content="Discover 1 Find Nearby Places design on Dribbble. Your resource to discover and connect with designers worldwide.">
  
<link rel="stylesheet" href="./Holeswing - Golf Courses List and Detail by ⭐️ <PERSON><PERSON><PERSON> Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/toasty-sign-up-cfa11915d0f8c4e1f124afdbf0449d9c7fa4de9c527fe11dab893ae463de615a.css"><link rel="stylesheet" href="./Holeswing - Golf Courses List and Detail by ⭐️ <PERSON><PERSON><PERSON> Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/media-overlay-modal-e335ee62734f01cca5c77637ef3fea3a0363b61dce89f701bbadff6f752a4419.css"><script type="text/javascript" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/7840d0c136"></script><script type="text/javascript" async="" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/analytics.js"></script><script type="text/javascript" async="" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/analytics.js"></script><script type="text/javascript" async="" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/js"></script><script type="text/javascript" async="" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/js(1)"></script><script src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/nr-1215.min.js"></script><script type="text/javascript" async="" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/f.txt"></script><script type="text/javascript" async="" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/js(2)"></script><script type="text/javascript" async="" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/analytics.js"></script><script type="text/javascript" integrity="sha384-girahbTbYZ9tT03PWWj0mEVgyxtZoyDF9KVZdL+R53PP5wCY0PiVUKq0jeRlMx9M" crossorigin="anonymous" async="" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/amplitude-7.2.1-min.gz.js"></script><script async="" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/snplw.js"></script><script type="text/javascript">window.NREUM||(NREUM={});NREUM.info={"beacon":"bam-cell.nr-data.net","errorBeacon":"bam-cell.nr-data.net","licenseKey":"7840d0c136","applicationID":"2909452","transactionName":"Jg1bREUOClsARUpAAgIRGkNfDhE=","queueTime":1,"applicationTime":213,"agent":""}</script>
<script type="text/javascript">(window.NREUM||(NREUM={})).init={privacy:{cookies_enabled:true},ajax:{deny_list:["bam-cell.nr-data.net"]}};(window.NREUM||(NREUM={})).loader_config={xpid:"VQEAWVVACgoAXVVXBQ==",licenseKey:"7840d0c136",applicationID:"2909452"};window.NREUM||(NREUM={}),__nr_require=function(t,e,n){function r(n){if(!e[n]){var i=e[n]={exports:{}};t[n][0].call(i.exports,function(e){var i=t[n][1][e];return r(i||e)},i,i.exports)}return e[n].exports}if("function"==typeof __nr_require)return __nr_require;for(var i=0;i<n.length;i++)r(n[i]);return r}({1:[function(t,e,n){function r(t){try{s.console&&console.log(t)}catch(e){}}var i,o=t("ee"),a=t(28),s={};try{i=localStorage.getItem("__nr_flags").split(","),console&&"function"==typeof console.log&&(s.console=!0,i.indexOf("dev")!==-1&&(s.dev=!0),i.indexOf("nr_dev")!==-1&&(s.nrDev=!0))}catch(c){}s.nrDev&&o.on("internal-error",function(t){r(t.stack)}),s.dev&&o.on("fn-err",function(t,e,n){r(n.stack)}),s.dev&&(r("NR AGENT IN DEVELOPMENT MODE"),r("flags: "+a(s,function(t,e){return t}).join(", ")))},{}],2:[function(t,e,n){function r(t,e,n,r,s){try{p?p-=1:i(s||new UncaughtException(t,e,n),!0)}catch(f){try{o("ierr",[f,c.now(),!0])}catch(d){}}return"function"==typeof u&&u.apply(this,a(arguments))}function UncaughtException(t,e,n){this.message=t||"Uncaught error with no additional information",this.sourceURL=e,this.line=n}function i(t,e){var n=e?null:c.now();o("err",[t,n])}var o=t("handle"),a=t(29),s=t("ee"),c=t("loader"),f=t("gos"),u=window.onerror,d=!1,l="nr@seenError";if(!c.disabled){var p=0;c.features.err=!0,t(1),window.onerror=r;try{throw new Error}catch(h){"stack"in h&&(t(10),t(9),"addEventListener"in window&&t(6),c.xhrWrappable&&t(11),d=!0)}s.on("fn-start",function(t,e,n){d&&(p+=1)}),s.on("fn-err",function(t,e,n){d&&!n[l]&&(f(n,l,function(){return!0}),this.thrown=!0,i(n))}),s.on("fn-end",function(){d&&!this.thrown&&p>0&&(p-=1)}),s.on("internal-error",function(t){o("ierr",[t,c.now(),!0])})}},{}],3:[function(t,e,n){var r=t("loader");r.disabled||(r.features.ins=!0)},{}],4:[function(t,e,n){function r(){var t=new PerformanceObserver(function(t,e){var n=t.getEntries();s(v,[n])});try{t.observe({entryTypes:["resource"]})}catch(e){}}function i(t){if(s(v,[window.performance.getEntriesByType(w)]),window.performance["c"+l])try{window.performance[h](m,i,!1)}catch(t){}else try{window.performance[h]("webkit"+m,i,!1)}catch(t){}}function o(t){}if(window.performance&&window.performance.timing&&window.performance.getEntriesByType){var a=t("ee"),s=t("handle"),c=t(10),f=t(9),u=t(5),d=t(19),l="learResourceTimings",p="addEventListener",h="removeEventListener",m="resourcetimingbufferfull",v="bstResource",w="resource",g="-start",y="-end",x="fn"+g,b="fn"+y,E="bstTimer",R="pushState",S=t("loader");if(!S.disabled){S.features.stn=!0,t(8),"addEventListener"in window&&t(6);var O=NREUM.o.EV;a.on(x,function(t,e){var n=t[0];n instanceof O&&(this.bstStart=S.now())}),a.on(b,function(t,e){var n=t[0];n instanceof O&&s("bst",[n,e,this.bstStart,S.now()])}),c.on(x,function(t,e,n){this.bstStart=S.now(),this.bstType=n}),c.on(b,function(t,e){s(E,[e,this.bstStart,S.now(),this.bstType])}),f.on(x,function(){this.bstStart=S.now()}),f.on(b,function(t,e){s(E,[e,this.bstStart,S.now(),"requestAnimationFrame"])}),a.on(R+g,function(t){this.time=S.now(),this.startPath=location.pathname+location.hash}),a.on(R+y,function(t){s("bstHist",[location.pathname+location.hash,this.startPath,this.time])}),u()?(s(v,[window.performance.getEntriesByType("resource")]),r()):p in window.performance&&(window.performance["c"+l]?window.performance[p](m,i,d(!1)):window.performance[p]("webkit"+m,i,d(!1))),document[p]("scroll",o,d(!1)),document[p]("keypress",o,d(!1)),document[p]("click",o,d(!1))}}},{}],5:[function(t,e,n){e.exports=function(){return"PerformanceObserver"in window&&"function"==typeof window.PerformanceObserver}},{}],6:[function(t,e,n){function r(t){for(var e=t;e&&!e.hasOwnProperty(u);)e=Object.getPrototypeOf(e);e&&i(e)}function i(t){s.inPlace(t,[u,d],"-",o)}function o(t,e){return t[1]}var a=t("ee").get("events"),s=t("wrap-function")(a,!0),c=t("gos"),f=XMLHttpRequest,u="addEventListener",d="removeEventListener";e.exports=a,"getPrototypeOf"in Object?(r(document),r(window),r(f.prototype)):f.prototype.hasOwnProperty(u)&&(i(window),i(f.prototype)),a.on(u+"-start",function(t,e){var n=t[1];if(null!==n&&("function"==typeof n||"object"==typeof n)){var r=c(n,"nr@wrapped",function(){function t(){if("function"==typeof n.handleEvent)return n.handleEvent.apply(n,arguments)}var e={object:t,"function":n}[typeof n];return e?s(e,"fn-",null,e.name||"anonymous"):n});this.wrapped=t[1]=r}}),a.on(d+"-start",function(t){t[1]=this.wrapped||t[1]})},{}],7:[function(t,e,n){function r(t,e,n){var r=t[e];"function"==typeof r&&(t[e]=function(){var t=o(arguments),e={};i.emit(n+"before-start",[t],e);var a;e[m]&&e[m].dt&&(a=e[m].dt);var s=r.apply(this,t);return i.emit(n+"start",[t,a],s),s.then(function(t){return i.emit(n+"end",[null,t],s),t},function(t){throw i.emit(n+"end",[t],s),t})})}var i=t("ee").get("fetch"),o=t(29),a=t(28);e.exports=i;var s=window,c="fetch-",f=c+"body-",u=["arrayBuffer","blob","json","text","formData"],d=s.Request,l=s.Response,p=s.fetch,h="prototype",m="nr@context";d&&l&&p&&(a(u,function(t,e){r(d[h],e,f),r(l[h],e,f)}),r(s,"fetch",c),i.on(c+"end",function(t,e){var n=this;if(e){var r=e.headers.get("content-length");null!==r&&(n.rxSize=r),i.emit(c+"done",[null,e],n)}else i.emit(c+"done",[t],n)}))},{}],8:[function(t,e,n){var r=t("ee").get("history"),i=t("wrap-function")(r);e.exports=r;var o=window.history&&window.history.constructor&&window.history.constructor.prototype,a=window.history;o&&o.pushState&&o.replaceState&&(a=o),i.inPlace(a,["pushState","replaceState"],"-")},{}],9:[function(t,e,n){var r=t("ee").get("raf"),i=t("wrap-function")(r),o="equestAnimationFrame";e.exports=r,i.inPlace(window,["r"+o,"mozR"+o,"webkitR"+o,"msR"+o],"raf-"),r.on("raf-start",function(t){t[0]=i(t[0],"fn-")})},{}],10:[function(t,e,n){function r(t,e,n){t[0]=a(t[0],"fn-",null,n)}function i(t,e,n){this.method=n,this.timerDuration=isNaN(t[1])?0:+t[1],t[0]=a(t[0],"fn-",this,n)}var o=t("ee").get("timer"),a=t("wrap-function")(o),s="setTimeout",c="setInterval",f="clearTimeout",u="-start",d="-";e.exports=o,a.inPlace(window,[s,"setImmediate"],s+d),a.inPlace(window,[c],c+d),a.inPlace(window,[f,"clearImmediate"],f+d),o.on(c+u,r),o.on(s+u,i)},{}],11:[function(t,e,n){function r(t,e){d.inPlace(e,["onreadystatechange"],"fn-",s)}function i(){var t=this,e=u.context(t);t.readyState>3&&!e.resolved&&(e.resolved=!0,u.emit("xhr-resolved",[],t)),d.inPlace(t,y,"fn-",s)}function o(t){x.push(t),m&&(E?E.then(a):w?w(a):(R=-R,S.data=R))}function a(){for(var t=0;t<x.length;t++)r([],x[t]);x.length&&(x=[])}function s(t,e){return e}function c(t,e){for(var n in t)e[n]=t[n];return e}t(6);var f=t("ee"),u=f.get("xhr"),d=t("wrap-function")(u),l=t(19),p=NREUM.o,h=p.XHR,m=p.MO,v=p.PR,w=p.SI,g="readystatechange",y=["onload","onerror","onabort","onloadstart","onloadend","onprogress","ontimeout"],x=[];e.exports=u;var b=window.XMLHttpRequest=function(t){var e=new h(t);try{u.emit("new-xhr",[e],e),e.addEventListener(g,i,l(!1))}catch(n){try{u.emit("internal-error",[n])}catch(r){}}return e};if(c(h,b),b.prototype=h.prototype,d.inPlace(b.prototype,["open","send"],"-xhr-",s),u.on("send-xhr-start",function(t,e){r(t,e),o(e)}),u.on("open-xhr-start",r),m){var E=v&&v.resolve();if(!w&&!v){var R=1,S=document.createTextNode(R);new m(a).observe(S,{characterData:!0})}}else f.on("fn-end",function(t){t[0]&&t[0].type===g||a()})},{}],12:[function(t,e,n){function r(t){if(!s(t))return null;var e=window.NREUM;if(!e.loader_config)return null;var n=(e.loader_config.accountID||"").toString()||null,r=(e.loader_config.agentID||"").toString()||null,f=(e.loader_config.trustKey||"").toString()||null;if(!n||!r)return null;var h=p.generateSpanId(),m=p.generateTraceId(),v=Date.now(),w={spanId:h,traceId:m,timestamp:v};return(t.sameOrigin||c(t)&&l())&&(w.traceContextParentHeader=i(h,m),w.traceContextStateHeader=o(h,v,n,r,f)),(t.sameOrigin&&!u()||!t.sameOrigin&&c(t)&&d())&&(w.newrelicHeader=a(h,m,v,n,r,f)),w}function i(t,e){return"00-"+e+"-"+t+"-01"}function o(t,e,n,r,i){var o=0,a="",s=1,c="",f="";return i+"@nr="+o+"-"+s+"-"+n+"-"+r+"-"+t+"-"+a+"-"+c+"-"+f+"-"+e}function a(t,e,n,r,i,o){var a="btoa"in window&&"function"==typeof window.btoa;if(!a)return null;var s={v:[0,1],d:{ty:"Browser",ac:r,ap:i,id:t,tr:e,ti:n}};return o&&r!==o&&(s.d.tk=o),btoa(JSON.stringify(s))}function s(t){return f()&&c(t)}function c(t){var e=!1,n={};if("init"in NREUM&&"distributed_tracing"in NREUM.init&&(n=NREUM.init.distributed_tracing),t.sameOrigin)e=!0;else if(n.allowed_origins instanceof Array)for(var r=0;r<n.allowed_origins.length;r++){var i=h(n.allowed_origins[r]);if(t.hostname===i.hostname&&t.protocol===i.protocol&&t.port===i.port){e=!0;break}}return e}function f(){return"init"in NREUM&&"distributed_tracing"in NREUM.init&&!!NREUM.init.distributed_tracing.enabled}function u(){return"init"in NREUM&&"distributed_tracing"in NREUM.init&&!!NREUM.init.distributed_tracing.exclude_newrelic_header}function d(){return"init"in NREUM&&"distributed_tracing"in NREUM.init&&NREUM.init.distributed_tracing.cors_use_newrelic_header!==!1}function l(){return"init"in NREUM&&"distributed_tracing"in NREUM.init&&!!NREUM.init.distributed_tracing.cors_use_tracecontext_headers}var p=t(25),h=t(14);e.exports={generateTracePayload:r,shouldGenerateTrace:s}},{}],13:[function(t,e,n){function r(t){var e=this.params,n=this.metrics;if(!this.ended){this.ended=!0;for(var r=0;r<l;r++)t.removeEventListener(d[r],this.listener,!1);return e.protocol&&"data"===e.protocol?void g("Ajax/DataUrl/Excluded"):void(e.aborted||(n.duration=a.now()-this.startTime,this.loadCaptureCalled||4!==t.readyState?null==e.status&&(e.status=0):o(this,t),n.cbTime=this.cbTime,s("xhr",[e,n,this.startTime,this.endTime,"xhr"],this)))}}function i(t,e){var n=c(e),r=t.params;r.hostname=n.hostname,r.port=n.port,r.protocol=n.protocol,r.host=n.hostname+":"+n.port,r.pathname=n.pathname,t.parsedOrigin=n,t.sameOrigin=n.sameOrigin}function o(t,e){t.params.status=e.status;var n=v(e,t.lastSize);if(n&&(t.metrics.rxSize=n),t.sameOrigin){var r=e.getResponseHeader("X-NewRelic-App-Data");r&&(t.params.cat=r.split(", ").pop())}t.loadCaptureCalled=!0}var a=t("loader");if(a.xhrWrappable&&!a.disabled){var s=t("handle"),c=t(14),f=t(12).generateTracePayload,u=t("ee"),d=["load","error","abort","timeout"],l=d.length,p=t("id"),h=t(20),m=t(18),v=t(15),w=t(19),g=t(21).recordSupportability,y=NREUM.o.REQ,x=window.XMLHttpRequest;a.features.xhr=!0,t(11),t(7),u.on("new-xhr",function(t){var e=this;e.totalCbs=0,e.called=0,e.cbTime=0,e.end=r,e.ended=!1,e.xhrGuids={},e.lastSize=null,e.loadCaptureCalled=!1,e.params=this.params||{},e.metrics=this.metrics||{},t.addEventListener("load",function(n){o(e,t)},w(!1)),h&&(h>34||h<10)||t.addEventListener("progress",function(t){e.lastSize=t.loaded},w(!1))}),u.on("open-xhr-start",function(t){this.params={method:t[0]},i(this,t[1]),this.metrics={}}),u.on("open-xhr-end",function(t,e){"loader_config"in NREUM&&"xpid"in NREUM.loader_config&&this.sameOrigin&&e.setRequestHeader("X-NewRelic-ID",NREUM.loader_config.xpid);var n=f(this.parsedOrigin);if(n){var r=!1;n.newrelicHeader&&(e.setRequestHeader("newrelic",n.newrelicHeader),r=!0),n.traceContextParentHeader&&(e.setRequestHeader("traceparent",n.traceContextParentHeader),n.traceContextStateHeader&&e.setRequestHeader("tracestate",n.traceContextStateHeader),r=!0),r&&(this.dt=n)}}),u.on("send-xhr-start",function(t,e){var n=this.metrics,r=t[0],i=this;if(n&&r){var o=m(r);o&&(n.txSize=o)}this.startTime=a.now(),this.listener=function(t){try{"abort"!==t.type||i.loadCaptureCalled||(i.params.aborted=!0),("load"!==t.type||i.called===i.totalCbs&&(i.onloadCalled||"function"!=typeof e.onload))&&i.end(e)}catch(n){try{u.emit("internal-error",[n])}catch(r){}}};for(var s=0;s<l;s++)e.addEventListener(d[s],this.listener,w(!1))}),u.on("xhr-cb-time",function(t,e,n){this.cbTime+=t,e?this.onloadCalled=!0:this.called+=1,this.called!==this.totalCbs||!this.onloadCalled&&"function"==typeof n.onload||this.end(n)}),u.on("xhr-load-added",function(t,e){var n=""+p(t)+!!e;this.xhrGuids&&!this.xhrGuids[n]&&(this.xhrGuids[n]=!0,this.totalCbs+=1)}),u.on("xhr-load-removed",function(t,e){var n=""+p(t)+!!e;this.xhrGuids&&this.xhrGuids[n]&&(delete this.xhrGuids[n],this.totalCbs-=1)}),u.on("xhr-resolved",function(){this.endTime=a.now()}),u.on("addEventListener-end",function(t,e){e instanceof x&&"load"===t[0]&&u.emit("xhr-load-added",[t[1],t[2]],e)}),u.on("removeEventListener-end",function(t,e){e instanceof x&&"load"===t[0]&&u.emit("xhr-load-removed",[t[1],t[2]],e)}),u.on("fn-start",function(t,e,n){e instanceof x&&("onload"===n&&(this.onload=!0),("load"===(t[0]&&t[0].type)||this.onload)&&(this.xhrCbStart=a.now()))}),u.on("fn-end",function(t,e){this.xhrCbStart&&u.emit("xhr-cb-time",[a.now()-this.xhrCbStart,this.onload,e],e)}),u.on("fetch-before-start",function(t){function e(t,e){var n=!1;return e.newrelicHeader&&(t.set("newrelic",e.newrelicHeader),n=!0),e.traceContextParentHeader&&(t.set("traceparent",e.traceContextParentHeader),e.traceContextStateHeader&&t.set("tracestate",e.traceContextStateHeader),n=!0),n}var n,r=t[1]||{};"string"==typeof t[0]?n=t[0]:t[0]&&t[0].url?n=t[0].url:window.URL&&t[0]&&t[0]instanceof URL&&(n=t[0].href),n&&(this.parsedOrigin=c(n),this.sameOrigin=this.parsedOrigin.sameOrigin);var i=f(this.parsedOrigin);if(i&&(i.newrelicHeader||i.traceContextParentHeader))if("string"==typeof t[0]||window.URL&&t[0]&&t[0]instanceof URL){var o={};for(var a in r)o[a]=r[a];o.headers=new Headers(r.headers||{}),e(o.headers,i)&&(this.dt=i),t.length>1?t[1]=o:t.push(o)}else t[0]&&t[0].headers&&e(t[0].headers,i)&&(this.dt=i)}),u.on("fetch-start",function(t,e){this.params={},this.metrics={},this.startTime=a.now(),this.dt=e,t.length>=1&&(this.target=t[0]),t.length>=2&&(this.opts=t[1]);var n,r=this.opts||{},o=this.target;if("string"==typeof o?n=o:"object"==typeof o&&o instanceof y?n=o.url:window.URL&&"object"==typeof o&&o instanceof URL&&(n=o.href),i(this,n),"data"!==this.params.protocol){var s=(""+(o&&o instanceof y&&o.method||r.method||"GET")).toUpperCase();this.params.method=s,this.txSize=m(r.body)||0}}),u.on("fetch-done",function(t,e){if(this.endTime=a.now(),this.params||(this.params={}),"data"===this.params.protocol)return void g("Ajax/DataUrl/Excluded");this.params.status=e?e.status:0;var n;"string"==typeof this.rxSize&&this.rxSize.length>0&&(n=+this.rxSize);var r={txSize:this.txSize,rxSize:n,duration:a.now()-this.startTime};s("xhr",[this.params,r,this.startTime,this.endTime,"fetch"],this)})}},{}],14:[function(t,e,n){var r={};e.exports=function(t){if(t in r)return r[t];if(0===(t||"").indexOf("data:"))return{protocol:"data"};var e=document.createElement("a"),n=window.location,i={};e.href=t,i.port=e.port;var o=e.href.split("://");!i.port&&o[1]&&(i.port=o[1].split("/")[0].split("@").pop().split(":")[1]),i.port&&"0"!==i.port||(i.port="https"===o[0]?"443":"80"),i.hostname=e.hostname||n.hostname,i.pathname=e.pathname,i.protocol=o[0],"/"!==i.pathname.charAt(0)&&(i.pathname="/"+i.pathname);var a=!e.protocol||":"===e.protocol||e.protocol===n.protocol,s=e.hostname===document.domain&&e.port===n.port;return i.sameOrigin=a&&(!e.hostname||s),"/"===i.pathname&&(r[t]=i),i}},{}],15:[function(t,e,n){function r(t,e){var n=t.responseType;return"json"===n&&null!==e?e:"arraybuffer"===n||"blob"===n||"json"===n?i(t.response):"text"===n||""===n||void 0===n?i(t.responseText):void 0}var i=t(18);e.exports=r},{}],16:[function(t,e,n){function r(){}function i(t,e,n,r){return function(){return u.recordSupportability("API/"+e+"/called"),o(t+e,[f.now()].concat(s(arguments)),n?null:this,r),n?void 0:this}}var o=t("handle"),a=t(28),s=t(29),c=t("ee").get("tracer"),f=t("loader"),u=t(21),d=NREUM;"undefined"==typeof window.newrelic&&(newrelic=d);var l=["setPageViewName","setCustomAttribute","setErrorHandler","finished","addToTrace","inlineHit","addRelease"],p="api-",h=p+"ixn-";a(l,function(t,e){d[e]=i(p,e,!0,"api")}),d.addPageAction=i(p,"addPageAction",!0),d.setCurrentRouteName=i(p,"routeName",!0),e.exports=newrelic,d.interaction=function(){return(new r).get()};var m=r.prototype={createTracer:function(t,e){var n={},r=this,i="function"==typeof e;return o(h+"tracer",[f.now(),t,n],r),function(){if(c.emit((i?"":"no-")+"fn-start",[f.now(),r,i],n),i)try{return e.apply(this,arguments)}catch(t){throw c.emit("fn-err",[arguments,this,t],n),t}finally{c.emit("fn-end",[f.now()],n)}}}};a("actionText,setName,setAttribute,save,ignore,onEnd,getContext,end,get".split(","),function(t,e){m[e]=i(h,e)}),newrelic.noticeError=function(t,e){"string"==typeof t&&(t=new Error(t)),u.recordSupportability("API/noticeError/called"),o("err",[t,f.now(),!1,e])}},{}],17:[function(t,e,n){function r(t){if(NREUM.init){for(var e=NREUM.init,n=t.split("."),r=0;r<n.length-1;r++)if(e=e[n[r]],"object"!=typeof e)return;return e=e[n[n.length-1]]}}e.exports={getConfiguration:r}},{}],18:[function(t,e,n){e.exports=function(t){if("string"==typeof t&&t.length)return t.length;if("object"==typeof t){if("undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer&&t.byteLength)return t.byteLength;if("undefined"!=typeof Blob&&t instanceof Blob&&t.size)return t.size;if(!("undefined"!=typeof FormData&&t instanceof FormData))try{return JSON.stringify(t).length}catch(e){return}}}},{}],19:[function(t,e,n){var r=!1;try{var i=Object.defineProperty({},"passive",{get:function(){r=!0}});window.addEventListener("testPassive",null,i),window.removeEventListener("testPassive",null,i)}catch(o){}e.exports=function(t){return r?{passive:!0,capture:!!t}:!!t}},{}],20:[function(t,e,n){var r=0,i=navigator.userAgent.match(/Firefox[\/\s](\d+\.\d+)/);i&&(r=+i[1]),e.exports=r},{}],21:[function(t,e,n){function r(t,e){var n=[a,t,{name:t},e];return o("storeMetric",n,null,"api"),n}function i(t,e){var n=[s,t,{name:t},e];return o("storeEventMetrics",n,null,"api"),n}var o=t("handle"),a="sm",s="cm";e.exports={constants:{SUPPORTABILITY_METRIC:a,CUSTOM_METRIC:s},recordSupportability:r,recordCustom:i}},{}],22:[function(t,e,n){function r(){return s.exists&&performance.now?Math.round(performance.now()):(o=Math.max((new Date).getTime(),o))-a}function i(){return o}var o=(new Date).getTime(),a=o,s=t(30);e.exports=r,e.exports.offset=a,e.exports.getLastTimestamp=i},{}],23:[function(t,e,n){function r(t){return!(!t||!t.protocol||"file:"===t.protocol)}e.exports=r},{}],24:[function(t,e,n){function r(t,e){var n=t.getEntries();n.forEach(function(t){"first-paint"===t.name?p("timing",["fp",Math.floor(t.startTime)]):"first-contentful-paint"===t.name&&p("timing",["fcp",Math.floor(t.startTime)])})}function i(t,e){var n=t.getEntries();if(n.length>0){var r=n[n.length-1];if(f&&f<r.startTime)return;var i=[r],o=a({});o&&i.push(o),p("lcp",i)}}function o(t){t.getEntries().forEach(function(t){t.hadRecentInput||p("cls",[t])})}function a(t){var e=navigator.connection||navigator.mozConnection||navigator.webkitConnection;if(e)return e.type&&(t["net-type"]=e.type),e.effectiveType&&(t["net-etype"]=e.effectiveType),e.rtt&&(t["net-rtt"]=e.rtt),e.downlink&&(t["net-dlink"]=e.downlink),t}function s(t){if(t instanceof w&&!y){var e=Math.round(t.timeStamp),n={type:t.type};a(n),e<=h.now()?n.fid=h.now()-e:e>h.offset&&e<=Date.now()?(e-=h.offset,n.fid=h.now()-e):e=h.now(),y=!0,p("timing",["fi",e,n])}}function c(t){"hidden"===t&&(f=h.now(),p("pageHide",[f]))}if(!("init"in NREUM&&"page_view_timing"in NREUM.init&&"enabled"in NREUM.init.page_view_timing&&NREUM.init.page_view_timing.enabled===!1)){var f,u,d,l,p=t("handle"),h=t("loader"),m=t(27),v=t(19),w=NREUM.o.EV;if("PerformanceObserver"in window&&"function"==typeof window.PerformanceObserver){u=new PerformanceObserver(r);try{u.observe({entryTypes:["paint"]})}catch(g){}d=new PerformanceObserver(i);try{d.observe({entryTypes:["largest-contentful-paint"]})}catch(g){}l=new PerformanceObserver(o);try{l.observe({type:"layout-shift",buffered:!0})}catch(g){}}if("addEventListener"in document){var y=!1,x=["click","keydown","mousedown","pointerdown","touchstart"];x.forEach(function(t){document.addEventListener(t,s,v(!1))})}m(c)}},{}],25:[function(t,e,n){function r(){function t(){return e?15&e[n++]:16*Math.random()|0}var e=null,n=0,r=window.crypto||window.msCrypto;r&&r.getRandomValues&&(e=r.getRandomValues(new Uint8Array(31)));for(var i,o="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx",a="",s=0;s<o.length;s++)i=o[s],"x"===i?a+=t().toString(16):"y"===i?(i=3&t()|8,a+=i.toString(16)):a+=i;return a}function i(){return a(16)}function o(){return a(32)}function a(t){function e(){return n?15&n[r++]:16*Math.random()|0}var n=null,r=0,i=window.crypto||window.msCrypto;i&&i.getRandomValues&&Uint8Array&&(n=i.getRandomValues(new Uint8Array(31)));for(var o=[],a=0;a<t;a++)o.push(e().toString(16));return o.join("")}e.exports={generateUuid:r,generateSpanId:i,generateTraceId:o}},{}],26:[function(t,e,n){function r(t,e){if(!i)return!1;if(t!==i)return!1;if(!e)return!0;if(!o)return!1;for(var n=o.split("."),r=e.split("."),a=0;a<r.length;a++)if(r[a]!==n[a])return!1;return!0}var i=null,o=null,a=/Version\/(\S+)\s+Safari/;if(navigator.userAgent){var s=navigator.userAgent,c=s.match(a);c&&s.indexOf("Chrome")===-1&&s.indexOf("Chromium")===-1&&(i="Safari",o=c[1])}e.exports={agent:i,version:o,match:r}},{}],27:[function(t,e,n){function r(t){function e(){t(s&&document[s]?document[s]:document[o]?"hidden":"visible")}"addEventListener"in document&&a&&document.addEventListener(a,e,i(!1))}var i=t(19);e.exports=r;var o,a,s;"undefined"!=typeof document.hidden?(o="hidden",a="visibilitychange",s="visibilityState"):"undefined"!=typeof document.msHidden?(o="msHidden",a="msvisibilitychange"):"undefined"!=typeof document.webkitHidden&&(o="webkitHidden",a="webkitvisibilitychange",s="webkitVisibilityState")},{}],28:[function(t,e,n){function r(t,e){var n=[],r="",o=0;for(r in t)i.call(t,r)&&(n[o]=e(r,t[r]),o+=1);return n}var i=Object.prototype.hasOwnProperty;e.exports=r},{}],29:[function(t,e,n){function r(t,e,n){e||(e=0),"undefined"==typeof n&&(n=t?t.length:0);for(var r=-1,i=n-e||0,o=Array(i<0?0:i);++r<i;)o[r]=t[e+r];return o}e.exports=r},{}],30:[function(t,e,n){e.exports={exists:"undefined"!=typeof window.performance&&window.performance.timing&&"undefined"!=typeof window.performance.timing.navigationStart}},{}],ee:[function(t,e,n){function r(){}function i(t){function e(t){return t&&t instanceof r?t:t?f(t,c,a):a()}function n(n,r,i,o,a){if(a!==!1&&(a=!0),!p.aborted||o){t&&a&&t(n,r,i);for(var s=e(i),c=m(n),f=c.length,u=0;u<f;u++)c[u].apply(s,r);var l=d[y[n]];return l&&l.push([x,n,r,s]),s}}function o(t,e){g[t]=m(t).concat(e)}function h(t,e){var n=g[t];if(n)for(var r=0;r<n.length;r++)n[r]===e&&n.splice(r,1)}function m(t){return g[t]||[]}function v(t){return l[t]=l[t]||i(n)}function w(t,e){p.aborted||u(t,function(t,n){e=e||"feature",y[n]=e,e in d||(d[e]=[])})}var g={},y={},x={on:o,addEventListener:o,removeEventListener:h,emit:n,get:v,listeners:m,context:e,buffer:w,abort:s,aborted:!1};return x}function o(t){return f(t,c,a)}function a(){return new r}function s(){(d.api||d.feature)&&(p.aborted=!0,d=p.backlog={})}var c="nr@context",f=t("gos"),u=t(28),d={},l={},p=e.exports=i();e.exports.getOrSetContext=o,p.backlog=d},{}],gos:[function(t,e,n){function r(t,e,n){if(i.call(t,e))return t[e];var r=n();if(Object.defineProperty&&Object.keys)try{return Object.defineProperty(t,e,{value:r,writable:!0,enumerable:!1}),r}catch(o){}return t[e]=r,r}var i=Object.prototype.hasOwnProperty;e.exports=r},{}],handle:[function(t,e,n){function r(t,e,n,r){i.buffer([t],r),i.emit(t,e,n)}var i=t("ee").get("handle");e.exports=r,r.ee=i},{}],id:[function(t,e,n){function r(t){var e=typeof t;return!t||"object"!==e&&"function"!==e?-1:t===window?0:a(t,o,function(){return i++})}var i=1,o="nr@id",a=t("gos");e.exports=r},{}],loader:[function(t,e,n){function r(){if(!T++){var t=O.info=NREUM.info,e=v.getElementsByTagName("script")[0];if(setTimeout(f.abort,3e4),!(t&&t.licenseKey&&t.applicationID&&e))return f.abort();c(R,function(e,n){t[e]||(t[e]=n)});var n=a();s("mark",["onload",n+O.offset],null,"api"),s("timing",["load",n]);var r=v.createElement("script");0===t.agent.indexOf("http://")||0===t.agent.indexOf("https://")?r.src=t.agent:r.src=h+"://"+t.agent,e.parentNode.insertBefore(r,e)}}function i(){"complete"===v.readyState&&o()}function o(){s("mark",["domContent",a()+O.offset],null,"api")}var a=t(22),s=t("handle"),c=t(28),f=t("ee"),u=t(26),d=t(23),l=t(17),p=t(19),h=l.getConfiguration("ssl")===!1?"http":"https",m=window,v=m.document,w="addEventListener",g="attachEvent",y=m.XMLHttpRequest,x=y&&y.prototype,b=!d(m.location);NREUM.o={ST:setTimeout,SI:m.setImmediate,CT:clearTimeout,XHR:y,REQ:m.Request,EV:m.Event,PR:m.Promise,MO:m.MutationObserver};var E=""+location,R={beacon:"bam.nr-data.net",errorBeacon:"bam.nr-data.net",agent:"js-agent.newrelic.com/nr-1215.min.js"},S=y&&x&&x[w]&&!/CriOS/.test(navigator.userAgent),O=e.exports={offset:a.getLastTimestamp(),now:a,origin:E,features:{},xhrWrappable:S,userAgent:u,disabled:b};if(!b){t(16),t(24),v[w]?(v[w]("DOMContentLoaded",o,p(!1)),m[w]("load",r,p(!1))):(v[g]("onreadystatechange",i),m[g]("onload",r)),s("mark",["firstbyte",a.getLastTimestamp()],null,"api");var T=0}},{}],"wrap-function":[function(t,e,n){function r(t,e){function n(e,n,r,c,f){function nrWrapper(){var o,a,u,l;try{a=this,o=d(arguments),u="function"==typeof r?r(o,a):r||{}}catch(p){i([p,"",[o,a,c],u],t)}s(n+"start",[o,a,c],u,f);try{return l=e.apply(a,o)}catch(h){throw s(n+"err",[o,a,h],u,f),h}finally{s(n+"end",[o,a,l],u,f)}}return a(e)?e:(n||(n=""),nrWrapper[l]=e,o(e,nrWrapper,t),nrWrapper)}function r(t,e,r,i,o){r||(r="");var s,c,f,u="-"===r.charAt(0);for(f=0;f<e.length;f++)c=e[f],s=t[c],a(s)||(t[c]=n(s,u?c+r:r,i,c,o))}function s(n,r,o,a){if(!h||e){var s=h;h=!0;try{t.emit(n,r,o,e,a)}catch(c){i([c,n,r,o],t)}h=s}}return t||(t=u),n.inPlace=r,n.flag=l,n}function i(t,e){e||(e=u);try{e.emit("internal-error",t)}catch(n){}}function o(t,e,n){if(Object.defineProperty&&Object.keys)try{var r=Object.keys(t);return r.forEach(function(n){Object.defineProperty(e,n,{get:function(){return t[n]},set:function(e){return t[n]=e,e}})}),e}catch(o){i([o],n)}for(var a in t)p.call(t,a)&&(e[a]=t[a]);return e}function a(t){return!(t&&t instanceof Function&&t.apply&&!t[l])}function s(t,e){var n=e(t);return n[l]=t,o(t,n,u),n}function c(t,e,n){var r=t[e];t[e]=s(r,n)}function f(){for(var t=arguments.length,e=new Array(t),n=0;n<t;++n)e[n]=arguments[n];return e}var u=t("ee"),d=t(29),l="nr@original",p=Object.prototype.hasOwnProperty,h=!1;e.exports=r,e.exports.wrapFunction=s,e.exports.wrapInPlace=c,e.exports.argsToArray=f},{}]},{},["loader",2,13,4,3]);</script>
  <meta name="theme-color" content="#FFFFFF">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0">
    <meta name="referrer" content="origin-when-cross-origin">
  <meta name="facebook-domain-verification" content="14yui0t6bj9n8uz4d48a2snpt3cvnz">

  <link rel="preload" href="https://cdn.dribbble.com/assets/neue-haas-grotesk/NeueHaasGrotTextRound-55Roman-Web-9e7322596eec47d8e79e2453d75a77fb58045ae944ec818e4fc1ba05559121a4.woff2" as="font" crossorigin="anonymous">
  <link rel="preload" href="https://cdn.dribbble.com/assets/neue-haas-grotesk/NeueHaasGrotTextRound-65Medium-Web-741c8c70e5c90808119c701483ca7362baff5ae482c823718406bbb1e31d9143.woff2" as="font" crossorigin="anonymous">
  <link rel="preload" href="https://cdn.dribbble.com/assets/neue-haas-grotesk/NeueHaasGrotTextRound-75Bold-Web-f2ff2f34217e3fdab82c30ef689f5be3fb86a8e4218a1906a9399d0354a95d45.woff2" as="font" crossorigin="anonymous">

  <!--[if gte IE 7]><!-->
        <link rel="stylesheet" href="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/mini-master-async-8912b62d0bdfaf3bbbb3e3b8f675e0cd0f556aedd41e8f559e228f5ff637d7f1.css" media="all" onload="this.media=&#39;all&#39;">
  <noscript><link rel="stylesheet" href="https://cdn.dribbble.com/assets/mini-master-async-8912b62d0bdfaf3bbbb3e3b8f675e0cd0f556aedd41e8f559e228f5ff637d7f1.css"></noscript>



  <link rel="stylesheet" media="screen" href="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/index-194470dfba9b8b4badd798e54a502f46b737e49fdab547bfcf79e70651c686dd.css">

  <!-- <![endif]-->
  <link href="https://cdn.dribbble.com/assets/dribbble-vector-ball-f320a6ba48a4ccf416ef6e396e204c899874565b694593834b6c23f978394498.svg" rel="mask-icon" color="#ea4c89">
<link href="https://cdn.dribbble.com/assets/apple-touch-icon-precomposed-1c6d9b0a173f5b2d5c392ea101bb206ee9a2a39bef19eb21513b857eeb3624d2.png" rel="apple-touch-icon-precomposed">
<link href="https://cdn.dribbble.com/assets/favicon-b38525134603b9513174ec887944bde1a869eb6cd414f4d640ee48ab2a15a26b.ico" rel="icon">
<link href="https://cdn.dribbble.com/assets/dribbble-ball-192-23ecbdf987832231e87c642bb25de821af1ba6734a626c8c259a20a0ca51a247.png" sizes="192x192" rel="icon">

  
  <meta name="csrf-param" content="authenticity_token">
<meta name="csrf-token" content="L8A6FZwmkcWS0CJ1VejyIPKXuzxqnqDm2/+cwRwtvqGQxgyHx/pwE4emn5Mg/jTP6fwd2jYdKGEs6/r1ic8ILQ==">
    <link rel="canonical" href="https://dribbble.com/tags/find%20nearby%20places">
    <link rel="preload" as="image" imagesrcset="https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w" imagesizes="(max-width: 500px) 320px, (min-width: 501px) and (max-width: 615px) 600px, 400px">
    <link rel="preload" as="image" imagesrcset="https://cdn.dribbble.com/users/1624253/screenshots/16403200/media/2c84c15cd0d1b03fce487249c415aa35.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/1624253/screenshots/16403200/media/2c84c15cd0d1b03fce487249c415aa35.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/1624253/screenshots/16403200/media/2c84c15cd0d1b03fce487249c415aa35.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/1624253/screenshots/16403200/media/2c84c15cd0d1b03fce487249c415aa35.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/1624253/screenshots/16403200/media/2c84c15cd0d1b03fce487249c415aa35.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/1624253/screenshots/16403200/media/2c84c15cd0d1b03fce487249c415aa35.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/1624253/screenshots/16403200/media/2c84c15cd0d1b03fce487249c415aa35.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/1624253/screenshots/16403200/media/2c84c15cd0d1b03fce487249c415aa35.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/1624253/screenshots/16403200/media/2c84c15cd0d1b03fce487249c415aa35.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w" imagesizes="(max-width: 500px) 320px, (min-width: 501px) and (max-width: 615px) 600px, 400px">


  <meta property="fb:app_id" content="811749452338490">

  <script>
    if (location.hash.match(/^#\./) && window.history) {
      window.history.replaceState({}, window.title, location.origin + location.pathname + location.search)
    }
  </script>
  <noscript>
    <style>ol.dribbbles { display: block !important }</style>
  </noscript>
  <meta name="itly-controller" content="tags">
<meta name="itly-action" content="show">


    <script src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/client" async="" defer=""></script>
<script charset="utf-8" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/130-4b42117cbbd6e28b56b5.chunk.js"></script><script charset="utf-8" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/134-b959e2c658a07891cbf9.chunk.js"></script><script charset="utf-8" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/113-dfad94aa8a5caf88cfa5.chunk.js"></script><script charset="utf-8" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/137-16b88f586e4b513e4a03.chunk.js"></script><link id="googleidentityservice" type="text/css" media="all" rel="stylesheet" href="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/style"><style id="googleidentityservice_button_styles">.qJTHM{-webkit-user-select:none;color:#202124;direction:ltr;-webkit-touch-callout:none;font-family:'Roboto-Regular',arial,sans-serif;-webkit-font-smoothing:antialiased;font-weight:400;margin:0;overflow:hidden;-webkit-text-size-adjust:100%}.ynRLnc{left:-9999px;position:absolute;top:-9999px}.L6cTce{display:none}.bltWBb{word-break:break-all}.hSRGPd{color:#1a73e8;cursor:pointer;font-weight:500;text-decoration:none}.Bz112c-W3lGp{height:16px;width:16px}.Bz112c-E3DyYd{height:20px;width:20px}.Bz112c-r9oPif{height:24px;width:24px}.Bz112c-uaxL4e{-webkit-border-radius:10px;border-radius:10px}.LgbsSe-Bz112c{display:block}.S9gUrf-YoZ4jf,.S9gUrf-YoZ4jf *{border:none;margin:0;padding:0}.fFW7wc-ibnC6b>.aZ2wEe>div{border-color:#4285f4}.P1ekSe-ZMv3u>div:nth-child(1){background-color:#1a73e8!important}.P1ekSe-ZMv3u>div:nth-child(2),.P1ekSe-ZMv3u>div:nth-child(3){background-image:linear-gradient(to right,rgba(255,255,255,0.7),rgba(255,255,255,0.7)),linear-gradient(to right,#1a73e8,#1a73e8)!important}.haAclf{display:inline-block}.nsm7Bb-HzV7m-LgbsSe{-webkit-border-radius:4px;border-radius:4px;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-transition:background-color .218s,border-color .218s;transition:background-color .218s,border-color .218s;-webkit-user-select:none;-webkit-appearance:none;background-color:#fff;background-image:none;border:1px solid #dadce0;color:#3c4043;cursor:pointer;font-family:'Google Sans',arial,sans-serif;font-size:14px;height:40px;letter-spacing:0.25px;outline:none;overflow:hidden;padding:0 12px;position:relative;text-align:center;vertical-align:middle;white-space:nowrap;width:auto}@media screen and (-ms-high-contrast:active){.nsm7Bb-HzV7m-LgbsSe{border:2px solid windowText;color:windowText}}.nsm7Bb-HzV7m-LgbsSe.pSzOP-SxQuSe{font-size:14px;height:32px;letter-spacing:0.25px;padding:0 10px}.nsm7Bb-HzV7m-LgbsSe.purZT-SxQuSe{font-size:11px;height:20px;letter-spacing:0.3px;padding:0 8px}.nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe{padding:0;width:40px}.nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe.pSzOP-SxQuSe{width:32px}.nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe.purZT-SxQuSe{width:20px}.nsm7Bb-HzV7m-LgbsSe.JGcpL-RbRzK{-webkit-border-radius:20px;border-radius:20px}.nsm7Bb-HzV7m-LgbsSe.JGcpL-RbRzK.pSzOP-SxQuSe{-webkit-border-radius:16px;border-radius:16px}.nsm7Bb-HzV7m-LgbsSe.JGcpL-RbRzK.purZT-SxQuSe{-webkit-border-radius:10px;border-radius:10px}.nsm7Bb-HzV7m-LgbsSe.MFS4be-Ia7Qfc{border:none;color:#fff}.nsm7Bb-HzV7m-LgbsSe.MFS4be-v3pZbf-Ia7Qfc{background-color:#1a73e8}.nsm7Bb-HzV7m-LgbsSe.MFS4be-JaPV2b-Ia7Qfc{background-color:#202124;color:#e8eaed}.nsm7Bb-HzV7m-LgbsSe .nsm7Bb-HzV7m-LgbsSe-Bz112c{height:18px;margin-right:8px;min-width:18px;width:18px}.nsm7Bb-HzV7m-LgbsSe.pSzOP-SxQuSe .nsm7Bb-HzV7m-LgbsSe-Bz112c{height:14px;min-width:14px;width:14px}.nsm7Bb-HzV7m-LgbsSe.purZT-SxQuSe .nsm7Bb-HzV7m-LgbsSe-Bz112c{height:10px;min-width:10px;width:10px}.nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-Bz112c{margin-left:8px;margin-right:-4px}.nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe .nsm7Bb-HzV7m-LgbsSe-Bz112c{margin:0;padding:10px}.nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe.pSzOP-SxQuSe .nsm7Bb-HzV7m-LgbsSe-Bz112c{padding:8px}.nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe.purZT-SxQuSe .nsm7Bb-HzV7m-LgbsSe-Bz112c{padding:4px}.nsm7Bb-HzV7m-LgbsSe .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf{-webkit-border-top-left-radius:3px;border-top-left-radius:3px;-webkit-border-bottom-left-radius:3px;border-bottom-left-radius:3px;display:-webkit-box;display:-webkit-flex;display:flex;justify-content:center;-webkit-align-items:center;align-items:center;background-color:#fff;height:36px;margin-left:-10px;margin-right:12px;min-width:36px;width:36px}.nsm7Bb-HzV7m-LgbsSe .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf .nsm7Bb-HzV7m-LgbsSe-Bz112c,.nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf .nsm7Bb-HzV7m-LgbsSe-Bz112c{margin:0;padding:0}.nsm7Bb-HzV7m-LgbsSe.pSzOP-SxQuSe .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf{height:28px;margin-left:-8px;margin-right:10px;min-width:28px;width:28px}.nsm7Bb-HzV7m-LgbsSe.purZT-SxQuSe .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf{height:16px;margin-left:-6px;margin-right:8px;min-width:16px;width:16px}.nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf{-webkit-border-radius:3px;border-radius:3px;margin-left:2px;margin-right:0;padding:0}.nsm7Bb-HzV7m-LgbsSe.JGcpL-RbRzK .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf{-webkit-border-radius:18px;border-radius:18px}.nsm7Bb-HzV7m-LgbsSe.pSzOP-SxQuSe.JGcpL-RbRzK .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf{-webkit-border-radius:14px;border-radius:14px}.nsm7Bb-HzV7m-LgbsSe.purZT-SxQuSe.JGcpL-RbRzK .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf{-webkit-border-radius:8px;border-radius:8px}.nsm7Bb-HzV7m-LgbsSe .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-align-items:center;align-items:center;-webkit-flex-direction:row;flex-direction:row;justify-content:space-between;-webkit-flex-wrap:nowrap;flex-wrap:nowrap;height:100%;position:relative;width:100%}.nsm7Bb-HzV7m-LgbsSe .oXtfBe-l4eHX{justify-content:center}.nsm7Bb-HzV7m-LgbsSe .nsm7Bb-HzV7m-LgbsSe-BPrWId{-webkit-flex-grow:1;flex-grow:1;font-family:'Google Sans',arial,sans-serif;font-weight:500;overflow:hidden;text-overflow:ellipsis;vertical-align:top}.nsm7Bb-HzV7m-LgbsSe.purZT-SxQuSe .nsm7Bb-HzV7m-LgbsSe-BPrWId{font-weight:300}.nsm7Bb-HzV7m-LgbsSe .oXtfBe-l4eHX .nsm7Bb-HzV7m-LgbsSe-BPrWId{-webkit-flex-grow:0;flex-grow:0}.nsm7Bb-HzV7m-LgbsSe .nsm7Bb-HzV7m-LgbsSe-MJoBVe{-webkit-transition:background-color .218s;transition:background-color .218s;bottom:0;left:0;position:absolute;right:0;top:0}.nsm7Bb-HzV7m-LgbsSe:hover,.nsm7Bb-HzV7m-LgbsSe:focus{-webkit-box-shadow:none;box-shadow:none;border-color:#d2e3fc;outline:none}.nsm7Bb-HzV7m-LgbsSe:hover .nsm7Bb-HzV7m-LgbsSe-MJoBVe,.nsm7Bb-HzV7m-LgbsSe:focus .nsm7Bb-HzV7m-LgbsSe-MJoBVe{background:rgba(66,133,244,0.04)}.nsm7Bb-HzV7m-LgbsSe:active .nsm7Bb-HzV7m-LgbsSe-MJoBVe{background:rgba(66,133,244,0.1)}.nsm7Bb-HzV7m-LgbsSe.MFS4be-Ia7Qfc:hover .nsm7Bb-HzV7m-LgbsSe-MJoBVe,.nsm7Bb-HzV7m-LgbsSe.MFS4be-Ia7Qfc:focus .nsm7Bb-HzV7m-LgbsSe-MJoBVe{background:rgba(255,255,255,0.24)}.nsm7Bb-HzV7m-LgbsSe.MFS4be-Ia7Qfc:active .nsm7Bb-HzV7m-LgbsSe-MJoBVe{background:rgba(255,255,255,0.32)}.nsm7Bb-HzV7m-LgbsSe .n1UuX-DkfjY{-webkit-border-radius:50%;border-radius:50%;display:-webkit-box;display:-webkit-flex;display:flex;height:20px;margin-left:-4px;margin-right:8px;min-width:20px;width:20px}.nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-BPrWId{font-family:'Roboto';font-size:12px;text-align:left}.nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-BPrWId .ssJRIf,.nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-BPrWId .K4efff .fmcmS{overflow:hidden;text-overflow:ellipsis}.nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-BPrWId .K4efff{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-align-items:center;align-items:center;color:#5f6368;fill:#5f6368;font-size:11px;font-weight:400}.nsm7Bb-HzV7m-LgbsSe.jVeSEe.MFS4be-Ia7Qfc .nsm7Bb-HzV7m-LgbsSe-BPrWId .K4efff{color:#e8eaed;fill:#e8eaed}.nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-BPrWId .K4efff .Bz112c{height:18px;margin:-3px -3px -3px 2px;min-width:18px;width:18px}.nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf{-webkit-border-top-left-radius:0;border-top-left-radius:0;-webkit-border-bottom-left-radius:0;border-bottom-left-radius:0;-webkit-border-top-right-radius:3px;border-top-right-radius:3px;-webkit-border-bottom-right-radius:3px;border-bottom-right-radius:3px;margin-left:12px;margin-right:-10px}.nsm7Bb-HzV7m-LgbsSe.jVeSEe.JGcpL-RbRzK .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf{-webkit-border-radius:18px;border-radius:18px}.L5Fo6c-sM5MNb{border:0;display:block;left:0;position:relative;top:0}.L5Fo6c-bF1uUb{-webkit-border-radius:4px;border-radius:4px;bottom:0;cursor:pointer;left:0;position:absolute;right:0;top:0}.L5Fo6c-bF1uUb:focus{border:none;outline:none}sentinel{}
/*# sourceURL=/_/gsi/_/ss/k=gsi.gsi.IVArq21tg7M.L.W.O/am=whU/d=1/rs=AF0KOtWieN5fFQYOaa3RmPbB3e7nPQb2KA/m=gis_client_button_style */</style><script src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/f(1).txt"></script><script charset="utf-8" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/0-e969334ee2f0f497252f.chunk.js"></script><script charset="utf-8" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/1-b26898014d68475989e9.chunk.js"></script><script charset="utf-8" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/11-a5ef92b21f89aa6b131c.chunk.js"></script><script charset="utf-8" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/112-cbe66cd57fb5bbc88b74.chunk.js"></script><link rel="stylesheet" type="text/css" href="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/95-52b2de91.chunk.css"><script charset="utf-8" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/95-5d9107ad04c0b819c57d.chunk.js"></script></head>

<body id="tags-show" class="logged-out not-pro not-player not-self not-team not-on-team noscroll"><div class="new-hover-card" style="display: none;"></div><div class="js-toasty-sign-up-container"><div class="toasty-sign-up js-toasty-sign-up js-event-text lazyloading-hidden position-fixed no-events lazyloaded" data-test="toasty-sign-up" data-text-signin="Sign up with email" data-text-twitter="Twitter Logo" data-text-google="Google Logo" data-include="css:https://cdn.dribbble.com/assets/toasty-sign-up-cfa11915d0f8c4e1f124afdbf0449d9c7fa4de9c527fe11dab893ae463de615a.css" data-currentinclude="">
  <div class="display-flex justify-space-between">
    <h4 class="font-heading-mobile-3 margin-b-12 js-event-text" data-text="Looking for a Designer?">Welcome to Dribbble</h4>
    <a class="close-btn js-dismiss" aria-label="close" href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail#">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 22 22" fill="currentColor" role="img" class="icon icon-16 fill-current">
<path d="M7.22876 5.81455C6.83824 5.42403 6.20507 5.42403 5.81455 5.81455C5.42402 6.20507 5.42402 6.83824 5.81455 7.22876L9.58578 11L5.81455 14.7712C5.42402 15.1618 5.42402 15.7949 5.81455 16.1854C6.20507 16.576 6.83824 16.576 7.22876 16.1854L11 12.4142L14.7712 16.1854C15.1618 16.576 15.7949 16.576 16.1854 16.1854C16.576 15.7949 16.576 15.1618 16.1854 14.7712L12.4142 11L16.1854 7.22876C16.576 6.83824 16.576 6.20507 16.1854 5.81455C15.7949 5.42403 15.1618 5.42403 14.7712 5.81455L11 9.58579L7.22876 5.81455Z"></path>
</svg>

    </a>
  </div>
  <p class="font-body margin-b-16">Find design inspiration. Share your work. Join the #1 creative community online.</p>
  <div class="display-flex">
    <a class="form-sub js-sign-up-btn" href="https://dribbble.com/signup/new">Sign up with email</a>
    <div class="display-flex">
      
  <form class="auth-google-form js-social-login-form" action="https://dribbble.com/auth/google_signup" accept-charset="UTF-8" method="post"><input name="utf8" type="hidden" value="✓"><input type="hidden" name="authenticity_token" value="NB3EfyM1SE17w75l2YAAjSUznBG+VkaujlnszQL6YmqLG/LteOmpm261A4OslsZiPlg69+LVzil5TYr5lxjU5g==">
    <button name="button" type="submit" class="form-btn auth-google outlined social-btn js-google-btn js-social-login-btn" data-auth-action="Sign Up">
      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none" role="img" class="icon ">
<path fill-rule="evenodd" clip-rule="evenodd" d="M17.64 9.20419C17.64 8.56601 17.5827 7.95237 17.4764 7.36328H9V10.8446H13.8436C13.635 11.9696 13.0009 12.9228 12.0477 13.561V15.8192H14.9564C16.6582 14.2524 17.64 11.9451 17.64 9.20419Z" fill="#4285F4"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.99976 18C11.4298 18 13.467 17.1941 14.9561 15.8195L12.0475 13.5613C11.2416 14.1013 10.2107 14.4204 8.99976 14.4204C6.65567 14.4204 4.67158 12.8372 3.96385 10.71H0.957031V13.0418C2.43794 15.9831 5.48158 18 8.99976 18Z" fill="#34A853"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.96409 10.7098C3.78409 10.1698 3.68182 9.59301 3.68182 8.99983C3.68182 8.40664 3.78409 7.82983 3.96409 7.28983V4.95801H0.957273C0.347727 6.17301 0 7.54755 0 8.99983C0 10.4521 0.347727 11.8266 0.957273 13.0416L3.96409 10.7098Z" fill="#FBBC05"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.99976 3.57955C10.3211 3.57955 11.5075 4.03364 12.4402 4.92545L15.0216 2.34409C13.4629 0.891818 11.4257 0 8.99976 0C5.48158 0 2.43794 2.01682 0.957031 4.95818L3.96385 7.29C4.67158 5.16273 6.65567 3.57955 8.99976 3.57955Z" fill="#EA4335"></path>
</svg>

      
</button></form>
      
<form class="js-social-login-form" action="https://dribbble.com/auth/Twitter?signup=true" accept-charset="UTF-8" method="post"><input name="utf8" type="hidden" value="✓"><input type="hidden" name="authenticity_token" value="abGV/M2vseiAMA8CcT+ipfActG8Qb7WGbGK/cTtqt+DWt6NulnNQPpVGsuQEKWRK63cSiUzsPQGbdtlFrogBbA==">
  <button name="button" type="submit" class="form-btn outlined auth-twitter auth-top twitter-btn social-btn js-twitter-btn js-social-login-btn" title="Sign in with Twitter" rel="" data-auth-action="Sign Up">
    <svg xmlns="http://www.w3.org/2000/svg" aria-labelledby="********************************" role="img" viewBox="0 0 24 24" class="icon "><title id="********************************">Twitter icon</title><path d="M23.954 4.569c-.885.389-1.83.654-2.825.775 1.014-.611 1.794-1.574 2.163-2.723-.951.555-2.005.959-3.127 1.184-.896-.959-2.173-1.559-3.591-1.559-2.717 0-4.92 2.203-4.92 4.917 0 .39.045.765.127 1.124C7.691 8.094 4.066 6.13 1.64 3.161c-.427.722-.666 1.561-.666 2.475 0 1.71.87 3.213 2.188 4.096-.807-.026-1.566-.248-2.228-.616v.061c0 2.385 1.693 4.374 3.946 4.827-.413.111-.849.171-1.296.171-.314 0-.615-.03-.916-.086.631 1.953 2.445 3.377 4.604 3.417-1.68 1.319-3.809 2.105-6.102 2.105-.39 0-.779-.023-1.17-.067 2.189 1.394 4.768 2.209 7.557 2.209 9.054 0 13.999-7.496 13.999-13.986 0-.209 0-.42-.015-.63.961-.689 1.8-1.56 2.46-2.548l-.047-.02z"></path></svg>

</button></form>
    </div>
  </div>
</div>
</div>

  <div class="psst">
  <div>
    <a href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail#" class="close" aria-label="close">
      <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon icon-16 text-white fill-current"><path d="m14.828 12 4.586-4.586c.781-.781.781-2.047 0-2.828-.78-.781-2.048-.781-2.828 0l-4.586 4.586-4.586-4.586c-.78-.781-2.048-.781-2.828 0-.781.781-.781 2.047 0 2.828l4.586 4.586-4.586 4.586c-.781.781-.781 2.047 0 2.828.39.391.902.586 1.414.586s1.024-.195 1.414-.586l4.586-4.586 4.586 4.586c.39.391.902.586 1.414.586s1.024-.195 1.414-.586c.781-.781.781-2.047 0-2.828z"></path></svg>

    </a>

      <p data-url="/announcements/3012/view">
        <span>📚LAST CHANCE! Registration is closing on March 2 for our 12 Week Product Design course. Flexible Learning. Live Mentorship. Hiring Connections. <a href="https://dribbble.com/courses/product-design?utm_campaign=announcement-3012&amp;utm_medium=banner&amp;utm_source=dribbble">Sign up now.</a></span>
      </p>
  </div>
</div>


<div class="js-site-nav site-nav lazyloading-hidden">
  <div class="site-nav-container">
    <nav class="site-nav-desktop-only align-center" aria-label="primary">
  <div data-site-nav-category="Top Nav">
    <a href="https://dribbble.com/" data-site-nav-element="Logo">
      <svg xmlns="http://www.w3.org/2000/svg" width="76" height="30" viewBox="0 0 76 19" fill="none" class="site-nav-desktop-logo fill-current">
<title>Dribbble: the community for graphic design</title>
<path fill-rule="evenodd" clip-rule="evenodd" d="M75.8822 14.657C72.7063 20.0415 67.6766 18.4791 66.166 17.2558C65.5231 17.692 64.3958 18.6481 62.8926 18.5377C59.6913 18.3027 58.5449 13.7279 58.5449 13.7279C58.5679 13.7462 57.5913 14.0649 57.0635 14.0592C57.0567 15.4008 55.897 18.6056 52.7672 18.5646C49.2551 18.5188 48.5411 13.2864 48.5411 13.2864C48.5411 13.2864 48.1916 13.7717 46.8627 14.2551C46.9296 13.2244 46.8807 18.4077 42.6713 18.4839C39.3435 18.5442 38.4452 13.2057 38.4452 13.2057C38.4452 13.2057 37.8679 13.8054 36.7491 14.0134C36.8202 12.9659 36.7046 18.5015 32.4947 18.4839C29.6497 18.4721 28.6775 15.1954 28.7531 14.7406C28.8496 14.161 27.7916 18.5654 25.0281 18.4968C23.8877 18.4633 23.0375 17.6376 22.504 16.5368C21.7898 17.354 20.7529 18.4968 19.5897 18.4968C17.5017 18.4968 16.5812 16.7504 16.7371 11.7624C16.7504 11.1708 16.7077 10.9381 16.1196 10.8496C15.7666 10.7907 15.4051 10.6792 15.0226 10.6204C14.9 11.0295 13.8602 18.3637 10.2847 18.5029C9.08519 18.5496 8.4293 17.5105 7.89066 16.7393C7.06497 17.8316 5.97501 18.5377 4.42227 18.5377C1.79205 18.5377 0 16.4114 0 13.7885C0 11.1655 1.79205 9.03942 4.42227 9.03942C4.88732 9.03942 5.01787 9.10608 5.44272 9.23004C4.569 1.27504 6.63238 0.0317866 8.43739 0.0317866C10.1703 0.0317866 13.1308 4.05384 8.96512 14.2559C9.88998 17.2989 11.8838 17.1268 12.8419 10.8626C13.0369 9.58927 12.5155 7.87099 13.3265 7.63117C14.809 7.19289 14.9663 8.50787 15.6614 8.72697C16.3964 8.95853 16.8254 8.93592 17.531 9.08327C18.7367 9.31873 19.2072 9.96643 19.0603 11.409C18.8839 13.2343 18.5753 15.891 19.5162 16.2148C20.1947 16.45 21.4335 15.0429 21.6509 14.273C21.8682 13.5031 21.9136 13.2396 21.9329 12.6749C21.9623 11.468 21.9992 10.5833 22.205 9.67055C22.2931 9.31736 22.3935 9.08347 22.7931 9.06748C23.1219 9.0591 23.7232 8.96009 23.9879 9.16611C24.3407 9.43119 24.2966 9.70017 24.2561 10.4081C23.8458 20.5015 27.0038 15.4628 27.9454 11.4283C27.6101 6.86623 27.8403 0.115326 30.6991 0.00210112C32.1859 -0.0567822 32.8432 1.13431 32.9155 2.02335C33.12 4.53433 31.9745 8.69372 30.468 11.4909C29.607 17.1984 34.2325 18.3269 34.9722 13.7712C33.762 13.1958 32.4541 10.8668 33.5184 9.73181C34.1156 9.09483 36.6015 10.0099 36.6422 12.0057C37.8616 11.6796 38.0244 10.9911 38.0413 11.1052C37.7061 6.54312 38.017 0.115385 40.876 0.00215941C42.3626 -0.0567239 43.0198 1.13437 43.0921 2.02341C43.2966 4.53438 42.1511 8.69378 40.6448 11.491C39.7837 17.1984 44.4093 18.327 45.1488 13.7713C44.2528 13.5984 42.3614 11.1212 43.4527 9.73187C44.0359 8.98944 46.5127 10.5334 46.79 12.0718C47.9614 11.7403 48.1205 11.0737 48.1373 11.1859C47.802 6.62397 48.1129 0.196235 50.9719 0.0830097C52.4585 0.0241264 53.1157 1.21522 53.188 2.10426C53.3925 4.61523 52.247 8.77471 50.7405 11.5719C49.8796 17.2794 54.5051 18.4077 55.2448 13.852C54.0135 13.647 52.2636 11.0314 53.672 9.69333C54.2347 9.15869 56.3848 10.5465 56.8881 12.1298C57.5874 12.1029 58.0227 11.8617 58.116 11.8374C56.9996 6.4818 57.8307 0.0558781 60.9062 0.00223793C62.5685 -0.0267262 64.1936 0.900905 63.4803 5.99604C62.7994 10.8574 60.3519 12.8975 60.3576 12.9287C60.5 13.5111 61.7559 18.3851 64.9185 15.8134C64.7548 15.4427 64.5909 15.064 64.4993 14.6052C63.9751 11.9327 65.0047 8.91409 67.8032 8.42622C69.4066 8.14671 70.917 8.92734 71.1558 10.6872C71.5487 13.5669 68.9484 15.6524 67.9596 16.1048C67.5167 15.8532 71.9742 18.712 74.6196 12.9829C74.773 12.6558 74.9579 12.6835 75.1975 12.8521C75.3667 12.9712 76.3305 13.8842 75.8822 14.657ZM6.33552 13.0809C6.20092 12.6785 5.92469 11.7918 5.82701 11.4076C5.28905 10.9398 4.90507 10.8638 4.21455 10.8638C2.68 10.8638 1.77679 12.2826 1.77679 13.8125C1.77679 15.3423 2.76077 16.7613 4.29533 16.7613C5.6252 16.7613 6.63735 15.8571 6.94615 14.5771C6.73001 14.0781 6.5156 13.6195 6.33552 13.0809ZM8.39979 2.13753C7.32153 2.13753 6.95576 4.70973 7.02674 6.90406C7.08953 8.84411 7.63164 10.5521 7.91286 11.3174C7.98426 11.4133 7.971 11.3509 8.03474 11.4526C9.90694 7.35053 9.13041 2.13753 8.39979 2.13753ZM30.8531 2.21833C29.4595 2.0585 29.4685 7.86389 29.6415 9.16611C30.3653 8.00823 31.6226 2.53009 30.8531 2.21833ZM41.0297 2.21833C39.6361 2.0585 39.6451 7.86389 39.8182 9.16611C40.5419 8.00823 41.7993 2.53009 41.0297 2.21833ZM51.1257 2.2991C49.732 2.13927 49.741 7.94469 49.9141 9.247C50.6378 8.08902 51.8952 2.61084 51.1257 2.2991ZM61.06 1.87849C58.8887 2.31451 59.3943 9.55399 59.7241 10.8237C62.2817 7.31085 62.3344 1.74654 61.06 1.87849ZM69.1975 10.9254C69.0871 10.4638 68.5351 10.2171 68.1541 10.2779C67.0639 10.4268 66.0065 11.7846 66.4158 13.8518C66.5069 14.3122 66.7349 14.736 66.7312 14.7219C69.1711 13.085 69.3785 11.7951 69.1975 10.9254ZM23.2205 7.46958C22.9089 7.46964 22.6043 7.37729 22.3452 7.20422C22.0861 7.03114 21.8841 6.78512 21.7648 6.49725C21.6455 6.20939 21.6143 5.89261 21.6751 5.587C21.7358 5.28138 21.8859 5.00065 22.1062 4.7803C22.3265 4.55995 22.6072 4.40989 22.9128 4.34909C23.2184 4.28829 23.5352 4.31948 23.8231 4.43873C24.111 4.55797 24.357 4.75991 24.5301 5.019C24.7032 5.27809 24.7956 5.5827 24.7956 5.89429C24.7956 6.10115 24.7549 6.30599 24.6757 6.4971C24.5966 6.68822 24.4806 6.86187 24.3343 7.00815C24.188 7.15443 24.0144 7.27047 23.8233 7.34964C23.6322 7.42882 23.4274 7.46957 23.2205 7.46958Z" fill="#0D0C22"></path>
</svg>

      <span class="accessibility-text">Back to home page</span>
    </a>
  </div>
  <ul class="site-nav-desktop-nav">
    <li class="site-nav-desktop-item site-nav-hover-item" data-site-nav-category="Top Nav">
      <a data-site-nav-element="Inspiration" href="https://dribbble.com/shots">Inspiration</a>
      <div class="site-nav-hover-menu">
        <ul class="site-nav-dropdown-list site-nav-dropdown-white-bg">
          <div data-site-nav-category="Inspiration">
            <li class="site-nav-dropdown-item purple">
              <a class="site-nav-dropdown-item-padding site-nav-dropdown-item-hover flex" data-site-nav-element="Explore Design Work" href="https://dribbble.com/shots/popular">
                <div class="site-nav-dropdown-icon-container">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none" class="site-nav-dropdown-icon">
<path d="M2.5 0.75H6.5C7.4665 0.75 8.25 1.5335 8.25 2.5V6.5C8.25 7.4665 7.4665 8.25 6.5 8.25H2.5C1.5335 8.25 0.75 7.4665 0.75 6.5V2.5C0.75 1.5335 1.5335 0.75 2.5 0.75ZM13.5 0.75H17.5C18.4665 0.75 19.25 1.5335 19.25 2.5V6.5C19.25 7.4665 18.4665 8.25 17.5 8.25H13.5C12.5335 8.25 11.75 7.4665 11.75 6.5V2.5C11.75 1.5335 12.5335 0.75 13.5 0.75ZM13.5 11.75H17.5C18.4665 11.75 19.25 12.5335 19.25 13.5V17.5C19.25 18.4665 18.4665 19.25 17.5 19.25H13.5C12.5335 19.25 11.75 18.4665 11.75 17.5V13.5C11.75 12.5335 12.5335 11.75 13.5 11.75ZM2.5 11.75H6.5C7.4665 11.75 8.25 12.5335 8.25 13.5V17.5C8.25 18.4665 7.4665 19.25 6.5 19.25H2.5C1.5335 19.25 0.75 18.4665 0.75 17.5V13.5C0.75 12.5335 1.5335 11.75 2.5 11.75Z" fill="currentColor" fill-opacity="0.2" stroke="currentColor" stroke-width="1.5"></path>
</svg>

                </div>
                <div class="site-nav-dropdown-item-text">
                  <div class="site-nav-dropdown-item-title">
                    Explore Design Work
                  </div>
                  <p class="site-nav-dropdown-item-desc">Trending designs to inspire you</p>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" fill="none" class="site-nav-dropdown-arrow icon-12 fill-current">
<path d="M11.7803 6.53033C12.0732 6.23744 12.0732 5.76257 11.7803 5.46967L7.18414 0.873479C6.89124 0.580585 6.41637 0.580585 6.12348 0.873478C5.83058 1.16637 5.83058 1.64125 6.12348 1.93414L9.43934 5.25H0.75C0.335787 5.25 0 5.58579 0 6C0 6.41422 0.335787 6.75 0.75 6.75H9.43934L6.12348 10.0659C5.83058 10.3588 5.83058 10.8336 6.12348 11.1265C6.41637 11.4194 6.89124 11.4194 7.18414 11.1265L11.7803 6.53033Z"></path>
</svg>

</a>            </li>
            <li class="site-nav-dropdown-item red">
              <a class="site-nav-dropdown-item-padding site-nav-dropdown-item-hover flex" data-site-nav-element="New &amp; Noteworthy" href="https://dribbble.com/shots/recent">
                <div class="site-nav-dropdown-icon-container">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="currentColor" class="site-nav-dropdown-icon">
<path d="M13.4842 15.5185C11.8769 16.8972 10.4552 17.9485 10 18.279C9.54459 17.9483 8.12304 16.897 6.51599 15.5185C4.62061 13.8926 2.5424 11.8742 1.4452 10.0758L1.44176 10.07L1.43651 10.0611C0.987839 9.31334 0.75 8.45358 0.75 7.57322C0.75 4.91104 2.90591 2.75 5.54681 2.75C7.07683 2.75 8.49854 3.48017 9.39693 4.69529L10 5.51098L10.6031 4.69529C11.5014 3.48018 12.9234 2.75 14.4532 2.75C17.0941 2.75 19.25 4.91104 19.25 7.57322C19.25 8.45358 19.0122 9.31334 18.5635 10.0611L18.5581 10.0701L18.556 10.0738C17.4592 11.8727 15.3803 13.892 13.4842 15.5185Z" fill="currentColor" fill-opacity="0.2" stroke="currentColor" stroke-width="1.5"></path>
</svg>

                </div>
                <div class="site-nav-dropdown-item-text">
                  <div class="site-nav-dropdown-item-title">
                    New &amp; Noteworthy
                  </div>
                  <p class="site-nav-dropdown-item-desc">Up-and-coming designers</p>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" fill="none" class="site-nav-dropdown-arrow icon-12 fill-current">
<path d="M11.7803 6.53033C12.0732 6.23744 12.0732 5.76257 11.7803 5.46967L7.18414 0.873479C6.89124 0.580585 6.41637 0.580585 6.12348 0.873478C5.83058 1.16637 5.83058 1.64125 6.12348 1.93414L9.43934 5.25H0.75C0.335787 5.25 0 5.58579 0 6C0 6.41422 0.335787 6.75 0.75 6.75H9.43934L6.12348 10.0659C5.83058 10.3588 5.83058 10.8336 6.12348 11.1265C6.41637 11.4194 6.89124 11.4194 7.18414 11.1265L11.7803 6.53033Z"></path>
</svg>

</a>            </li>
            <li class="site-nav-dropdown-item green">
              <a class="site-nav-dropdown-item-padding site-nav-dropdown-item-hover flex" data-site-nav-element="Playoffs" href="https://dribbble.com/shots?list=playoffs">
                <div class="site-nav-dropdown-icon-container">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none" class="site-nav-dropdown-icon">
<rect x="0.75" y="4.75" width="14.5" height="14.5" rx="3.25" fill="currentColor" fill-opacity="0.2" stroke="currentColor" stroke-width="1.5"></rect>
<path d="M16 1.5H8C7.40168 1.5 6.85156 1.70993 6.42022 2.062H4.5C5.18236 0.832288 6.49399 0 8 0H16C18.2091 0 20 1.79086 20 4V12C20 13.4806 19.1956 14.7733 18 15.4649V13.5002C18.314 13.0824 18.5 12.5629 18.5 12V4C18.5 2.61929 17.3807 1.5 16 1.5Z" fill="currentColor"></path>
</svg>

                </div>
                <div class="site-nav-dropdown-item-text">
                  <div class="site-nav-dropdown-item-title">
                    Playoffs
                  </div>
                  <p class="site-nav-dropdown-item-desc">Work designers are riffing on</p>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" fill="none" class="site-nav-dropdown-arrow icon-12 fill-current">
<path d="M11.7803 6.53033C12.0732 6.23744 12.0732 5.76257 11.7803 5.46967L7.18414 0.873479C6.89124 0.580585 6.41637 0.580585 6.12348 0.873478C5.83058 1.16637 5.83058 1.64125 6.12348 1.93414L9.43934 5.25H0.75C0.335787 5.25 0 5.58579 0 6C0 6.41422 0.335787 6.75 0.75 6.75H9.43934L6.12348 10.0659C5.83058 10.3588 5.83058 10.8336 6.12348 11.1265C6.41637 11.4194 6.89124 11.4194 7.18414 11.1265L11.7803 6.53033Z"></path>
</svg>

</a>            </li>
            <li class="site-nav-dropdown-item yellow">
              <a class="site-nav-dropdown-item-padding site-nav-dropdown-item-hover flex" data-site-nav-element="Blog" href="https://dribbble.com/stories">
                <div class="site-nav-dropdown-icon-container">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none" class="site-nav-dropdown-icon">
<rect x="0.75" y="0.75" width="18.5" height="18.5" rx="3.25" fill="currentColor" fill-opacity="0.2" stroke="currentColor" stroke-width="1.5"></rect>
<path d="M4.16667 5.08331C4.16667 4.6691 4.50246 4.33331 4.91667 4.33331H14.25C14.6642 4.33331 15 4.6691 15 5.08331C15 5.49753 14.6642 5.83331 14.25 5.83331L4.91667 5.83331C4.50246 5.83331 4.16667 5.49753 4.16667 5.08331Z" fill="currentColor"></path>
<path d="M4.16667 9.25C4.16667 8.83579 4.50246 8.5 4.91667 8.5L12.5833 8.5C12.9976 8.5 13.3333 8.83579 13.3333 9.25C13.3333 9.66422 12.9976 10 12.5833 10L4.91667 10C4.50246 10 4.16667 9.66421 4.16667 9.25Z" fill="currentColor"></path>
<path d="M4.91667 12.6666C4.50246 12.6666 4.16667 13.0024 4.16667 13.4166C4.16667 13.8308 4.50246 14.1666 4.91667 14.1666H9.25001C9.66422 14.1666 10 13.8308 10 13.4166C10 13.0024 9.66422 12.6666 9.25001 12.6666H4.91667Z" fill="currentColor"></path>
</svg>

                </div>
                <div class="site-nav-dropdown-item-text">
                  <div class="site-nav-dropdown-item-title">
                    Blog
                  </div>
                  <p class="site-nav-dropdown-item-desc">Interviews, tutorials, and more</p>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" fill="none" class="site-nav-dropdown-arrow icon-12 fill-current">
<path d="M11.7803 6.53033C12.0732 6.23744 12.0732 5.76257 11.7803 5.46967L7.18414 0.873479C6.89124 0.580585 6.41637 0.580585 6.12348 0.873478C5.83058 1.16637 5.83058 1.64125 6.12348 1.93414L9.43934 5.25H0.75C0.335787 5.25 0 5.58579 0 6C0 6.41422 0.335787 6.75 0.75 6.75H9.43934L6.12348 10.0659C5.83058 10.3588 5.83058 10.8336 6.12348 11.1265C6.41637 11.4194 6.89124 11.4194 7.18414 11.1265L11.7803 6.53033Z"></path>
</svg>

</a>            </li>
          </div>
        </ul>
        <ul class="site-nav-dropdown-list site-nav-dropdown-grey-bg" data-site-nav-category="Inspiration">
          <p class="site-nav-dropdown-item site-nav-dropdown-category-padding site-nav-dropdown-item-title">Browse Categories</p>
            <li>
              <a class="site-nav-dropdown-category-padding" data-site-nav-element="Animation" href="https://dribbble.com/shots/popular/animation">Animation</a>
            </li>
            <li>
              <a class="site-nav-dropdown-category-padding" data-site-nav-element="Branding" href="https://dribbble.com/shots/popular/branding">Branding</a>
            </li>
            <li>
              <a class="site-nav-dropdown-category-padding" data-site-nav-element="Illustration" href="https://dribbble.com/shots/popular/illustration">Illustration</a>
            </li>
            <li>
              <a class="site-nav-dropdown-category-padding" data-site-nav-element="Mobile" href="https://dribbble.com/shots/popular/mobile">Mobile</a>
            </li>
            <li>
              <a class="site-nav-dropdown-category-padding" data-site-nav-element="Print" href="https://dribbble.com/shots/popular/print">Print</a>
            </li>
            <li>
              <a class="site-nav-dropdown-category-padding" data-site-nav-element="Product Design" href="https://dribbble.com/shots/popular/product-design">Product Design</a>
            </li>
            <li>
              <a class="site-nav-dropdown-category-padding" data-site-nav-element="Typography" href="https://dribbble.com/shots/popular/typography">Typography</a>
            </li>
            <li>
              <a class="site-nav-dropdown-category-padding" data-site-nav-element="Web Design" href="https://dribbble.com/shots/popular/web-design">Web Design</a>
            </li>
        </ul>
      </div>
    </li>

    <li class="site-nav-desktop-item site-nav-hover-item" data-site-nav-category="Top Nav">
      <a data-site-nav-element="Find Work" href="https://dribbble.com/jobs">Find Work</a>
      <div class="site-nav-hover-menu">
        <ul class="site-nav-dropdown-list">
          <div data-site-nav-category="Find Work">
            <li class="site-nav-dropdown-item purple">
              <a class="site-nav-dropdown-item-padding site-nav-dropdown-item-hover flex" data-site-nav-element="Job Board" href="https://dribbble.com/jobs">
                <div class="site-nav-dropdown-icon-container">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none" class="site-nav-dropdown-icon">
<path d="M0.75 2.5C0.75 1.5335 1.5335 0.75 2.5 0.75H8.5C9.4665 0.75 10.25 1.5335 10.25 2.5V8.5C10.25 9.4665 9.4665 10.25 8.5 10.25H2.5C1.5335 10.25 0.75 9.4665 0.75 8.5V2.5ZM13.75 2.5C13.75 1.5335 14.5335 0.75 15.5 0.75H17.5C18.4665 0.75 19.25 1.5335 19.25 2.5V8.5C19.25 9.4665 18.4665 10.25 17.5 10.25H15.5C14.5335 10.25 13.75 9.4665 13.75 8.5V2.5ZM9.75 15.5C9.75 14.5335 10.5335 13.75 11.5 13.75H17.5C18.4665 13.75 19.25 14.5335 19.25 15.5V17.5C19.25 18.4665 18.4665 19.25 17.5 19.25H11.5C10.5335 19.25 9.75 18.4665 9.75 17.5V15.5ZM0.75 15.5C0.75 14.5335 1.5335 13.75 2.5 13.75H4.5C5.4665 13.75 6.25 14.5335 6.25 15.5V17.5C6.25 18.4665 5.4665 19.25 4.5 19.25H2.5C1.5335 19.25 0.75 18.4665 0.75 17.5V15.5Z" fill="currentColor" fill-opacity="0.2" stroke="currentColor" stroke-width="1.5"></path>
</svg>

                </div>
                <div class="site-nav-dropdown-item-text">
                  <div class="site-nav-dropdown-item-title">
                    Job Board
                  </div>
                  <p class="site-nav-dropdown-item-desc">Find your dream design job</p>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" fill="none" class="site-nav-dropdown-arrow icon-12 fill-current">
<path d="M11.7803 6.53033C12.0732 6.23744 12.0732 5.76257 11.7803 5.46967L7.18414 0.873479C6.89124 0.580585 6.41637 0.580585 6.12348 0.873478C5.83058 1.16637 5.83058 1.64125 6.12348 1.93414L9.43934 5.25H0.75C0.335787 5.25 0 5.58579 0 6C0 6.41422 0.335787 6.75 0.75 6.75H9.43934L6.12348 10.0659C5.83058 10.3588 5.83058 10.8336 6.12348 11.1265C6.41637 11.4194 6.89124 11.4194 7.18414 11.1265L11.7803 6.53033Z"></path>
</svg>

</a>            </li>
            <li class="site-nav-dropdown-item red">
              <a class="site-nav-dropdown-item-padding site-nav-dropdown-item-hover flex" data-site-nav-element="Freelance Projects" href="https://dribbble.com/freelance-jobs">
                <div class="site-nav-dropdown-icon-container">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none" class="site-nav-dropdown-icon">
<path d="M1.25 8.5C1.25 7.5335 2.0335 6.75 3 6.75H14C14.9665 6.75 15.75 7.5335 15.75 8.5V14C15.75 16.8995 13.3995 19.25 10.5 19.25H6.5C3.60051 19.25 1.25 16.8995 1.25 14V8.5Z" fill="currentColor" fill-opacity="0.2" stroke="currentColor" stroke-width="1.5"></path>
<path d="M6.25 0.833313C6.25 0.419099 5.91421 0.083313 5.5 0.083313C5.08579 0.083313 4.75 0.419099 4.75 0.833313V3.33331C4.75 3.74753 5.08579 4.08331 5.5 4.08331C5.91421 4.08331 6.25 3.74753 6.25 3.33331V0.833313Z" fill="currentColor"></path>
<path d="M9.58331 0.833313C9.58331 0.419099 9.24753 0.083313 8.83331 0.083313C8.4191 0.083313 8.08331 0.419099 8.08331 0.833313V3.33331C8.08331 3.74753 8.4191 4.08331 8.83331 4.08331C9.24753 4.08331 9.58331 3.74753 9.58331 3.33331V0.833313Z" fill="currentColor"></path>
<path d="M12.1667 0.083313C12.5809 0.083313 12.9167 0.419099 12.9167 0.833313V3.33331C12.9167 3.74753 12.5809 4.08331 12.1667 4.08331C11.7525 4.08331 11.4167 3.74753 11.4167 3.33331V0.833313C11.4167 0.419099 11.7525 0.083313 12.1667 0.083313Z" fill="currentColor"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M16 16C16.1693 16 16.3362 15.9895 16.5 15.9691C18.4732 15.723 20 14.0398 20 12C20 9.96019 18.4732 8.277 16.5 8.03095C16.3362 8.01052 16.1693 8 16 8H15V16H16ZM18.5 12C18.5 10.7905 17.6411 9.78164 16.5 9.55001V14.45C17.6411 14.2184 18.5 13.2095 18.5 12Z" fill="currentColor"></path>
</svg>

                </div>
                <div class="site-nav-dropdown-item-text">
                  <div class="site-nav-dropdown-item-title">
                    Freelance Projects
                    <div class="pro-flag margin-l-8">Pro+</div>
                  </div>
                  <p class="site-nav-dropdown-item-desc">An exclusive list for contract work</p>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" fill="none" class="site-nav-dropdown-arrow icon-12 fill-current">
<path d="M11.7803 6.53033C12.0732 6.23744 12.0732 5.76257 11.7803 5.46967L7.18414 0.873479C6.89124 0.580585 6.41637 0.580585 6.12348 0.873478C5.83058 1.16637 5.83058 1.64125 6.12348 1.93414L9.43934 5.25H0.75C0.335787 5.25 0 5.58579 0 6C0 6.41422 0.335787 6.75 0.75 6.75H9.43934L6.12348 10.0659C5.83058 10.3588 5.83058 10.8336 6.12348 11.1265C6.41637 11.4194 6.89124 11.4194 7.18414 11.1265L11.7803 6.53033Z"></path>
</svg>

</a>            </li>
            
            <li class="padding-h-32">
              <hr>
            </li>
            <li class="site-nav-dropdown-item green">
              <a class="site-nav-dropdown-item-padding site-nav-dropdown-item-hover flex" data-site-nav-element="Design Leads" href="https://dribbble.com/projects">
                <div class="site-nav-dropdown-icon-container">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none" class="site-nav-dropdown-icon">
<rect x="0.75" y="1.75" width="18.5" height="16.5" rx="3.25" fill="currentColor" fill-opacity="0.2" stroke="currentColor" stroke-width="1.5"></rect>
<path d="M19 4L10 11L1 4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
</svg>

                </div>
                <div class="site-nav-dropdown-item-text">
                  <div class="site-nav-dropdown-item-title">
                    Want freelance design projects?
                  </div>
                  <p class="site-nav-dropdown-item-desc">Get new leads in your inbox every day</p>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" fill="none" class="site-nav-dropdown-arrow icon-12 fill-current">
<path d="M11.7803 6.53033C12.0732 6.23744 12.0732 5.76257 11.7803 5.46967L7.18414 0.873479C6.89124 0.580585 6.41637 0.580585 6.12348 0.873478C5.83058 1.16637 5.83058 1.64125 6.12348 1.93414L9.43934 5.25H0.75C0.335787 5.25 0 5.58579 0 6C0 6.41422 0.335787 6.75 0.75 6.75H9.43934L6.12348 10.0659C5.83058 10.3588 5.83058 10.8336 6.12348 11.1265C6.41637 11.4194 6.89124 11.4194 7.18414 11.1265L11.7803 6.53033Z"></path>
</svg>

</a>            </li>
            <li class="site-nav-dropdown-item yellow">
              <a class="site-nav-dropdown-item-padding site-nav-dropdown-item-hover flex" data-site-nav-element="Pitch" href="https://dribbble.com/pro/pitch">
                <div class="site-nav-dropdown-icon-container">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none" class="site-nav-dropdown-icon">
<rect x="4.25" y="0.75" width="11.5" height="18.5" rx="2.25" stroke="currentColor" stroke-width="1.5"></rect>
<path d="M4.25 17C4.25 14.6528 6.15279 12.75 8.5 12.75H11.5C13.8472 12.75 15.75 14.6528 15.75 17C15.75 18.2426 14.7426 19.25 13.5 19.25H6.5C5.25736 19.25 4.25 18.2426 4.25 17Z" fill="currentColor" fill-opacity="0.2" stroke="currentColor" stroke-width="1.5"></path>
<circle cx="10" cy="7" r="3.25" fill="currentColor" fill-opacity="0.2" stroke="currentColor" stroke-width="1.5"></circle>
</svg>

                </div>
                <div class="site-nav-dropdown-item-text">
                  <div class="site-nav-dropdown-item-title">
                    Personalize your profile with video
                  </div>
                  <p class="site-nav-dropdown-item-desc">Introduce yourself to new clients with Pitch</p>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" fill="none" class="site-nav-dropdown-arrow icon-12 fill-current">
<path d="M11.7803 6.53033C12.0732 6.23744 12.0732 5.76257 11.7803 5.46967L7.18414 0.873479C6.89124 0.580585 6.41637 0.580585 6.12348 0.873478C5.83058 1.16637 5.83058 1.64125 6.12348 1.93414L9.43934 5.25H0.75C0.335787 5.25 0 5.58579 0 6C0 6.41422 0.335787 6.75 0.75 6.75H9.43934L6.12348 10.0659C5.83058 10.3588 5.83058 10.8336 6.12348 11.1265C6.41637 11.4194 6.89124 11.4194 7.18414 11.1265L11.7803 6.53033Z"></path>
</svg>

</a>            </li>
          </div>
        </ul>
      </div>
    </li>

    <li class="site-nav-desktop-item site-nav-hover-item" data-site-nav-category="Top Nav">
      <a data-site-nav-element="Learn Design" href="https://dribbble.com/learn">
        <span>Learn Design</span>
</a>      <div class="site-nav-hover-menu">
        <ul class="site-nav-dropdown-list">
          <div data-site-nav-category="Learn Design">
          <li class="site-nav-dropdown-item purple">
              <a class="site-nav-dropdown-item-padding site-nav-dropdown-item-hover flex" data-site-nav-element="Product Design Course" href="https://dribbble.com/courses/product-design">
                <div class="site-nav-dropdown-icon-container">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="22" viewBox="0 0 20 22" fill="none" class="site-nav-dropdown-icon">
<path d="M4.375 20.375C3.71196 20.375 3.07607 20.1116 2.60723 19.6427C2.13839 19.1739 1.875 18.538 1.875 17.875V6.44165C1.87606 6.05378 1.96736 5.67148 2.14167 5.32498L3.80833 1.99165C3.86038 1.88819 3.94014 1.80123 4.03873 1.74046C4.13732 1.6797 4.25086 1.64752 4.36667 1.64752C4.48248 1.64752 4.59601 1.6797 4.6946 1.74046C4.79319 1.80123 4.87295 1.88819 4.925 1.99165L6.59167 5.32498C6.76598 5.67148 6.85727 6.05378 6.85833 6.44165V17.875C6.85835 18.5351 6.59724 19.1685 6.132 19.6369C5.66676 20.1052 5.03515 20.3706 4.375 20.375Z" fill="none" fill-opacity="0.2" stroke="currentColor" stroke-width="1.5"></path>
<path d="M1.875 6.41669H6.875" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
<path d="M1.875 16.4167H6.875" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
<path d="M16.875 1.625H11.875C11.1846 1.625 10.625 2.18464 10.625 2.875V19.125C10.625 19.8154 11.1846 20.375 11.875 20.375H16.875C17.5654 20.375 18.125 19.8154 18.125 19.125V2.875C18.125 2.18464 17.5654 1.625 16.875 1.625Z" fill="none" fill-opacity="0.2" stroke="currentColor" stroke-width="1.5"></path>
<path d="M10.625 5.375H13.5417" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
<path d="M10.625 9.125H13.5417" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
<path d="M10.625 12.875H13.5417" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
<path d="M10.625 16.625H13.5417" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
</svg>

                </div>
                <div class="site-nav-dropdown-item-text">
                  <div class="site-nav-dropdown-item-title">
                    Certified Product Design Course
                  </div>
                  <p class="site-nav-dropdown-item-desc">Learn product design in just 12 weeks...</p>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" fill="none" class="site-nav-dropdown-arrow icon-12 fill-current">
<path d="M11.7803 6.53033C12.0732 6.23744 12.0732 5.76257 11.7803 5.46967L7.18414 0.873479C6.89124 0.580585 6.41637 0.580585 6.12348 0.873478C5.83058 1.16637 5.83058 1.64125 6.12348 1.93414L9.43934 5.25H0.75C0.335787 5.25 0 5.58579 0 6C0 6.41422 0.335787 6.75 0.75 6.75H9.43934L6.12348 10.0659C5.83058 10.3588 5.83058 10.8336 6.12348 11.1265C6.41637 11.4194 6.89124 11.4194 7.18414 11.1265L11.7803 6.53033Z"></path>
</svg>

</a>            </li>
            <li class="site-nav-dropdown-item yellow">
              <a class="site-nav-dropdown-item-padding site-nav-dropdown-item-hover flex" data-site-nav-element="Live Workshops" href="https://dribbble.com/learn">
                <div class="site-nav-dropdown-icon-container">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none" class="site-nav-dropdown-icon">
<path d="M15 7.50915L12.5 8.34249V7.50915C12.5 7.17763 12.3683 6.85969 12.1339 6.62527C11.8995 6.39085 11.5815 6.25916 11.25 6.25916H6.25C5.91848 6.25916 5.60054 6.39085 5.36612 6.62527C5.1317 6.85969 5 7.17763 5 7.50915V12.5092C5 12.8407 5.1317 13.1586 5.36612 13.393C5.60054 13.6275 5.91848 13.7592 6.25 13.7592H11.25C11.5815 13.7592 11.8995 13.6275 12.1339 13.393C12.3683 13.1586 12.5 12.8407 12.5 12.5092V11.6758L15 12.5092V7.50915Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
<path d="M5 2.00916H15C16.6569 2.00916 18 3.3523 18 5.00915V15.0092C18 16.666 16.6569 18.0092 15 18.0092H5C3.34315 18.0092 2 16.666 2 15.0092V5.00915C2 3.3523 3.34315 2.00916 5 2.00916Z" fill="none" fill-opacity="0.2" stroke="currentColor" stroke-width="1.5"></path>
</svg>

                </div>
                <div class="site-nav-dropdown-item-text">
                  <div class="site-nav-dropdown-item-title">
                    Live Workshops
                  </div>
                  <p class="site-nav-dropdown-item-desc">Level up your skills with our interactive workshops…</p>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" fill="none" class="site-nav-dropdown-arrow icon-12 fill-current">
<path d="M11.7803 6.53033C12.0732 6.23744 12.0732 5.76257 11.7803 5.46967L7.18414 0.873479C6.89124 0.580585 6.41637 0.580585 6.12348 0.873478C5.83058 1.16637 5.83058 1.64125 6.12348 1.93414L9.43934 5.25H0.75C0.335787 5.25 0 5.58579 0 6C0 6.41422 0.335787 6.75 0.75 6.75H9.43934L6.12348 10.0659C5.83058 10.3588 5.83058 10.8336 6.12348 11.1265C6.41637 11.4194 6.89124 11.4194 7.18414 11.1265L11.7803 6.53033Z"></path>
</svg>

</a>            </li>
          </div>
        </ul>
      </div>
    </li>

      <li class="site-nav-desktop-item" data-site-nav-category="Top Nav">
        <a data-site-nav-element="Go Pro" href="https://dribbble.com/pro">Go Pro</a>
      </li>

    <li class="site-nav-desktop-item site-nav-hover-item" data-site-nav-category="Top Nav">
      <a data-site-nav-element="Marketplace" href="https://dribbble.com/marketplace">Marketplace</a>
      <div class="site-nav-hover-menu">
        <ul class="site-nav-dropdown-list">
          <div data-site-nav-category="Marketplace">
          <li class="site-nav-dropdown-item purple">
              <a class="site-nav-dropdown-item-padding site-nav-dropdown-item-hover flex" data-site-nav-element="Discover" href="https://dribbble.com/marketplace">
                <div class="site-nav-dropdown-icon-container">
                  <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" viewBox="0 0 21 21" fill="currentColor" class="site-nav-dropdown-icon">
    <path d="M14.6161 1.44455C15.1043 0.956391 15.8957 0.956391 16.3839 1.44455L19.5659 4.62653C20.054 5.11468 20.054 5.90614 19.5659 6.39429L16.3839 9.57627C15.8957 10.0644 15.1043 10.0644 14.6161 9.57627L11.4341 6.39429C10.946 5.90614 10.946 5.11468 11.4341 4.62653L14.6161 1.44455L14.0963 0.924774L14.6161 1.44455ZM0.75 3C0.75 2.30965 1.30964 1.75 2 1.75H7C7.69036 1.75 8.25 2.30965 8.25 3V8C8.25 8.69036 7.69036 9.25 7 9.25H2C1.30964 9.25 0.75 8.69036 0.75 8V3ZM0.75 14C0.75 13.3096 1.30964 12.75 2 12.75H7C7.69036 12.75 8.25 13.3096 8.25 14V19C8.25 19.6904 7.69036 20.25 7 20.25H2C1.30964 20.25 0.75 19.6904 0.75 19V14ZM11.75 14C11.75 13.3096 12.3096 12.75 13 12.75H18C18.6904 12.75 19.25 13.3096 19.25 14V19C19.25 19.6904 18.6904 20.25 18 20.25H13C12.3096 20.25 11.75 19.6904 11.75 19V14Z" fill="currentColor" fill-opacity="0.2" stroke="currentColor" stroke-width="1.5"></path>
</svg>

                </div>
                <div class="site-nav-dropdown-item-text">
                  <div class="site-nav-dropdown-item-title">
                    Discover
                  </div>
                  <p class="site-nav-dropdown-item-desc">A marketplace of digital assets…</p>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" fill="none" class="site-nav-dropdown-arrow icon-12 fill-current">
<path d="M11.7803 6.53033C12.0732 6.23744 12.0732 5.76257 11.7803 5.46967L7.18414 0.873479C6.89124 0.580585 6.41637 0.580585 6.12348 0.873478C5.83058 1.16637 5.83058 1.64125 6.12348 1.93414L9.43934 5.25H0.75C0.335787 5.25 0 5.58579 0 6C0 6.41422 0.335787 6.75 0.75 6.75H9.43934L6.12348 10.0659C5.83058 10.3588 5.83058 10.8336 6.12348 11.1265C6.41637 11.4194 6.89124 11.4194 7.18414 11.1265L11.7803 6.53033Z"></path>
</svg>

</a>            </li>

            
              <li class="site-nav-dropdown-item red">
                <a class="site-nav-dropdown-item-padding site-nav-dropdown-item-hover flex" data-site-nav-element="Graphics" href="https://dribbble.com/marketplace/graphics">

                  <div class="site-nav-dropdown-icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" width="19" height="20" viewBox="0 0 19 20" fill="none" class="site-nav-dropdown-icon">
<path d="M11.25 6C11.25 8.8995 8.8995 11.25 6 11.25C3.10051 11.25 0.75 8.8995 0.75 6C0.75 3.10051 3.10051 0.75 6 0.75C8.8995 0.75 11.25 3.10051 11.25 6ZM14.5762 12.2955L17.5713 17.3641C18.0636 18.1974 17.463 19.25 16.4951 19.25H10.5049C9.53702 19.25 8.93635 18.1974 9.42874 17.3641L12.4238 12.2954C12.9077 11.4767 14.0923 11.4767 14.5762 12.2955Z" fill="currentColor" fill-opacity="0.2" stroke="currentColor" stroke-width="1.5"></path>
</svg>

                  </div>

                  <div class="site-nav-dropdown-item-text">
                    <div class="site-nav-dropdown-item-title">
                      Graphics
                    </div>

                    <p class="site-nav-dropdown-item-desc">Icons, Illustrations, Patterns, Textures…</p>
                  </div>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" fill="none" class="site-nav-dropdown-arrow icon-12 fill-current">
<path d="M11.7803 6.53033C12.0732 6.23744 12.0732 5.76257 11.7803 5.46967L7.18414 0.873479C6.89124 0.580585 6.41637 0.580585 6.12348 0.873478C5.83058 1.16637 5.83058 1.64125 6.12348 1.93414L9.43934 5.25H0.75C0.335787 5.25 0 5.58579 0 6C0 6.41422 0.335787 6.75 0.75 6.75H9.43934L6.12348 10.0659C5.83058 10.3588 5.83058 10.8336 6.12348 11.1265C6.41637 11.4194 6.89124 11.4194 7.18414 11.1265L11.7803 6.53033Z"></path>
</svg>

</a>              </li>
              <li class="site-nav-dropdown-item green">
                <a class="site-nav-dropdown-item-padding site-nav-dropdown-item-hover flex" data-site-nav-element="Fonts" href="https://dribbble.com/marketplace/fonts">

                  <div class="site-nav-dropdown-icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="currentColor" class="site-nav-dropdown-icon">
<path d="M0.760205 8.3167C0.692108 8.72528 0.968123 9.1117 1.3767 9.1798C1.78528 9.24789 2.1717 8.97188 2.2398 8.5633L0.760205 8.3167ZM6.46 4.1L6.35393 3.35754L6.35393 3.35754L6.46 4.1ZM17.636 1.3975C17.8555 1.04625 17.7488 0.583534 17.3975 0.364001C17.0462 0.144469 16.5835 0.251248 16.364 0.602501L17.636 1.3975ZM2.6002 13.3786L3.26145 13.7325L3.26145 13.7325L2.6002 13.3786ZM10.8001 11.05C11.2143 11.05 11.5501 10.7142 11.5501 10.3C11.5501 9.88579 11.2143 9.55 10.8001 9.55V11.05ZM1.5 8.44C2.2398 8.5633 2.23973 8.56369 2.23967 8.56406C2.23965 8.56416 2.23959 8.56452 2.23955 8.56474C2.23948 8.56516 2.23942 8.56553 2.23937 8.56584C2.23926 8.56647 2.23919 8.56689 2.23915 8.56708C2.23908 8.56748 2.23916 8.56702 2.2394 8.56574C2.23988 8.56316 2.24099 8.55727 2.24283 8.54828C2.24651 8.53028 2.25307 8.49993 2.26323 8.45883C2.28357 8.3765 2.31809 8.25191 2.37228 8.09768C2.48109 7.78799 2.66639 7.36673 2.96914 6.92941C3.56332 6.07115 4.62831 5.11928 6.56607 4.84246L6.35393 3.35754C3.95169 3.70072 2.53668 4.91885 1.73586 6.07559C1.34111 6.64577 1.10016 7.19326 0.957092 7.60045C0.885345 7.80465 0.837528 7.97553 0.807009 8.09906C0.791733 8.16089 0.78074 8.21107 0.773218 8.24786C0.769456 8.26627 0.766558 8.28135 0.764423 8.29288C0.763356 8.29865 0.762479 8.30354 0.76178 8.30752C0.761431 8.30951 0.761126 8.31127 0.760864 8.3128C0.760733 8.31357 0.760612 8.31427 0.760503 8.31492C0.760448 8.31525 0.760373 8.31569 0.760346 8.31586C0.760274 8.31629 0.760205 8.3167 1.5 8.44ZM6.56607 4.84246C7.57053 4.69897 8.51421 4.73293 9.44844 4.79966C10.3572 4.86457 11.3189 4.9681 12.2291 4.92673C13.1673 4.88408 14.1111 4.68878 15.0403 4.1335C15.9638 3.58169 16.817 2.70787 17.636 1.3975L16.364 0.602501C15.633 1.77213 14.9362 2.44831 14.2709 2.84588C13.6114 3.23997 12.9277 3.39342 12.1609 3.42827C11.3661 3.4644 10.5453 3.37418 9.55531 3.30347C8.59079 3.23457 7.51947 3.19103 6.35393 3.35754L6.56607 4.84246ZM5.14106 4.37183C5.73972 5.91126 6.30914 8.38787 6.44475 10.6859L7.94214 10.5975C7.79793 8.15371 7.19797 5.5225 6.53906 3.82817L5.14106 4.37183ZM6.44475 10.6859C6.53354 12.1906 6.43052 13.5336 6.08761 14.4692C5.9185 14.9307 5.70693 15.2489 5.47375 15.4482C5.25397 15.636 4.97943 15.75 4.60006 15.75V17.25C5.3231 17.25 5.94669 17.0172 6.44832 16.5885C6.93653 16.1712 7.26962 15.6031 7.496 14.9854C7.94408 13.7629 8.03533 12.1768 7.94214 10.5975L6.44475 10.6859ZM4.60006 15.75C3.58083 15.75 3.25531 15.368 3.13436 15.0743C2.97657 14.6911 3.03884 14.1484 3.26145 13.7325L1.93896 13.0247C1.56135 13.7302 1.37795 14.7483 1.74734 15.6454C2.15357 16.632 3.1281 17.25 4.60006 17.25V15.75ZM3.26145 13.7325C3.78686 12.7509 5.11165 11.845 7.3475 11.3757L7.03939 9.90771C4.61144 10.4173 2.76728 11.4771 1.93896 13.0247L3.26145 13.7325ZM7.3475 11.3757C8.34386 11.1666 9.49864 11.05 10.8001 11.05V9.55C9.41122 9.55 8.15172 9.67425 7.03939 9.90771L7.3475 11.3757Z"></path>
</svg>

                  </div>

                  <div class="site-nav-dropdown-item-text">
                    <div class="site-nav-dropdown-item-title">
                      Fonts
                    </div>

                    <p class="site-nav-dropdown-item-desc">Display, Script, Sans Serif, Serif…</p>
                  </div>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" fill="none" class="site-nav-dropdown-arrow icon-12 fill-current">
<path d="M11.7803 6.53033C12.0732 6.23744 12.0732 5.76257 11.7803 5.46967L7.18414 0.873479C6.89124 0.580585 6.41637 0.580585 6.12348 0.873478C5.83058 1.16637 5.83058 1.64125 6.12348 1.93414L9.43934 5.25H0.75C0.335787 5.25 0 5.58579 0 6C0 6.41422 0.335787 6.75 0.75 6.75H9.43934L6.12348 10.0659C5.83058 10.3588 5.83058 10.8336 6.12348 11.1265C6.41637 11.4194 6.89124 11.4194 7.18414 11.1265L11.7803 6.53033Z"></path>
</svg>

</a>              </li>
              <li class="site-nav-dropdown-item yellow">
                <a class="site-nav-dropdown-item-padding site-nav-dropdown-item-hover flex" data-site-nav-element="Templates" href="https://dribbble.com/marketplace/templates">

                  <div class="site-nav-dropdown-icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none" class="site-nav-dropdown-icon">
<rect x="0.75" y="0.75" width="18.5" height="18.5" rx="3.25" fill="currentColor" fill-opacity="0.2" stroke="currentColor" stroke-width="1.5"></rect>
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 7H19V5.5H1V7H7V19H8.5V7Z" fill="currentColor"></path>
</svg>

                  </div>

                  <div class="site-nav-dropdown-item-text">
                    <div class="site-nav-dropdown-item-title">
                      Templates
                    </div>

                    <p class="site-nav-dropdown-item-desc">Mock Ups, Social Media, Presentations…</p>
                  </div>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" fill="none" class="site-nav-dropdown-arrow icon-12 fill-current">
<path d="M11.7803 6.53033C12.0732 6.23744 12.0732 5.76257 11.7803 5.46967L7.18414 0.873479C6.89124 0.580585 6.41637 0.580585 6.12348 0.873478C5.83058 1.16637 5.83058 1.64125 6.12348 1.93414L9.43934 5.25H0.75C0.335787 5.25 0 5.58579 0 6C0 6.41422 0.335787 6.75 0.75 6.75H9.43934L6.12348 10.0659C5.83058 10.3588 5.83058 10.8336 6.12348 11.1265C6.41637 11.4194 6.89124 11.4194 7.18414 11.1265L11.7803 6.53033Z"></path>
</svg>

</a>              </li>
              <li class="site-nav-dropdown-item purple">
                <a class="site-nav-dropdown-item-padding site-nav-dropdown-item-hover flex" data-site-nav-element="3D" href="https://dribbble.com/marketplace/3d">

                  <div class="site-nav-dropdown-icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none" class="site-nav-dropdown-icon">
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.62528 0.682372C9.34091 0.361573 10.1595 0.361573 10.8751 0.682372L19.0409 4.34292C19.1081 4.37121 19.1704 4.40901 19.2259 4.45464C19.2615 4.48385 19.2942 4.51615 19.3236 4.55105C19.4354 4.68376 19.5002 4.85399 19.5002 5.0345V14.9484C19.5036 15.0995 19.4609 15.2477 19.3798 15.3732C19.3222 15.4624 19.2452 15.54 19.1516 15.5992C19.1167 15.6213 19.0798 15.6407 19.0414 15.6569L10.8751 19.3176C10.1595 19.6384 9.34091 19.6384 8.62528 19.3176L0.459072 15.6569C0.420262 15.6406 0.383117 15.621 0.347962 15.5987C0.255151 15.5399 0.178767 15.4629 0.121449 15.3746C0.0397873 15.2488 -0.00317647 15.1001 0.000183103 14.9486V5.0345C0.000183103 4.80088 0.106999 4.59219 0.274458 4.45464C0.330003 4.40902 0.392221 4.37122 0.459426 4.34293L8.62528 0.682372ZM10.2615 2.05114C9.93622 1.90532 9.56415 1.90532 9.23886 2.05114L2.58367 5.0345L9.75018 8.24707L16.9167 5.0345L10.2615 2.05114ZM9.00018 17.8419L1.50018 14.4798V6.19261L9.00018 9.55468V17.8419ZM10.5002 17.8419L18.0002 14.4798V6.19261L10.5002 9.55468V17.8419Z" fill="currentColor"></path>
<path d="M1 4.80823L1.03864 4.8254L9.18189 1.17498C9.70235 0.941673 10.2977 0.941673 10.8181 1.17498L18.9614 4.8254L19 4.80823V14.8082L10 18.3082L1 14.8082V4.80823Z" fill="currentColor" fill-opacity="0.2"></path>
</svg>

                  </div>

                  <div class="site-nav-dropdown-item-text">
                    <div class="site-nav-dropdown-item-title">
                      3D
                    </div>

                    <p class="site-nav-dropdown-item-desc">Characters, Objects, Textures…</p>
                  </div>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" fill="none" class="site-nav-dropdown-arrow icon-12 fill-current">
<path d="M11.7803 6.53033C12.0732 6.23744 12.0732 5.76257 11.7803 5.46967L7.18414 0.873479C6.89124 0.580585 6.41637 0.580585 6.12348 0.873478C5.83058 1.16637 5.83058 1.64125 6.12348 1.93414L9.43934 5.25H0.75C0.335787 5.25 0 5.58579 0 6C0 6.41422 0.335787 6.75 0.75 6.75H9.43934L6.12348 10.0659C5.83058 10.3588 5.83058 10.8336 6.12348 11.1265C6.41637 11.4194 6.89124 11.4194 7.18414 11.1265L11.7803 6.53033Z"></path>
</svg>

</a>              </li>
              <li class="site-nav-dropdown-item red">
                <a class="site-nav-dropdown-item-padding site-nav-dropdown-item-hover flex" data-site-nav-element="Themes" href="https://dribbble.com/marketplace/themes">

                  <div class="site-nav-dropdown-icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none" class="site-nav-dropdown-icon">
<rect x="0.75" y="0.75" width="18.5" height="18.5" rx="3.25" fill="currentColor" fill-opacity="0.2" stroke="currentColor" stroke-width="1.5"></rect>
<path fill-rule="evenodd" clip-rule="evenodd" d="M4 5C4.55228 5 5 4.55228 5 4C5 3.44772 4.55228 3 4 3C3.44772 3 3 3.44772 3 4C3 4.55228 3.44772 5 4 5ZM0 6.25V7.75H20V6.25H0ZM8 4C8 4.55228 7.55228 5 7 5C6.44772 5 6 4.55228 6 4C6 3.44772 6.44772 3 7 3C7.55228 3 8 3.44772 8 4ZM10 5C10.5523 5 11 4.55228 11 4C11 3.44772 10.5523 3 10 3C9.44771 3 9 3.44772 9 4C9 4.55228 9.44771 5 10 5Z" fill="currentColor"></path>
</svg>

                  </div>

                  <div class="site-nav-dropdown-item-text">
                    <div class="site-nav-dropdown-item-title">
                      Themes
                    </div>

                    <p class="site-nav-dropdown-item-desc">WordPress, Shopify, Bootstrap, HTML5…</p>
                  </div>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" fill="none" class="site-nav-dropdown-arrow icon-12 fill-current">
<path d="M11.7803 6.53033C12.0732 6.23744 12.0732 5.76257 11.7803 5.46967L7.18414 0.873479C6.89124 0.580585 6.41637 0.580585 6.12348 0.873478C5.83058 1.16637 5.83058 1.64125 6.12348 1.93414L9.43934 5.25H0.75C0.335787 5.25 0 5.58579 0 6C0 6.41422 0.335787 6.75 0.75 6.75H9.43934L6.12348 10.0659C5.83058 10.3588 5.83058 10.8336 6.12348 11.1265C6.41637 11.4194 6.89124 11.4194 7.18414 11.1265L11.7803 6.53033Z"></path>
</svg>

</a>              </li>
              <li class="site-nav-dropdown-item green">
                <a class="site-nav-dropdown-item-padding site-nav-dropdown-item-hover flex" data-site-nav-element="Add Ons" href="https://dribbble.com/marketplace/add-ons">

                  <div class="site-nav-dropdown-icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none" class="site-nav-dropdown-icon">
<path d="M0.75 2C0.75 1.30964 1.30964 0.75 2 0.75H7C7.69036 0.75 8.25 1.30964 8.25 2V7C8.25 7.69036 7.69036 8.25 7 8.25H2C1.30964 8.25 0.75 7.69036 0.75 7V2ZM0.75 13C0.75 12.3096 1.30964 11.75 2 11.75H7C7.69036 11.75 8.25 12.3096 8.25 13V18C8.25 18.6904 7.69036 19.25 7 19.25H2C1.30964 19.25 0.75 18.6904 0.75 18V13ZM11.75 13C11.75 12.3096 12.3096 11.75 13 11.75H18C18.6904 11.75 19.25 12.3096 19.25 13V18C19.25 18.6904 18.6904 19.25 18 19.25H13C12.3096 19.25 11.75 18.6904 11.75 18V13Z" fill="currentColor" fill-opacity="0.2" stroke="currentColor" stroke-width="1.5"></path>
<path d="M16.25 1C16.25 0.585786 15.9142 0.25 15.5 0.25C15.0858 0.25 14.75 0.585786 14.75 1V3.75H12C11.5858 3.75 11.25 4.08579 11.25 4.5C11.25 4.91421 11.5858 5.25 12 5.25H14.75V8C14.75 8.41421 15.0858 8.75 15.5 8.75C15.9142 8.75 16.25 8.41421 16.25 8V5.25H19C19.4142 5.25 19.75 4.91421 19.75 4.5C19.75 4.08579 19.4142 3.75 19 3.75H16.25V1Z" fill="currentColor"></path>
</svg>

                  </div>

                  <div class="site-nav-dropdown-item-text">
                    <div class="site-nav-dropdown-item-title">
                      Add Ons
                    </div>

                    <p class="site-nav-dropdown-item-desc">Procreate, Affinity, Photoshop, InDesign…</p>
                  </div>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" fill="none" class="site-nav-dropdown-arrow icon-12 fill-current">
<path d="M11.7803 6.53033C12.0732 6.23744 12.0732 5.76257 11.7803 5.46967L7.18414 0.873479C6.89124 0.580585 6.41637 0.580585 6.12348 0.873478C5.83058 1.16637 5.83058 1.64125 6.12348 1.93414L9.43934 5.25H0.75C0.335787 5.25 0 5.58579 0 6C0 6.41422 0.335787 6.75 0.75 6.75H9.43934L6.12348 10.0659C5.83058 10.3588 5.83058 10.8336 6.12348 11.1265C6.41637 11.4194 6.89124 11.4194 7.18414 11.1265L11.7803 6.53033Z"></path>
</svg>

</a>              </li>

            <li class="padding-h-32">
              <hr>
            </li>

            <li class="site-nav-dropdown-item green">
              <a class="site-nav-dropdown-item-padding site-nav-dropdown-item-hover flex" data-site-nav-element="Open a Shop" target="_blank" href="https://creativemarket.com/sell?u=dribbble&amp;utm_source=Dribbble&amp;utm_medium=navigation&amp;utm_campaign=marketplace&amp;uid=a027fa4d-1f9a-4462-aa0a-29d9c0503b25">
                <div class="site-nav-dropdown-icon-container">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" class="site-nav-dropdown-icon">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0.292893 12.3625L11.6066 23.6762C11.9971 24.0667 12.6303 24.0667 13.0208 23.6762L23.6762 13.0208C23.8707 12.8263 23.9765 12.5602 23.9687 12.2851L24 1.28571C23.9849 0.755822 23.2132 0.0151391 22.6834 0L11.6839 0.000408484C11.4089 -0.00744935 11.1428 0.098349 10.9483 0.292893L0.292893 10.9483C-0.0976312 11.3388 -0.0976309 11.9719 0.292893 12.3625ZM20.0919 3.87719C20.4824 4.26771 20.4824 4.90088 20.0919 5.2914C19.7014 5.68192 19.0682 5.68192 18.6777 5.2914C18.2871 4.90087 18.2871 4.26771 18.6777 3.87719C19.0682 3.48666 19.7014 3.48666 20.0919 3.87719ZM12.3249 6.74473C11.547 9.28963 9.79622 10.8869 7.32482 11.6549C9.79627 12.4223 11.547 14.0203 12.3249 16.5654C13.1027 14.0203 14.8534 12.4223 17.3248 11.6549C14.8534 10.8869 13.1027 9.28961 12.3249 6.74473Z"></path>
</svg>

                </div>
                <div class="site-nav-dropdown-item-text">
                  <div class="site-nav-dropdown-item-title">
                    Open a Shop
                  </div>
                  <p class="site-nav-dropdown-item-desc">Earn money doing what you love</p>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="currentColor" class="site-nav-dropdown-arrow icon-12 fill-current">
<path d="M12.9498 2.05017C13.5021 2.05017 13.9498 2.49789 13.9498 3.05017V11.7168C13.9498 12.2691 13.5021 12.7168 12.9498 12.7168C12.3975 12.7168 11.9498 12.2691 11.9498 11.7168V5.46438L3.7574 13.6568C3.36688 14.0473 2.73371 14.0473 2.34319 13.6568C1.95266 13.2662 1.95266 12.6331 2.34319 12.2426L10.5356 4.05017H4.28312C3.73084 4.05017 3.28312 3.60246 3.28312 3.05017C3.28312 2.49789 3.73084 2.05017 4.28312 2.05017L12.9498 2.05017Z"></path>
</svg>

</a>            </li>
          </div>
        </ul>
      </div>
    </li>

    <li class="site-nav-desktop-item site-nav-hover-item" data-site-nav-category="Top Nav">
      <a data-site-nav-element="Hire Designers" href="https://dribbble.com/hiring">Hire Designers</a>
      <div class="site-nav-hover-menu">
        <ul class="site-nav-dropdown-list">
          <div data-site-nav-category="Hire Designers">
            <li class="site-nav-dropdown-item purple">
              <a class="site-nav-dropdown-item-padding site-nav-dropdown-item-hover flex" data-site-nav-element="Designer Search" href="https://dribbble.com/designers">
                <div class="site-nav-dropdown-icon-container">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none" class="site-nav-dropdown-icon">
<path d="M14.25 10C14.25 12.3472 12.3472 14.25 10 14.25C7.65279 14.25 5.75 12.3472 5.75 10C5.75 7.65279 7.65279 5.75 10 5.75C12.3472 5.75 14.25 7.65279 14.25 10ZM5.25 17C5.25 18.2426 4.24264 19.25 3 19.25C1.75736 19.25 0.75 18.2426 0.75 17C0.75 15.7574 1.75736 14.75 3 14.75C4.24264 14.75 5.25 15.7574 5.25 17ZM5.25 3C5.25 4.24264 4.24264 5.25 3 5.25C1.75736 5.25 0.75 4.24264 0.75 3C0.75 1.75736 1.75736 0.75 3 0.75C4.24264 0.75 5.25 1.75736 5.25 3ZM19.25 17C19.25 18.2426 18.2426 19.25 17 19.25C15.7574 19.25 14.75 18.2426 14.75 17C14.75 15.7574 15.7574 14.75 17 14.75C18.2426 14.75 19.25 15.7574 19.25 17ZM19.25 3C19.25 4.24264 18.2426 5.25 17 5.25C15.7574 5.25 14.75 4.24264 14.75 3C14.75 1.75736 15.7574 0.75 17 0.75C18.2426 0.75 19.25 1.75736 19.25 3Z" fill="currentColor" fill-opacity="0.2" stroke="currentColor" stroke-width="1.5"></path>
</svg>

                </div>
                <div class="site-nav-dropdown-item-text">
                  <div class="site-nav-dropdown-item-title">
                    Designer Search
                  </div>
                  <p class="site-nav-dropdown-item-desc">Find, contact, and hire designers</p>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" fill="none" class="site-nav-dropdown-arrow icon-12 fill-current">
<path d="M11.7803 6.53033C12.0732 6.23744 12.0732 5.76257 11.7803 5.46967L7.18414 0.873479C6.89124 0.580585 6.41637 0.580585 6.12348 0.873478C5.83058 1.16637 5.83058 1.64125 6.12348 1.93414L9.43934 5.25H0.75C0.335787 5.25 0 5.58579 0 6C0 6.41422 0.335787 6.75 0.75 6.75H9.43934L6.12348 10.0659C5.83058 10.3588 5.83058 10.8336 6.12348 11.1265C6.41637 11.4194 6.89124 11.4194 7.18414 11.1265L11.7803 6.53033Z"></path>
</svg>

</a>            </li>
            <li class="site-nav-dropdown-item red">
              <a class="site-nav-dropdown-item-padding site-nav-dropdown-item-hover flex" data-site-nav-element="List my Job Opening" href="https://dribbble.com/jobs/new">
                <div class="site-nav-dropdown-icon-container">
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" class="site-nav-dropdown-icon small-icon">
<path d="M7 0.75L7 13.25M13.25 7L0.75 7" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
</svg>

                </div>
                <div class="site-nav-dropdown-item-text">
                  <div class="site-nav-dropdown-item-title">
                    List my Job Opening
                  </div>
                  <p class="site-nav-dropdown-item-desc">The #1 job board for creatives</p>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" fill="none" class="site-nav-dropdown-arrow icon-12 fill-current">
<path d="M11.7803 6.53033C12.0732 6.23744 12.0732 5.76257 11.7803 5.46967L7.18414 0.873479C6.89124 0.580585 6.41637 0.580585 6.12348 0.873478C5.83058 1.16637 5.83058 1.64125 6.12348 1.93414L9.43934 5.25H0.75C0.335787 5.25 0 5.58579 0 6C0 6.41422 0.335787 6.75 0.75 6.75H9.43934L6.12348 10.0659C5.83058 10.3588 5.83058 10.8336 6.12348 11.1265C6.41637 11.4194 6.89124 11.4194 7.18414 11.1265L11.7803 6.53033Z"></path>
</svg>

</a>            </li>
            <li class="site-nav-dropdown-item green">
              <a class="site-nav-dropdown-item-padding site-nav-dropdown-item-hover flex" data-site-nav-element="Post a Freelance Project" href="https://dribbble.com/freelance-jobs">
                <div class="site-nav-dropdown-icon-container">
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" class="site-nav-dropdown-icon small-icon">
<path d="M7 0.75L7 13.25M13.25 7L0.75 7" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
</svg>

                </div>
                <div class="site-nav-dropdown-item-text">
                  <div class="site-nav-dropdown-item-title">
                    Post a Freelance Project
                  </div>
                  <p class="site-nav-dropdown-item-desc">Board for freelance &amp; contract work</p>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" fill="none" class="site-nav-dropdown-arrow icon-12 fill-current">
<path d="M11.7803 6.53033C12.0732 6.23744 12.0732 5.76257 11.7803 5.46967L7.18414 0.873479C6.89124 0.580585 6.41637 0.580585 6.12348 0.873478C5.83058 1.16637 5.83058 1.64125 6.12348 1.93414L9.43934 5.25H0.75C0.335787 5.25 0 5.58579 0 6C0 6.41422 0.335787 6.75 0.75 6.75H9.43934L6.12348 10.0659C5.83058 10.3588 5.83058 10.8336 6.12348 11.1265C6.41637 11.4194 6.89124 11.4194 7.18414 11.1265L11.7803 6.53033Z"></path>
</svg>

</a>            </li>
          </div>
        </ul>
      </div>
    </li>
  </ul>
</nav>


    <div class="site-nav-mobile-only align-center site-nav-mobile-menu-toggle" aria-label="Toggle Navigation">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 16" fill="none" role="img" class="icon js-site-nav-mobile-menu-open site-nav-mobile-menu-open icon-18 fill-current">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 2C0 1.37868 0.50368 0.875 1.125 0.875H16.875C17.4963 0.875 18 1.37868 18 2C18 2.62132 17.4963 3.125 16.875 3.125H1.125C0.50368 3.125 0 2.62132 0 2ZM0 8C0 7.37868 0.50368 6.875 1.125 6.875H16.875C17.4963 6.875 18 7.37868 18 8C18 8.62132 17.4963 9.125 16.875 9.125H1.125C0.50368 9.125 0 8.62132 0 8ZM1.125 12.875C0.50368 12.875 0 13.3787 0 14C0 14.6213 0.50368 15.125 1.125 15.125H16.875C17.4963 15.125 18 14.6213 18 14C18 13.3787 17.4963 12.875 16.875 12.875H1.125Z"></path>
</svg>

      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 22 22" fill="currentColor" role="img" class="icon js-site-nav-mobile-menu-close site-nav-mobile-menu-close icon-24 fill-current">
<path d="M7.22876 5.81455C6.83824 5.42403 6.20507 5.42403 5.81455 5.81455C5.42402 6.20507 5.42402 6.83824 5.81455 7.22876L9.58578 11L5.81455 14.7712C5.42402 15.1618 5.42402 15.7949 5.81455 16.1854C6.20507 16.576 6.83824 16.576 7.22876 16.1854L11 12.4142L14.7712 16.1854C15.1618 16.576 15.7949 16.576 16.1854 16.1854C16.576 15.7949 16.576 15.1618 16.1854 14.7712L12.4142 11L16.1854 7.22876C16.576 6.83824 16.576 6.20507 16.1854 5.81455C15.7949 5.42403 15.1618 5.42403 14.7712 5.81455L11 9.58579L7.22876 5.81455Z"></path>
</svg>

    </div>

    <div class="site-nav-mobile-only site-nav-mobile-logo" data-site-nav-category="Top Nav">
      <a href="https://dribbble.com/" data-site-nav-element="Logo">
        <svg xmlns="http://www.w3.org/2000/svg" width="76" height="30" viewBox="0 0 76 19" fill="none" class="site-nav-logo fill-current">
<title>Dribbble: the community for graphic design</title>
<path fill-rule="evenodd" clip-rule="evenodd" d="M75.8822 14.657C72.7063 20.0415 67.6766 18.4791 66.166 17.2558C65.5231 17.692 64.3958 18.6481 62.8926 18.5377C59.6913 18.3027 58.5449 13.7279 58.5449 13.7279C58.5679 13.7462 57.5913 14.0649 57.0635 14.0592C57.0567 15.4008 55.897 18.6056 52.7672 18.5646C49.2551 18.5188 48.5411 13.2864 48.5411 13.2864C48.5411 13.2864 48.1916 13.7717 46.8627 14.2551C46.9296 13.2244 46.8807 18.4077 42.6713 18.4839C39.3435 18.5442 38.4452 13.2057 38.4452 13.2057C38.4452 13.2057 37.8679 13.8054 36.7491 14.0134C36.8202 12.9659 36.7046 18.5015 32.4947 18.4839C29.6497 18.4721 28.6775 15.1954 28.7531 14.7406C28.8496 14.161 27.7916 18.5654 25.0281 18.4968C23.8877 18.4633 23.0375 17.6376 22.504 16.5368C21.7898 17.354 20.7529 18.4968 19.5897 18.4968C17.5017 18.4968 16.5812 16.7504 16.7371 11.7624C16.7504 11.1708 16.7077 10.9381 16.1196 10.8496C15.7666 10.7907 15.4051 10.6792 15.0226 10.6204C14.9 11.0295 13.8602 18.3637 10.2847 18.5029C9.08519 18.5496 8.4293 17.5105 7.89066 16.7393C7.06497 17.8316 5.97501 18.5377 4.42227 18.5377C1.79205 18.5377 0 16.4114 0 13.7885C0 11.1655 1.79205 9.03942 4.42227 9.03942C4.88732 9.03942 5.01787 9.10608 5.44272 9.23004C4.569 1.27504 6.63238 0.0317866 8.43739 0.0317866C10.1703 0.0317866 13.1308 4.05384 8.96512 14.2559C9.88998 17.2989 11.8838 17.1268 12.8419 10.8626C13.0369 9.58927 12.5155 7.87099 13.3265 7.63117C14.809 7.19289 14.9663 8.50787 15.6614 8.72697C16.3964 8.95853 16.8254 8.93592 17.531 9.08327C18.7367 9.31873 19.2072 9.96643 19.0603 11.409C18.8839 13.2343 18.5753 15.891 19.5162 16.2148C20.1947 16.45 21.4335 15.0429 21.6509 14.273C21.8682 13.5031 21.9136 13.2396 21.9329 12.6749C21.9623 11.468 21.9992 10.5833 22.205 9.67055C22.2931 9.31736 22.3935 9.08347 22.7931 9.06748C23.1219 9.0591 23.7232 8.96009 23.9879 9.16611C24.3407 9.43119 24.2966 9.70017 24.2561 10.4081C23.8458 20.5015 27.0038 15.4628 27.9454 11.4283C27.6101 6.86623 27.8403 0.115326 30.6991 0.00210112C32.1859 -0.0567822 32.8432 1.13431 32.9155 2.02335C33.12 4.53433 31.9745 8.69372 30.468 11.4909C29.607 17.1984 34.2325 18.3269 34.9722 13.7712C33.762 13.1958 32.4541 10.8668 33.5184 9.73181C34.1156 9.09483 36.6015 10.0099 36.6422 12.0057C37.8616 11.6796 38.0244 10.9911 38.0413 11.1052C37.7061 6.54312 38.017 0.115385 40.876 0.00215941C42.3626 -0.0567239 43.0198 1.13437 43.0921 2.02341C43.2966 4.53438 42.1511 8.69378 40.6448 11.491C39.7837 17.1984 44.4093 18.327 45.1488 13.7713C44.2528 13.5984 42.3614 11.1212 43.4527 9.73187C44.0359 8.98944 46.5127 10.5334 46.79 12.0718C47.9614 11.7403 48.1205 11.0737 48.1373 11.1859C47.802 6.62397 48.1129 0.196235 50.9719 0.0830097C52.4585 0.0241264 53.1157 1.21522 53.188 2.10426C53.3925 4.61523 52.247 8.77471 50.7405 11.5719C49.8796 17.2794 54.5051 18.4077 55.2448 13.852C54.0135 13.647 52.2636 11.0314 53.672 9.69333C54.2347 9.15869 56.3848 10.5465 56.8881 12.1298C57.5874 12.1029 58.0227 11.8617 58.116 11.8374C56.9996 6.4818 57.8307 0.0558781 60.9062 0.00223793C62.5685 -0.0267262 64.1936 0.900905 63.4803 5.99604C62.7994 10.8574 60.3519 12.8975 60.3576 12.9287C60.5 13.5111 61.7559 18.3851 64.9185 15.8134C64.7548 15.4427 64.5909 15.064 64.4993 14.6052C63.9751 11.9327 65.0047 8.91409 67.8032 8.42622C69.4066 8.14671 70.917 8.92734 71.1558 10.6872C71.5487 13.5669 68.9484 15.6524 67.9596 16.1048C67.5167 15.8532 71.9742 18.712 74.6196 12.9829C74.773 12.6558 74.9579 12.6835 75.1975 12.8521C75.3667 12.9712 76.3305 13.8842 75.8822 14.657ZM6.33552 13.0809C6.20092 12.6785 5.92469 11.7918 5.82701 11.4076C5.28905 10.9398 4.90507 10.8638 4.21455 10.8638C2.68 10.8638 1.77679 12.2826 1.77679 13.8125C1.77679 15.3423 2.76077 16.7613 4.29533 16.7613C5.6252 16.7613 6.63735 15.8571 6.94615 14.5771C6.73001 14.0781 6.5156 13.6195 6.33552 13.0809ZM8.39979 2.13753C7.32153 2.13753 6.95576 4.70973 7.02674 6.90406C7.08953 8.84411 7.63164 10.5521 7.91286 11.3174C7.98426 11.4133 7.971 11.3509 8.03474 11.4526C9.90694 7.35053 9.13041 2.13753 8.39979 2.13753ZM30.8531 2.21833C29.4595 2.0585 29.4685 7.86389 29.6415 9.16611C30.3653 8.00823 31.6226 2.53009 30.8531 2.21833ZM41.0297 2.21833C39.6361 2.0585 39.6451 7.86389 39.8182 9.16611C40.5419 8.00823 41.7993 2.53009 41.0297 2.21833ZM51.1257 2.2991C49.732 2.13927 49.741 7.94469 49.9141 9.247C50.6378 8.08902 51.8952 2.61084 51.1257 2.2991ZM61.06 1.87849C58.8887 2.31451 59.3943 9.55399 59.7241 10.8237C62.2817 7.31085 62.3344 1.74654 61.06 1.87849ZM69.1975 10.9254C69.0871 10.4638 68.5351 10.2171 68.1541 10.2779C67.0639 10.4268 66.0065 11.7846 66.4158 13.8518C66.5069 14.3122 66.7349 14.736 66.7312 14.7219C69.1711 13.085 69.3785 11.7951 69.1975 10.9254ZM23.2205 7.46958C22.9089 7.46964 22.6043 7.37729 22.3452 7.20422C22.0861 7.03114 21.8841 6.78512 21.7648 6.49725C21.6455 6.20939 21.6143 5.89261 21.6751 5.587C21.7358 5.28138 21.8859 5.00065 22.1062 4.7803C22.3265 4.55995 22.6072 4.40989 22.9128 4.34909C23.2184 4.28829 23.5352 4.31948 23.8231 4.43873C24.111 4.55797 24.357 4.75991 24.5301 5.019C24.7032 5.27809 24.7956 5.5827 24.7956 5.89429C24.7956 6.10115 24.7549 6.30599 24.6757 6.4971C24.5966 6.68822 24.4806 6.86187 24.3343 7.00815C24.188 7.15443 24.0144 7.27047 23.8233 7.34964C23.6322 7.42882 23.4274 7.46957 23.2205 7.46958Z" fill="#0D0C22"></path>
</svg>

        <span class="accessibility-text">Back to home page</span>
      </a>
    </div>

    
<ul class="site-nav-actions" data-site-nav-category="Top Nav">


  <li class="js-site-nav-search site-nav-actions-item site-nav-desktop-only">
    <a class="" data-site-nav-element="Search" href="https://dribbble.com/search">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" role="img" class="icon icon-18 fill-current">
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.6002 12.0498C9.49758 12.8568 8.13777 13.3333 6.66667 13.3333C2.98477 13.3333 0 10.3486 0 6.66667C0 2.98477 2.98477 0 6.66667 0C10.3486 0 13.3333 2.98477 13.3333 6.66667C13.3333 8.15637 12.8447 9.53194 12.019 10.6419C12.0265 10.6489 12.0338 10.656 12.0411 10.6633L15.2935 13.9157C15.6841 14.3063 15.6841 14.9394 15.2935 15.33C14.903 15.7205 14.2699 15.7205 13.8793 15.33L10.6269 12.0775C10.6178 12.0684 10.6089 12.0592 10.6002 12.0498ZM11.3333 6.66667C11.3333 9.244 9.244 11.3333 6.66667 11.3333C4.08934 11.3333 2 9.244 2 6.66667C2 4.08934 4.08934 2 6.66667 2C9.244 2 11.3333 4.08934 11.3333 6.66667Z"></path>
</svg>

      <span class="accessibility-text">Search</span>
</a>  </li>

      <li class="js-site-nav-sign-in site-nav-actions-item less-margin font-body-small">
        <a data-site-nav-element="Sign in" href="https://dribbble.com/session/new?return_to=%2Ftags%2Ffind_nearby_places">Sign in</a>
      </li>
      <li class="display-flex align-center site-nav-desktop-only color-deep-blue-sea-light-40">/</li>
      <li class="js-site-nav-sign-up site-nav-actions-item less-margin site-nav-desktop-only font-body-small">
        <a data-site-nav-element="Sign Up" href="https://dribbble.com/signup/new">Sign up</a>
      </li>
      <li class="site-nav-actions-item show-medium-only">
        <a class="form-sub" data-site-nav-element="Post a Freelance Project" href="https://dribbble.com/freelance-jobs">Start a project</a>
      </li>
      <li class="site-nav-actions-item show-desktop-only">
        <a class="form-sub" data-site-nav-element="Post a Freelance Project" href="https://dribbble.com/freelance-jobs">Start a free project</a>
      </li>
</ul>

  </div>

  <div class="site-nav-mobile-menu js-site-nav-mobile-menu">
  <nav aria-label="primary">
    <ul>
      <li>
        <form class="site-nav-mobile-menu-search" action="https://dribbble.com/search">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" role="img" class="icon site-nav-mobile-search-icon fill-current icon-16">
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.6002 12.0498C9.49758 12.8568 8.13777 13.3333 6.66667 13.3333C2.98477 13.3333 0 10.3486 0 6.66667C0 2.98477 2.98477 0 6.66667 0C10.3486 0 13.3333 2.98477 13.3333 6.66667C13.3333 8.15637 12.8447 9.53194 12.019 10.6419C12.0265 10.6489 12.0338 10.656 12.0411 10.6633L15.2935 13.9157C15.6841 14.3063 15.6841 14.9394 15.2935 15.33C14.903 15.7205 14.2699 15.7205 13.8793 15.33L10.6269 12.0775C10.6178 12.0684 10.6089 12.0592 10.6002 12.0498ZM11.3333 6.66667C11.3333 9.244 9.244 11.3333 6.66667 11.3333C4.08934 11.3333 2 9.244 2 6.66667C2 4.08934 4.08934 2 6.66667 2C9.244 2 11.3333 4.08934 11.3333 6.66667Z"></path>
</svg>

          <input class="site-nav-mobile-search-input" type="search" name="q" placeholder="Search" autocomplete="off" value="">
          
        </form>
      </li>

      <li class="site-nav-mobile-dropdown">
        <input type="checkbox" name="dropdown" id="inspiration">
        <label for="inspiration" class="site-nav-mobile-list-item">
          Inspiration
        </label>
        <ul class="site-nav-mobile-dropdown-list">
          <div class="site-nav-mobile-dropdown-list-container" data-site-nav-category="Inspiration">
            <li><a data-site-nav-element="Explore Design Work" href="https://dribbble.com/shots/popular">Explore Design Work</a></li>
            <li>
              <a class="flex align-center" data-site-nav-element="New &amp; Noteworthy" href="https://dribbble.com/shots/recent">
                New &amp; Noteworthy
</a>            </li>
            <li><a data-site-nav-element="Playoffs" href="https://dribbble.com/shots?list=playoffs">Playoffs</a></li>
            <li><a data-site-nav-element="Blog" href="https://dribbble.com/stories">Blog</a></li>
          </div>
        </ul>
      </li>
       <li class="site-nav-mobile-dropdown">
        <input type="checkbox" name="dropdown" id="find-work">
        <label for="find-work" class="site-nav-mobile-list-item">
          Find Work
        </label>
        <ul class="site-nav-mobile-dropdown-list">
          <div class="site-nav-mobile-dropdown-list-container" data-site-nav-category="Find Work">
            <li><a data-site-nav-element="Job Board" href="https://dribbble.com/jobs">Job Board</a></li>
            <li>
              <a class="flex align-center" data-site-nav-element="Freelance Projects" href="https://dribbble.com/freelance-jobs">
                Freelance Projects
                <span class="pro-flag margin-l-8">Pro+</span>
</a>            </li>
            <li>
              <a class="flex align-center" data-site-nav-element="Projects" href="https://dribbble.com/projects">
                Want freelance design projects?
</a>            </li>
            <li>
              <a class="flex align-center" data-site-nav-element="Pitch" href="https://dribbble.com/pro/pitch">
                Personalize your profile with video
</a>            </li>
          </div>
        </ul>
      </li>

      <li class="site-nav-mobile-dropdown">
        <input type="checkbox" name="dropdown" id="learn-design">
        <label for="learn-design" class="site-nav-mobile-list-item">
          Learn Design
        </label>
        <ul class="site-nav-mobile-dropdown-list" data-site-nav-category="Learn Design">
          <div class="site-nav-mobile-dropdown-list-container">
            <li><a data-site-nav-element="Product Design Course" href="https://dribbble.com/courses/product-design">Certified Product Design Course</a></li>
            <li><a data-site-nav-element="Live Workshops" href="https://dribbble.com/learn">Live Workshops</a></li>
          </div>
        </ul>
      </li>

        <li data-site-nav-category="Top Nav">
          <a class="site-nav-mobile-list-item" data-site-nav-element="Go Pro" href="https://dribbble.com/pro">Go Pro</a>
        </li>

      <li class="site-nav-mobile-dropdown">
        <input type="checkbox" name="dropdown" id="marketplace">
        <label for="marketplace" class="site-nav-mobile-list-item">
          Marketplace
        </label>
        <ul class="site-nav-mobile-dropdown-list" data-site-nav-category="Marketplace">
          <div class="site-nav-mobile-dropdown-list-container">
            <li><a data-site-nav-element="Discover" href="https://dribbble.com/marketplace">Discover</a></li>
            <li><a data-site-nav-element="Graphics" target="_blank" href="https://dribbble.com/marketplace/graphics">Graphics</a></li>
            <li><a data-site-nav-element="Fonts" target="_blank" href="https://dribbble.com/marketplace/fonts">Fonts</a></li>
            <li><a data-site-nav-element="3D" target="_blank" href="https://dribbble.com/marketplace/3d">3D</a></li>
            <li><a data-site-nav-element="Templates" target="_blank" href="https://dribbble.com/marketplace/templates">Templates</a></li>
            <li><a data-site-nav-element="Add-Ons" target="_blank" href="https://dribbble.com/marketplace/add-ons">Add-Ons</a></li>
            <li><a data-site-nav-element="Web Themes" target="_blank" href="https://dribbble.com/marketplace/themes">Web Themes</a></li>
            <li><a data-site-nav-element="Open a Shop" target="_blank" href="https://creativemarket.com/sell?utm_source=dribbble&amp;utm_medium=referral&amp;utm_campaign=header">Open a Shop</a></li>
          </div>
        </ul>
      </li>

      <li class="site-nav-mobile-dropdown">
        <input type="checkbox" name="dropdown" id="hire-designers">
        <label for="hire-designers" class="site-nav-mobile-list-item">
          Hire Designers
        </label>
        <ul class="site-nav-mobile-dropdown-list">
          <div class="site-nav-mobile-dropdown-list-container" data-site-nav-category="Hire Designers">
            <li><a data-site-nav-element="About Dribbble Hiring" href="https://dribbble.com/hiring">About Dribbble Hiring</a></li>
            <li>
              <a class="flex align-center" data-site-nav-element="Designer Search" href="https://dribbble.com/designers">
                Designer Search
</a>            </li>
            <li><a data-billing-interval="quarterly" data-site-nav-element="List my job opening" href="https://dribbble.com/jobs/new">List my Job Opening</a></li>
            <li><a data-site-nav-element="Post a Freelance Project" href="https://dribbble.com/freelance-jobs">Post a Freelance Project</a></li>
          </div>
        </ul>
      </li>


    </ul>
  </nav>

</div>

</div>


<div class="ajax notice hide">
  <h2></h2>
</div>




<div class="notice-alert">
  <h3><!-- message goes here --></h3>
  <div class="close" aria-label="close">
    <img class="lazyload" data-src="https://cdn.dribbble.com/assets/icon-shot-x-light-f3676a5f3f6b7d0902be702c858a78e3de991957bbb7cec07daef7150e284fd8.png" width="16" height="16" alt="close">
  </div>
</div>

  <div id="g_id_onload" data-client_id="32073492121-s6ur8ti01mh34gq2bpbufb8ujdfrpn4v.apps.googleusercontent.com" data-login_uri="https://dribbble.com/auth/google_one_tap/callback" data-ux_mode="redirect"></div>


<div id="wrap">
  
    <div class="filter-subnav container-fluid">
    <div class="filter-subnav-inner flex flex-row items-center justify-between">
      <div class="filter-views js-shot-views">
        <span class="btn-dropdown btn-dropdown-neue">
          <a class="form-btn outlined btn-dropdown-link" data-track-sub-nav="" href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail#" data-dropdown-state="closed">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" role="img" class="icon btn-dropdown-caret">
<path d="M21.5265 8.77171C22.1578 8.13764 22.1578 7.10962 21.5265 6.47555C20.8951 5.84148 19.8714 5.84148 19.24 6.47555L11.9999 13.7465L4.75996 6.47573C4.12858 5.84166 3.10492 5.84166 2.47354 6.47573C1.84215 7.10979 1.84215 8.13782 2.47354 8.77188L10.8332 17.1671C10.8408 17.1751 10.8486 17.183 10.8565 17.1909C11.0636 17.399 11.313 17.5388 11.577 17.6103C11.5834 17.6121 11.5899 17.6138 11.5964 17.6154C12.132 17.7536 12.7242 17.6122 13.1435 17.1911C13.1539 17.1807 13.1641 17.1702 13.1742 17.1596L21.5265 8.77171Z"></path>
</svg>

            Popular
          </a>
          <div class="btn-dropdown-options">
            <ul>
                <li class="active">
                  <a href="https://dribbble.com/tags/find_nearby_places">Popular</a>
                </li>
                <li class="">
                  <a href="https://dribbble.com/tags/find_nearby_places?s=latest">Latest</a>
                </li>
            </ul>
          </div>
        </span>
      </div>
    </div>
  </div>

  

  <div id="wrap-inner" class="flushed">
    <div id="content" role="main">
      






<h1 class="text-align-center text-size-20 text-size-32-ns text-weight-700 mb10">
  Find Nearby Places
</h1>

<h2 class="text-size-16 text-weight-400 lh-copy text-align-center text-medium mb40">
    Inspirational designs,
  illustrations, and graphic elements from the world’s best designers.
  <br>Want more inspiration? Browse our <a href="https://dribbble.com/search/find%20nearby%20places">search results</a>...
</h2>

<div id="main" class="main-full">
  

<ol class="js-thumbnail-grid shots-grid group dribbbles container-fluid is-scrolled">
    <script>if (!document.querySelector(".js-thumbnail-grid")){ window.location.reload(); }</script>





<li id="screenshot-14728277" data-thumbnail-id="14728277" class="shot-thumbnail js-thumbnail shot-thumbnail-container      " data-ad-data="" data-boost-id="" data-is-boost-fallback="">
  

<div class="js-thumbnail-base shot-thumbnail-base disabled-shot-section dribbble-shot dribbble  multi-shot">
      <figure style="background-color: #C4E5B2" class="js-thumbnail-placeholder shot-thumbnail-placeholder">
  <noscript>
      <img src="https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=400x300" alt="Holeswing - Golf Courses List and Detail app clean country club golf golf club golfapp golfcourse golfrange ios mobile mobile app mobile ui mobilegolf nearby places round scoring sportsapp uidesign uxdesign">
  </noscript>
  <img alt="Holeswing - Golf Courses List and Detail app clean country club golf golf club golfapp golfcourse golfrange ios mobile mobile app mobile ui mobilegolf nearby places round scoring sportsapp uidesign uxdesign" width="330" height="247" srcset="https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w" sizes="(max-width: 500px) 320px, (min-width: 501px) and (max-width: 615px) 600px, 400px" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/8937cca595068ed1cadc1ad15577470d.png">
</figure>


    <div class="shot-thumbnail-extras">
        <div class="loading-indicator shot-thumbnail-extras-icon"></div>
    <a class="animated-target" href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail">
      <span class="accessibility-text">Shot Link</span>
</a>
    </div>

      <a class="shot-thumbnail-link dribbble-link js-shot-link" href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail">
        <span class="accessibility-text">View Holeswing - Golf Courses List and Detail</span>
</a>
    <div class="shot-thumbnail-overlay">
      <div class="shot-thumbnail-overlay-content">
            <div class="shot-title">Holeswing - Golf Courses List and Detail</div>

            <ul class="js-dribbble-shot-actions shot-actions-container">
                <li data-bucket-container="true" class="shot-action">
                  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m22 5h-11l-2-3h-7c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h20c1.104 0 2-.896 2-2v-13c0-1.104-.896-2-2-2zm-6 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3v-3c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z"></path></svg>

</a>
                </li>

                

  <div class="like-action-14728277   shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(14728277, {
          likes_count: 310,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

            </ul>
      </div>
    </div>
</div>


    <div class="shot-details-container js-shot-details-container" style="visibility: visible;">
        <div class="user-information">
          <a class="hoverable url" rel="contact" href="https://dribbble.com/agensip">
            <img class="photo lazyloaded" alt="⚡️Agensip UI UX Agency" width="24" height="24" data-src="https://cdn.dribbble.com/users/2547736/avatars/mini/0c4aff997b5469101760bff0f3c5c535.png?1536649020" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/0c4aff997b5469101760bff0f3c5c535.png">
            <span class="display-name">⚡️Agensip UI UX Agency</span>
</a>          <a class="badge-link" href="https://dribbble.com/pro">
  <span class="badge badge-team">Team</span>
</a>
        </div>
        <div class="shot-statistics-container js-shot-statistics">

    <div class="shot-statistic js-shot-likes-container">
        

  <div class="like-action-14728277  compact-true shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(14728277, {
          likes_count: 310,
          liked: false
      });
      }
    </script>
      <a class=" tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current shot-tools-icon"><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

      <span class="js-shot-likes-count color-deep-blue-sea-light-20 font-weight-500">310</span>
    </div>
  <div class="shot-statistic js-shot-views-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11" viewBox="0 0 14 11" fill="none" role="img" class="icon fill-current shot-tools-icon">
<path d="M7 3.45833C5.87242 3.45833 4.95833 4.37242 4.95833 5.5C4.95833 6.62758 5.87242 7.54167 7 7.54167C8.12758 7.54167 9.04167 6.62758 9.04167 5.5C9.04167 4.37242 8.12758 3.45833 7 3.45833ZM7 0.25C3.13425 0.25 0 4.625 0 5.5C0 6.375 3.13425 10.75 7 10.75C10.8657 10.75 14 6.375 14 5.5C14 4.625 10.8657 0.25 7 0.25ZM7 8.70833C5.22783 8.70833 3.79167 7.27217 3.79167 5.5C3.79167 3.72783 5.22783 2.29167 7 2.29167C8.77217 2.29167 10.2083 3.72783 10.2083 5.5C10.2083 7.27217 8.77217 8.70833 7 8.70833Z"></path>
</svg>

    <span class="js-shot-views-count color-deep-blue-sea-light-20 font-weight-500">52.2k</span>
  </div>
</div>

    </div>



</li>


<li id="screenshot-16403200" data-thumbnail-id="16403200" class="shot-thumbnail js-thumbnail shot-thumbnail-container      " data-ad-data="" data-boost-id="" data-is-boost-fallback="">
  

<div class="js-thumbnail-base shot-thumbnail-base disabled-shot-section dribbble-shot dribbble  ">
      <figure style="background-color: #E7E8F2" class="js-thumbnail-placeholder shot-thumbnail-placeholder">
  <noscript>
      <img src="https://cdn.dribbble.com/users/1624253/screenshots/16403200/media/2c84c15cd0d1b03fce487249c415aa35.png?compress=1&amp;resize=400x300" alt="Parking Mobile App Exploration bike book booking bus car find parking nearby park park park app park uiux parking parking app parking space route van vehicle vr park">
  </noscript>
  <img alt="Parking Mobile App Exploration bike book booking bus car find parking nearby park park park app park uiux parking parking app parking space route van vehicle vr park" width="330" height="247" srcset="https://cdn.dribbble.com/users/1624253/screenshots/16403200/media/2c84c15cd0d1b03fce487249c415aa35.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/1624253/screenshots/16403200/media/2c84c15cd0d1b03fce487249c415aa35.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/1624253/screenshots/16403200/media/2c84c15cd0d1b03fce487249c415aa35.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/1624253/screenshots/16403200/media/2c84c15cd0d1b03fce487249c415aa35.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/1624253/screenshots/16403200/media/2c84c15cd0d1b03fce487249c415aa35.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/1624253/screenshots/16403200/media/2c84c15cd0d1b03fce487249c415aa35.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/1624253/screenshots/16403200/media/2c84c15cd0d1b03fce487249c415aa35.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/1624253/screenshots/16403200/media/2c84c15cd0d1b03fce487249c415aa35.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/1624253/screenshots/16403200/media/2c84c15cd0d1b03fce487249c415aa35.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w" sizes="(max-width: 500px) 320px, (min-width: 501px) and (max-width: 615px) 600px, 400px" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/2c84c15cd0d1b03fce487249c415aa35.png">
</figure>


    <div class="shot-thumbnail-extras">
          <div class="has-rebound shot-thumbnail-extras-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="12" viewBox="0 0 16 12" fill="none" role="img" class="icon fill-current">
<path d="M6.40436 0.522824C6.936 0.0682789 7.79709 0.0682789 8.32873 0.522824C8.59491 0.749733 8.72873 1.04792 8.72727 1.34537V3.12355C12.9542 3.45373 16 6.65955 16 11.6363C16 11.6363 13.5076 9.16719 8.72727 8.93228V10.6545C8.72873 10.9526 8.59491 11.2501 8.32873 11.477C8.06327 11.7039 7.71491 11.8181 7.36654 11.8181C7.01818 11.8181 6.67054 11.7046 6.40436 11.477L-4.76837e-07 5.99992L6.40436 0.522824Z" fill="white"></path>
</svg>

            <span class="has-rebound-count">1</span>
          </div>
      
    </div>

      <a class="shot-thumbnail-link dribbble-link js-shot-link" href="https://dribbble.com/shots/16403200-Parking-Mobile-App-Exploration">
        <span class="accessibility-text">View Parking Mobile App Exploration</span>
</a>
    <div class="shot-thumbnail-overlay">
      <div class="shot-thumbnail-overlay-content">
            <div class="shot-title">Parking Mobile App Exploration</div>

            <ul class="js-dribbble-shot-actions shot-actions-container">
                <li data-bucket-container="true" class="shot-action">
                  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m22 5h-11l-2-3h-7c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h20c1.104 0 2-.896 2-2v-13c0-1.104-.896-2-2-2zm-6 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3v-3c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z"></path></svg>

</a>
                </li>

                

  <div class="like-action-16403200   shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(16403200, {
          likes_count: 313,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

            </ul>
      </div>
    </div>
</div>


    <div class="shot-details-container js-shot-details-container" style="visibility: visible;">
        <div class="user-information">
          <a class="hoverable url" rel="contact" href="https://dribbble.com/vektora">
            <img class="photo lazyloaded" alt="Vektora" width="24" height="24" data-src="https://cdn.dribbble.com/users/6567474/avatars/mini/b849c692c6c9fc9cfdca178b90e354d2.png?1607746416" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/b849c692c6c9fc9cfdca178b90e354d2.png">
            <span class="display-name">Vektora</span>
</a>          <a class="badge-link" href="https://dribbble.com/pro">
  <span class="badge badge-team">Team</span>
</a>
        </div>
        <div class="shot-statistics-container js-shot-statistics">

    <div class="shot-statistic js-shot-likes-container">
        

  <div class="like-action-16403200  compact-true shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(16403200, {
          likes_count: 313,
          liked: false
      });
      }
    </script>
      <a class=" tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current shot-tools-icon"><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

      <span class="js-shot-likes-count color-deep-blue-sea-light-20 font-weight-500">313</span>
    </div>
  <div class="shot-statistic js-shot-views-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11" viewBox="0 0 14 11" fill="none" role="img" class="icon fill-current shot-tools-icon">
<path d="M7 3.45833C5.87242 3.45833 4.95833 4.37242 4.95833 5.5C4.95833 6.62758 5.87242 7.54167 7 7.54167C8.12758 7.54167 9.04167 6.62758 9.04167 5.5C9.04167 4.37242 8.12758 3.45833 7 3.45833ZM7 0.25C3.13425 0.25 0 4.625 0 5.5C0 6.375 3.13425 10.75 7 10.75C10.8657 10.75 14 6.375 14 5.5C14 4.625 10.8657 0.25 7 0.25ZM7 8.70833C5.22783 8.70833 3.79167 7.27217 3.79167 5.5C3.79167 3.72783 5.22783 2.29167 7 2.29167C8.77217 2.29167 10.2083 3.72783 10.2083 5.5C10.2083 7.27217 8.77217 8.70833 7 8.70833Z"></path>
</svg>

    <span class="js-shot-views-count color-deep-blue-sea-light-20 font-weight-500">76.6k</span>
  </div>
</div>

    </div>



</li>


<script>
var newestShots = [{
    id: 14728277,
    title: "Holeswing - Golf Courses List and Detail",
    path: "/shots/14728277-Holeswing-Golf-Courses-List-and-Detail",
    published_at: 'December 08, 2020',
    is_rebound: false,
    rebounds_count: 0,
    attachments_count: 0,
    view_count: '52.2k',
    comments_count: '33',
    likes_count: '310',
    liked: false,
    ga: [["agensip","UA-120360070-3"],["aveef","UA-120360070-1"]]
  },{
    id: 16403200,
    title: "Parking Mobile App Exploration",
    path: "/shots/16403200-Parking-Mobile-App-Exploration",
    published_at: 'September 08, 2021',
    is_rebound: false,
    rebounds_count: 1,
    attachments_count: 0,
    view_count: '76.6k',
    comments_count: '31',
    likes_count: '313',
    liked: false,
    ga: []
  }];

if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
  Dribbble.Shots.add(newestShots);
  Dribbble.TeaserStats.init(newestShots);

    Dribbble.Thumbnails.cursor = '1607482906.218592';
}
else {
  if (typeof newShots === "undefined") {
    var newShots = [];
  }
  newShots = newShots.concat(newestShots);
}

</script>


  <script>if (!document.querySelector(".js-thumbnail-grid")){ window.location.reload(); }</script>





<li id="screenshot-16490714" data-thumbnail-id="16490714" class="shot-thumbnail js-thumbnail shot-thumbnail-container      " data-ad-data="" data-boost-id="" data-is-boost-fallback="">
  

<div class="js-thumbnail-base shot-thumbnail-base disabled-shot-section dribbble-shot dribbble  ">
      <figure style="background-color: #ECE9F6" class="js-thumbnail-placeholder shot-thumbnail-placeholder">
  <noscript>
      <img src="https://cdn.dribbble.com/users/1624253/screenshots/16490714/media/1c55a02b19fa29b37967ede1acadbeb7.png?compress=1&amp;resize=400x300" alt="Parking Mobile App Exploration bike book booking bus car find parking mobile parking nearby park park park app park uiux parking parking app parking space route ui ux van vehicle vr park">
  </noscript>
  <img alt="Parking Mobile App Exploration bike book booking bus car find parking mobile parking nearby park park park app park uiux parking parking app parking space route ui ux van vehicle vr park" width="330" height="247" data-srcset="https://cdn.dribbble.com/users/1624253/screenshots/16490714/media/1c55a02b19fa29b37967ede1acadbeb7.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/1624253/screenshots/16490714/media/1c55a02b19fa29b37967ede1acadbeb7.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/1624253/screenshots/16490714/media/1c55a02b19fa29b37967ede1acadbeb7.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/1624253/screenshots/16490714/media/1c55a02b19fa29b37967ede1acadbeb7.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/1624253/screenshots/16490714/media/1c55a02b19fa29b37967ede1acadbeb7.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/1624253/screenshots/16490714/media/1c55a02b19fa29b37967ede1acadbeb7.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/1624253/screenshots/16490714/media/1c55a02b19fa29b37967ede1acadbeb7.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/1624253/screenshots/16490714/media/1c55a02b19fa29b37967ede1acadbeb7.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/1624253/screenshots/16490714/media/1c55a02b19fa29b37967ede1acadbeb7.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w" data-src="https://cdn.dribbble.com/users/1624253/screenshots/16490714/media/1c55a02b19fa29b37967ede1acadbeb7.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class="lazyautosizes lazyloaded" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/1c55a02b19fa29b37967ede1acadbeb7.png" sizes="295px" srcset="https://cdn.dribbble.com/users/1624253/screenshots/16490714/media/1c55a02b19fa29b37967ede1acadbeb7.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/1624253/screenshots/16490714/media/1c55a02b19fa29b37967ede1acadbeb7.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/1624253/screenshots/16490714/media/1c55a02b19fa29b37967ede1acadbeb7.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/1624253/screenshots/16490714/media/1c55a02b19fa29b37967ede1acadbeb7.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/1624253/screenshots/16490714/media/1c55a02b19fa29b37967ede1acadbeb7.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/1624253/screenshots/16490714/media/1c55a02b19fa29b37967ede1acadbeb7.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/1624253/screenshots/16490714/media/1c55a02b19fa29b37967ede1acadbeb7.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/1624253/screenshots/16490714/media/1c55a02b19fa29b37967ede1acadbeb7.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/1624253/screenshots/16490714/media/1c55a02b19fa29b37967ede1acadbeb7.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w">
</figure>


    <div class="shot-thumbnail-extras">
          <div class="is-rebound shot-thumbnail-extras-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" role="img" class="icon ">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 8C0 12.4183 3.58172 16 8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0C3.58172 0 0 3.58172 0 8ZM8.16504 6.24902V5.06161C8.16585 4.89576 8.09142 4.73031 7.94336 4.60411C7.64766 4.35128 7.16872 4.35128 6.87302 4.60411L3.31087 7.6505L6.87302 10.6969C7.02107 10.8231 7.21443 10.8866 7.40819 10.8866C7.60195 10.8866 7.79531 10.8235 7.94336 10.6969C8.09142 10.5707 8.16585 10.4048 8.16504 10.2394V8.98145C10.8239 9.1121 12.2102 10.7855 12.2102 10.7855C12.2102 8.0174 10.5161 6.43227 8.16504 6.24902Z" fill="white"></path>
</svg>

          </div>
      
    </div>

      <a class="shot-thumbnail-link dribbble-link js-shot-link" href="https://dribbble.com/shots/16490714-Parking-Mobile-App-Exploration">
        <span class="accessibility-text">View Parking Mobile App Exploration</span>
</a>
    <div class="shot-thumbnail-overlay">
      <div class="shot-thumbnail-overlay-content">
            <div class="shot-title">Parking Mobile App Exploration</div>

            <ul class="js-dribbble-shot-actions shot-actions-container">
                <li data-bucket-container="true" class="shot-action">
                  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m22 5h-11l-2-3h-7c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h20c1.104 0 2-.896 2-2v-13c0-1.104-.896-2-2-2zm-6 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3v-3c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z"></path></svg>

</a>
                </li>

                

  <div class="like-action-16490714   shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(16490714, {
          likes_count: 320,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

            </ul>
      </div>
    </div>
</div>


    <div class="shot-details-container js-shot-details-container" style="visibility: visible;">
        <div class="user-information">
          <a class="hoverable url" rel="contact" href="https://dribbble.com/vektora">
            <img class="photo lazyloaded" alt="Vektora" width="24" height="24" data-src="https://cdn.dribbble.com/users/6567474/avatars/mini/b849c692c6c9fc9cfdca178b90e354d2.png?1607746416" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/b849c692c6c9fc9cfdca178b90e354d2.png">
            <span class="display-name">Vektora</span>
</a>          <a class="badge-link" href="https://dribbble.com/pro">
  <span class="badge badge-team">Team</span>
</a>
        </div>
        <div class="shot-statistics-container js-shot-statistics">

    <div class="shot-statistic js-shot-likes-container">
        

  <div class="like-action-16490714  compact-true shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(16490714, {
          likes_count: 320,
          liked: false
      });
      }
    </script>
      <a class=" tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current shot-tools-icon"><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

      <span class="js-shot-likes-count color-deep-blue-sea-light-20 font-weight-500">320</span>
    </div>
  <div class="shot-statistic js-shot-views-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11" viewBox="0 0 14 11" fill="none" role="img" class="icon fill-current shot-tools-icon">
<path d="M7 3.45833C5.87242 3.45833 4.95833 4.37242 4.95833 5.5C4.95833 6.62758 5.87242 7.54167 7 7.54167C8.12758 7.54167 9.04167 6.62758 9.04167 5.5C9.04167 4.37242 8.12758 3.45833 7 3.45833ZM7 0.25C3.13425 0.25 0 4.625 0 5.5C0 6.375 3.13425 10.75 7 10.75C10.8657 10.75 14 6.375 14 5.5C14 4.625 10.8657 0.25 7 0.25ZM7 8.70833C5.22783 8.70833 3.79167 7.27217 3.79167 5.5C3.79167 3.72783 5.22783 2.29167 7 2.29167C8.77217 2.29167 10.2083 3.72783 10.2083 5.5C10.2083 7.27217 8.77217 8.70833 7 8.70833Z"></path>
</svg>

    <span class="js-shot-views-count color-deep-blue-sea-light-20 font-weight-500">72.9k</span>
  </div>
</div>

    </div>



</li>


<li id="screenshot-17116279" data-thumbnail-id="17116279" class="shot-thumbnail js-thumbnail shot-thumbnail-container      " data-ad-data="" data-boost-id="" data-is-boost-fallback="">
  

<div class="js-thumbnail-base shot-thumbnail-base disabled-shot-section dribbble-shot dribbble  ">
      <figure style="background-color: #EEF1D6" class="js-thumbnail-placeholder shot-thumbnail-placeholder">
  <noscript>
      <img src="https://cdn.dribbble.com/users/1083899/screenshots/17116279/media/8632b3e14668809861b76b36b24a862e.png?compress=1&amp;resize=400x300" alt="Search items nearby app buy and sell buy things ecommerce find nearby map illustration mobile app search nearby">
  </noscript>
  <img alt="Search items nearby app buy and sell buy things ecommerce find nearby map illustration mobile app search nearby" width="330" height="247" data-srcset="https://cdn.dribbble.com/users/1083899/screenshots/17116279/media/8632b3e14668809861b76b36b24a862e.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/1083899/screenshots/17116279/media/8632b3e14668809861b76b36b24a862e.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/1083899/screenshots/17116279/media/8632b3e14668809861b76b36b24a862e.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/1083899/screenshots/17116279/media/8632b3e14668809861b76b36b24a862e.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/1083899/screenshots/17116279/media/8632b3e14668809861b76b36b24a862e.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/1083899/screenshots/17116279/media/8632b3e14668809861b76b36b24a862e.png?compress=1&amp;resize=800x600&amp;vertical=top 800w" data-src="https://cdn.dribbble.com/users/1083899/screenshots/17116279/media/8632b3e14668809861b76b36b24a862e.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class="lazyautosizes lazyloaded" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/8632b3e14668809861b76b36b24a862e.png" sizes="295px" srcset="https://cdn.dribbble.com/users/1083899/screenshots/17116279/media/8632b3e14668809861b76b36b24a862e.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/1083899/screenshots/17116279/media/8632b3e14668809861b76b36b24a862e.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/1083899/screenshots/17116279/media/8632b3e14668809861b76b36b24a862e.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/1083899/screenshots/17116279/media/8632b3e14668809861b76b36b24a862e.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/1083899/screenshots/17116279/media/8632b3e14668809861b76b36b24a862e.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/1083899/screenshots/17116279/media/8632b3e14668809861b76b36b24a862e.png?compress=1&amp;resize=800x600&amp;vertical=top 800w">
</figure>


    <div class="shot-thumbnail-extras">
      
    </div>

      <a class="shot-thumbnail-link dribbble-link js-shot-link" href="https://dribbble.com/shots/17116279-Search-items-nearby-app">
        <span class="accessibility-text">View Search items nearby app</span>
</a>
    <div class="shot-thumbnail-overlay">
      <div class="shot-thumbnail-overlay-content">
            <div class="shot-title">Search items nearby app</div>

            <ul class="js-dribbble-shot-actions shot-actions-container">
                <li data-bucket-container="true" class="shot-action">
                  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m22 5h-11l-2-3h-7c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h20c1.104 0 2-.896 2-2v-13c0-1.104-.896-2-2-2zm-6 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3v-3c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z"></path></svg>

</a>
                </li>

                

  <div class="like-action-17116279   shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(17116279, {
          likes_count: 32,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

            </ul>
      </div>
    </div>
</div>


    <div class="shot-details-container js-shot-details-container" style="visibility: visible;">
        <div class="user-information">
          <a class="hoverable url" rel="contact" href="https://dribbble.com/adiatmabani">
            <img class="photo lazyloaded" alt="adiatma bani" width="24" height="24" data-src="https://cdn.dribbble.com/users/1083899/avatars/mini/8eac4b930006c41606cced98f012502e.jpg?1589202052" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/8eac4b930006c41606cced98f012502e.jpg">
            <span class="display-name">adiatma bani</span>
</a>          <a class="badge-link" href="https://dribbble.com/pro">
  <span class="badge badge-pro">Pro</span>

</a>
        </div>
        <div class="shot-statistics-container js-shot-statistics">

    <div class="shot-statistic js-shot-likes-container">
        

  <div class="like-action-17116279  compact-true shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(17116279, {
          likes_count: 32,
          liked: false
      });
      }
    </script>
      <a class=" tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current shot-tools-icon"><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

      <span class="js-shot-likes-count color-deep-blue-sea-light-20 font-weight-500">32</span>
    </div>
  <div class="shot-statistic js-shot-views-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11" viewBox="0 0 14 11" fill="none" role="img" class="icon fill-current shot-tools-icon">
<path d="M7 3.45833C5.87242 3.45833 4.95833 4.37242 4.95833 5.5C4.95833 6.62758 5.87242 7.54167 7 7.54167C8.12758 7.54167 9.04167 6.62758 9.04167 5.5C9.04167 4.37242 8.12758 3.45833 7 3.45833ZM7 0.25C3.13425 0.25 0 4.625 0 5.5C0 6.375 3.13425 10.75 7 10.75C10.8657 10.75 14 6.375 14 5.5C14 4.625 10.8657 0.25 7 0.25ZM7 8.70833C5.22783 8.70833 3.79167 7.27217 3.79167 5.5C3.79167 3.72783 5.22783 2.29167 7 2.29167C8.77217 2.29167 10.2083 3.72783 10.2083 5.5C10.2083 7.27217 8.77217 8.70833 7 8.70833Z"></path>
</svg>

    <span class="js-shot-views-count color-deep-blue-sea-light-20 font-weight-500">12.2k</span>
  </div>
</div>

    </div>



</li>


<li id="screenshot-15133535" data-thumbnail-id="15133535" class="shot-thumbnail js-thumbnail shot-thumbnail-container      " data-ad-data="" data-boost-id="" data-is-boost-fallback="">
  

<div class="js-thumbnail-base shot-thumbnail-base disabled-shot-section dribbble-shot dribbble  ">
      <figure style="background-color: #E5E9F3" class="js-thumbnail-placeholder shot-thumbnail-placeholder">
  <noscript>
      <img src="https://cdn.dribbble.com/users/3417184/screenshots/15133535/media/38b3e75c1c0112e5c5bb8638d43d5439.png?compress=1&amp;resize=400x300" alt="Inner. Find out how your feelings matches with people nearby app chat company contacts emotion feelings friend intention interface location log match mention onboarding plans quiz social statistic ui ux">
  </noscript>
  <img alt="Inner. Find out how your feelings matches with people nearby app chat company contacts emotion feelings friend intention interface location log match mention onboarding plans quiz social statistic ui ux" width="330" height="247" data-srcset="https://cdn.dribbble.com/users/3417184/screenshots/15133535/media/38b3e75c1c0112e5c5bb8638d43d5439.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/3417184/screenshots/15133535/media/38b3e75c1c0112e5c5bb8638d43d5439.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/3417184/screenshots/15133535/media/38b3e75c1c0112e5c5bb8638d43d5439.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/3417184/screenshots/15133535/media/38b3e75c1c0112e5c5bb8638d43d5439.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/3417184/screenshots/15133535/media/38b3e75c1c0112e5c5bb8638d43d5439.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/3417184/screenshots/15133535/media/38b3e75c1c0112e5c5bb8638d43d5439.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/3417184/screenshots/15133535/media/38b3e75c1c0112e5c5bb8638d43d5439.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/3417184/screenshots/15133535/media/38b3e75c1c0112e5c5bb8638d43d5439.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/3417184/screenshots/15133535/media/38b3e75c1c0112e5c5bb8638d43d5439.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w" data-src="https://cdn.dribbble.com/users/3417184/screenshots/15133535/media/38b3e75c1c0112e5c5bb8638d43d5439.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class="lazyautosizes lazyloaded" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/38b3e75c1c0112e5c5bb8638d43d5439.png" sizes="295px" srcset="https://cdn.dribbble.com/users/3417184/screenshots/15133535/media/38b3e75c1c0112e5c5bb8638d43d5439.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/3417184/screenshots/15133535/media/38b3e75c1c0112e5c5bb8638d43d5439.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/3417184/screenshots/15133535/media/38b3e75c1c0112e5c5bb8638d43d5439.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/3417184/screenshots/15133535/media/38b3e75c1c0112e5c5bb8638d43d5439.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/3417184/screenshots/15133535/media/38b3e75c1c0112e5c5bb8638d43d5439.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/3417184/screenshots/15133535/media/38b3e75c1c0112e5c5bb8638d43d5439.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/3417184/screenshots/15133535/media/38b3e75c1c0112e5c5bb8638d43d5439.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/3417184/screenshots/15133535/media/38b3e75c1c0112e5c5bb8638d43d5439.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/3417184/screenshots/15133535/media/38b3e75c1c0112e5c5bb8638d43d5439.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w">
</figure>


    <div class="shot-thumbnail-extras">
          <div class="has-rebound shot-thumbnail-extras-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="12" viewBox="0 0 16 12" fill="none" role="img" class="icon fill-current">
<path d="M6.40436 0.522824C6.936 0.0682789 7.79709 0.0682789 8.32873 0.522824C8.59491 0.749733 8.72873 1.04792 8.72727 1.34537V3.12355C12.9542 3.45373 16 6.65955 16 11.6363C16 11.6363 13.5076 9.16719 8.72727 8.93228V10.6545C8.72873 10.9526 8.59491 11.2501 8.32873 11.477C8.06327 11.7039 7.71491 11.8181 7.36654 11.8181C7.01818 11.8181 6.67054 11.7046 6.40436 11.477L-4.76837e-07 5.99992L6.40436 0.522824Z" fill="white"></path>
</svg>

            <span class="has-rebound-count">1</span>
          </div>
      
    </div>

      <a class="shot-thumbnail-link dribbble-link js-shot-link" href="https://dribbble.com/shots/15133535-Inner-Find-out-how-your-feelings-matches-with-people-nearby">
        <span class="accessibility-text">View Inner. Find out how your feelings matches with people nearby</span>
</a>
    <div class="shot-thumbnail-overlay">
      <div class="shot-thumbnail-overlay-content">
            <div class="shot-title">Inner. Find out how your feelings matches with people nearby</div>

            <ul class="js-dribbble-shot-actions shot-actions-container">
                <li data-bucket-container="true" class="shot-action">
                  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m22 5h-11l-2-3h-7c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h20c1.104 0 2-.896 2-2v-13c0-1.104-.896-2-2-2zm-6 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3v-3c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z"></path></svg>

</a>
                </li>

                

  <div class="like-action-15133535   shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(15133535, {
          likes_count: 241,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

            </ul>
      </div>
    </div>
</div>


    <div class="shot-details-container js-shot-details-container" style="visibility: visible;">
        <div class="user-information">
          <a class="hoverable url" rel="contact" href="https://dribbble.com/cre8team">
            <img class="photo lazyloaded" alt="Cre8 Team" width="24" height="24" data-src="https://cdn.dribbble.com/users/6878497/avatars/mini/b91909d451c9e47e3ae10fccac1d35ee.png?1612002350" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/b91909d451c9e47e3ae10fccac1d35ee.png">
            <span class="display-name">Cre8 Team</span>
</a>          <a class="badge-link" href="https://dribbble.com/pro">
  <span class="badge badge-team">Team</span>
</a>
        </div>
        <div class="shot-statistics-container js-shot-statistics">

    <div class="shot-statistic js-shot-likes-container">
        

  <div class="like-action-15133535  compact-true shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(15133535, {
          likes_count: 241,
          liked: false
      });
      }
    </script>
      <a class=" tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current shot-tools-icon"><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

      <span class="js-shot-likes-count color-deep-blue-sea-light-20 font-weight-500">241</span>
    </div>
  <div class="shot-statistic js-shot-views-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11" viewBox="0 0 14 11" fill="none" role="img" class="icon fill-current shot-tools-icon">
<path d="M7 3.45833C5.87242 3.45833 4.95833 4.37242 4.95833 5.5C4.95833 6.62758 5.87242 7.54167 7 7.54167C8.12758 7.54167 9.04167 6.62758 9.04167 5.5C9.04167 4.37242 8.12758 3.45833 7 3.45833ZM7 0.25C3.13425 0.25 0 4.625 0 5.5C0 6.375 3.13425 10.75 7 10.75C10.8657 10.75 14 6.375 14 5.5C14 4.625 10.8657 0.25 7 0.25ZM7 8.70833C5.22783 8.70833 3.79167 7.27217 3.79167 5.5C3.79167 3.72783 5.22783 2.29167 7 2.29167C8.77217 2.29167 10.2083 3.72783 10.2083 5.5C10.2083 7.27217 8.77217 8.70833 7 8.70833Z"></path>
</svg>

    <span class="js-shot-views-count color-deep-blue-sea-light-20 font-weight-500">61.3k</span>
  </div>
</div>

    </div>



</li>


<li id="screenshot-7217612" data-thumbnail-id="7217612" class="shot-thumbnail js-thumbnail shot-thumbnail-container      " data-ad-data="" data-boost-id="" data-is-boost-fallback="">
  

<div class="js-thumbnail-base shot-thumbnail-base disabled-shot-section dribbble-shot dribbble  ">
      <figure style="background-color: #ECECED" class="js-thumbnail-placeholder shot-thumbnail-placeholder">
  <noscript>
      <img src="https://cdn.dribbble.com/users/5576/screenshots/7217612/media/50429fe2e1dac9263006f40cb63b9fb1.png?compress=1&amp;resize=400x300" alt="Travel App - Search activities app countries cyprus destination find hiking iphone map mobile app design mobile ui nature places search search bar search results searching sports suggestion travel">
  </noscript>
  <img alt="Travel App - Search activities app countries cyprus destination find hiking iphone map mobile app design mobile ui nature places search search bar search results searching sports suggestion travel" width="330" height="247" data-srcset="https://cdn.dribbble.com/users/5576/screenshots/7217612/media/50429fe2e1dac9263006f40cb63b9fb1.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/5576/screenshots/7217612/media/50429fe2e1dac9263006f40cb63b9fb1.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/5576/screenshots/7217612/media/50429fe2e1dac9263006f40cb63b9fb1.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/5576/screenshots/7217612/media/50429fe2e1dac9263006f40cb63b9fb1.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/5576/screenshots/7217612/media/50429fe2e1dac9263006f40cb63b9fb1.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/5576/screenshots/7217612/media/50429fe2e1dac9263006f40cb63b9fb1.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/5576/screenshots/7217612/media/50429fe2e1dac9263006f40cb63b9fb1.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/5576/screenshots/7217612/media/50429fe2e1dac9263006f40cb63b9fb1.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/5576/screenshots/7217612/media/50429fe2e1dac9263006f40cb63b9fb1.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w" data-src="https://cdn.dribbble.com/users/5576/screenshots/7217612/media/50429fe2e1dac9263006f40cb63b9fb1.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class="lazyautosizes lazyloaded" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/50429fe2e1dac9263006f40cb63b9fb1.png" sizes="295px" srcset="https://cdn.dribbble.com/users/5576/screenshots/7217612/media/50429fe2e1dac9263006f40cb63b9fb1.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/5576/screenshots/7217612/media/50429fe2e1dac9263006f40cb63b9fb1.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/5576/screenshots/7217612/media/50429fe2e1dac9263006f40cb63b9fb1.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/5576/screenshots/7217612/media/50429fe2e1dac9263006f40cb63b9fb1.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/5576/screenshots/7217612/media/50429fe2e1dac9263006f40cb63b9fb1.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/5576/screenshots/7217612/media/50429fe2e1dac9263006f40cb63b9fb1.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/5576/screenshots/7217612/media/50429fe2e1dac9263006f40cb63b9fb1.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/5576/screenshots/7217612/media/50429fe2e1dac9263006f40cb63b9fb1.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/5576/screenshots/7217612/media/50429fe2e1dac9263006f40cb63b9fb1.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w">
</figure>


    <div class="shot-thumbnail-extras">
      
    </div>

      <a class="shot-thumbnail-link dribbble-link js-shot-link" href="https://dribbble.com/shots/7217612-Travel-App-Search">
        <span class="accessibility-text">View Travel App - Search</span>
</a>
    <div class="shot-thumbnail-overlay">
      <div class="shot-thumbnail-overlay-content">
            <div class="shot-title">Travel App - Search</div>

            <ul class="js-dribbble-shot-actions shot-actions-container">
                <li data-bucket-container="true" class="shot-action">
                  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m22 5h-11l-2-3h-7c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h20c1.104 0 2-.896 2-2v-13c0-1.104-.896-2-2-2zm-6 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3v-3c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z"></path></svg>

</a>
                </li>

                

  <div class="like-action-7217612   shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(7217612, {
          likes_count: 191,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

            </ul>
      </div>
    </div>
</div>


    <div class="shot-details-container js-shot-details-container" style="visibility: visible;">
        <div class="user-information">
          <a class="hoverable url" rel="contact" href="https://dribbble.com/plat4m">
            <img class="photo lazyloaded" alt="PLATFORM" width="24" height="24" data-src="https://cdn.dribbble.com/users/37144/avatars/mini/logo-dribbble.jpg?1402480283" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/logo-dribbble.jpg">
            <span class="display-name">PLATFORM</span>
</a>          <a class="badge-link" href="https://dribbble.com/pro">
  <span class="badge badge-team">Team</span>
</a>
        </div>
        <div class="shot-statistics-container js-shot-statistics">

    <div class="shot-statistic js-shot-likes-container">
        

  <div class="like-action-7217612  compact-true shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(7217612, {
          likes_count: 191,
          liked: false
      });
      }
    </script>
      <a class=" tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current shot-tools-icon"><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

      <span class="js-shot-likes-count color-deep-blue-sea-light-20 font-weight-500">191</span>
    </div>
  <div class="shot-statistic js-shot-views-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11" viewBox="0 0 14 11" fill="none" role="img" class="icon fill-current shot-tools-icon">
<path d="M7 3.45833C5.87242 3.45833 4.95833 4.37242 4.95833 5.5C4.95833 6.62758 5.87242 7.54167 7 7.54167C8.12758 7.54167 9.04167 6.62758 9.04167 5.5C9.04167 4.37242 8.12758 3.45833 7 3.45833ZM7 0.25C3.13425 0.25 0 4.625 0 5.5C0 6.375 3.13425 10.75 7 10.75C10.8657 10.75 14 6.375 14 5.5C14 4.625 10.8657 0.25 7 0.25ZM7 8.70833C5.22783 8.70833 3.79167 7.27217 3.79167 5.5C3.79167 3.72783 5.22783 2.29167 7 2.29167C8.77217 2.29167 10.2083 3.72783 10.2083 5.5C10.2083 7.27217 8.77217 8.70833 7 8.70833Z"></path>
</svg>

    <span class="js-shot-views-count color-deep-blue-sea-light-20 font-weight-500">91.7k</span>
  </div>
</div>

    </div>



</li>


<li id="screenshot-3388355" data-thumbnail-id="3388355" class="shot-thumbnail js-thumbnail shot-thumbnail-container      " data-ad-data="" data-boost-id="" data-is-boost-fallback="">
  

<div class="js-thumbnail-base shot-thumbnail-base disabled-shot-section dribbble-shot dribbble  ">
      <figure style="background-color: #28303B" class="js-thumbnail-placeholder shot-thumbnail-placeholder">
  <noscript>
      <img src="https://cdn.dribbble.com/users/405554/screenshots/3388355/media/0dbce1408234b3729d4ff79dd1d2ef0e.jpg?compress=1&amp;resize=400x300" alt="My Nearest Places Ios App Icon app icon find nearby places ios app icon iphone app iphone app icon my nearest places travel app">
  </noscript>
  <img alt="My Nearest Places Ios App Icon app icon find nearby places ios app icon iphone app iphone app icon my nearest places travel app" width="330" height="247" data-srcset="https://cdn.dribbble.com/users/405554/screenshots/3388355/media/0dbce1408234b3729d4ff79dd1d2ef0e.jpg?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/405554/screenshots/3388355/media/0dbce1408234b3729d4ff79dd1d2ef0e.jpg?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/405554/screenshots/3388355/media/0dbce1408234b3729d4ff79dd1d2ef0e.jpg?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/405554/screenshots/3388355/media/0dbce1408234b3729d4ff79dd1d2ef0e.jpg?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/405554/screenshots/3388355/media/0dbce1408234b3729d4ff79dd1d2ef0e.jpg?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/405554/screenshots/3388355/media/0dbce1408234b3729d4ff79dd1d2ef0e.jpg?compress=1&amp;resize=800x600&amp;vertical=top 800w" data-src="https://cdn.dribbble.com/users/405554/screenshots/3388355/media/0dbce1408234b3729d4ff79dd1d2ef0e.jpg?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class="lazyautosizes lazyloaded" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/0dbce1408234b3729d4ff79dd1d2ef0e.jpg" sizes="295px" srcset="https://cdn.dribbble.com/users/405554/screenshots/3388355/media/0dbce1408234b3729d4ff79dd1d2ef0e.jpg?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/405554/screenshots/3388355/media/0dbce1408234b3729d4ff79dd1d2ef0e.jpg?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/405554/screenshots/3388355/media/0dbce1408234b3729d4ff79dd1d2ef0e.jpg?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/405554/screenshots/3388355/media/0dbce1408234b3729d4ff79dd1d2ef0e.jpg?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/405554/screenshots/3388355/media/0dbce1408234b3729d4ff79dd1d2ef0e.jpg?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/405554/screenshots/3388355/media/0dbce1408234b3729d4ff79dd1d2ef0e.jpg?compress=1&amp;resize=800x600&amp;vertical=top 800w">
</figure>


    <div class="shot-thumbnail-extras">
      
    </div>

      <a class="shot-thumbnail-link dribbble-link js-shot-link" href="https://dribbble.com/shots/3388355-My-Nearest-Places-Ios-App-Icon">
        <span class="accessibility-text">View My Nearest Places Ios App Icon</span>
</a>
    <div class="shot-thumbnail-overlay">
      <div class="shot-thumbnail-overlay-content">
            <div class="shot-title">My Nearest Places Ios App Icon</div>

            <ul class="js-dribbble-shot-actions shot-actions-container">
                <li data-bucket-container="true" class="shot-action">
                  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m22 5h-11l-2-3h-7c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h20c1.104 0 2-.896 2-2v-13c0-1.104-.896-2-2-2zm-6 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3v-3c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z"></path></svg>

</a>
                </li>

                

  <div class="like-action-3388355   shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(3388355, {
          likes_count: 31,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

            </ul>
      </div>
    </div>
</div>


    <div class="shot-details-container js-shot-details-container" style="visibility: visible;">
        <div class="user-information">
          <a class="hoverable url" rel="contact" href="https://dribbble.com/montu">
            <img class="photo lazyloaded" alt="Montu Yadav" width="24" height="24" data-src="https://cdn.dribbble.com/users/405554/avatars/mini/92eb36b5eddfd90a42aa646cbfc254f3.png?1536857210" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/92eb36b5eddfd90a42aa646cbfc254f3.png">
            <span class="display-name">Montu Yadav</span>
</a>          
        </div>
        <div class="shot-statistics-container js-shot-statistics">

    <div class="shot-statistic js-shot-likes-container">
        

  <div class="like-action-3388355  compact-true shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(3388355, {
          likes_count: 31,
          liked: false
      });
      }
    </script>
      <a class=" tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current shot-tools-icon"><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

      <span class="js-shot-likes-count color-deep-blue-sea-light-20 font-weight-500">31</span>
    </div>
  <div class="shot-statistic js-shot-views-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11" viewBox="0 0 14 11" fill="none" role="img" class="icon fill-current shot-tools-icon">
<path d="M7 3.45833C5.87242 3.45833 4.95833 4.37242 4.95833 5.5C4.95833 6.62758 5.87242 7.54167 7 7.54167C8.12758 7.54167 9.04167 6.62758 9.04167 5.5C9.04167 4.37242 8.12758 3.45833 7 3.45833ZM7 0.25C3.13425 0.25 0 4.625 0 5.5C0 6.375 3.13425 10.75 7 10.75C10.8657 10.75 14 6.375 14 5.5C14 4.625 10.8657 0.25 7 0.25ZM7 8.70833C5.22783 8.70833 3.79167 7.27217 3.79167 5.5C3.79167 3.72783 5.22783 2.29167 7 2.29167C8.77217 2.29167 10.2083 3.72783 10.2083 5.5C10.2083 7.27217 8.77217 8.70833 7 8.70833Z"></path>
</svg>

    <span class="js-shot-views-count color-deep-blue-sea-light-20 font-weight-500">1.6k</span>
  </div>
</div>

    </div>



</li>


<li id="screenshot-5556684" data-thumbnail-id="5556684" class="shot-thumbnail js-thumbnail shot-thumbnail-container      " data-ad-data="" data-boost-id="" data-is-boost-fallback="">
  

<div class="js-thumbnail-base shot-thumbnail-base disabled-shot-section dribbble-shot dribbble  ">
      <figure style="background-color: #E9E7EA" class="js-thumbnail-placeholder shot-thumbnail-placeholder">
  <noscript>
      <img src="https://cdn.dribbble.com/users/903897/screenshots/5556684/shot_4x.png?compress=1&amp;resize=400x300" alt="Find food nearby account app check out details food hiwow mobile order panel popup price profile search shop ui">
  </noscript>
  <img alt="Find food nearby account app check out details food hiwow mobile order panel popup price profile search shop ui" width="330" height="247" data-srcset="https://cdn.dribbble.com/users/903897/screenshots/5556684/shot_4x.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/903897/screenshots/5556684/shot_4x.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/903897/screenshots/5556684/shot_4x.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/903897/screenshots/5556684/shot_4x.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/903897/screenshots/5556684/shot_4x.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/903897/screenshots/5556684/shot_4x.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/903897/screenshots/5556684/shot_4x.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/903897/screenshots/5556684/shot_4x.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/903897/screenshots/5556684/shot_4x.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w" data-src="https://cdn.dribbble.com/users/903897/screenshots/5556684/shot_4x.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class="lazyautosizes lazyloaded" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/shot_4x.png" sizes="295px" srcset="https://cdn.dribbble.com/users/903897/screenshots/5556684/shot_4x.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/903897/screenshots/5556684/shot_4x.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/903897/screenshots/5556684/shot_4x.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/903897/screenshots/5556684/shot_4x.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/903897/screenshots/5556684/shot_4x.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/903897/screenshots/5556684/shot_4x.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/903897/screenshots/5556684/shot_4x.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/903897/screenshots/5556684/shot_4x.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/903897/screenshots/5556684/shot_4x.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w">
</figure>


    <div class="shot-thumbnail-extras">
          <div class="has-attachment shot-thumbnail-extras-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" role="img" class="icon ">
<path d="M14.0369 1.96266C13.2016 1.12666 12.0903 0.666656 10.9083 0.666656C9.72559 0.666656 8.61493 1.12666 7.77959 1.96266C7.59093 2.15066 7.48693 2.40066 7.48693 2.66732C7.48693 2.93399 7.59093 3.18399 7.77893 3.37199C7.95959 3.55266 8.21626 3.65599 8.48359 3.65666C8.75093 3.65666 9.00759 3.55266 9.18826 3.37199C10.1043 2.45666 11.7116 2.45666 12.6269 3.37199C13.0856 3.83132 13.3389 4.44199 13.3389 5.09132C13.3389 5.74066 13.0856 6.35132 12.6269 6.81066L6.80959 12.628C5.89359 13.5433 4.28693 13.5433 3.37093 12.628C2.91293 12.168 2.65959 11.558 2.65959 10.9087C2.65959 10.2593 2.91293 9.64866 3.37159 9.18932L5.85026 6.71066C6.13026 6.43199 6.58493 6.43199 6.86493 6.71066C7.00026 6.84599 7.07493 7.02666 7.07493 7.21799C7.07493 7.40999 7.00026 7.59066 6.86493 7.72532L5.09359 9.49666C4.90493 9.68466 4.80093 9.93466 4.80093 10.2013C4.80093 10.468 4.90493 10.718 5.09293 10.906C5.45359 11.2673 6.14159 11.2673 6.50226 10.906L8.27359 9.13466C8.78493 8.62332 9.06693 7.94266 9.06693 7.21799C9.06693 6.49266 8.78559 5.81199 8.27359 5.30132C7.76226 4.78999 7.08159 4.50866 6.35693 4.50866C5.63226 4.50866 4.95293 4.78999 4.44093 5.30066L1.96226 7.77999C1.12626 8.61466 0.66626 9.72666 0.66626 10.9087C0.66626 12.0907 1.12626 13.2013 1.96226 14.0373C2.79759 14.8733 3.90893 15.3333 5.09093 15.3333C6.27359 15.3333 7.38426 14.8733 8.21959 14.0373L14.0369 8.21999C14.8729 7.38532 15.3329 6.27332 15.3329 5.09132C15.3329 3.90932 14.8729 2.79866 14.0369 1.96266V1.96266Z" fill="white"></path>
</svg>

          </div>
      
    </div>

      <a class="shot-thumbnail-link dribbble-link js-shot-link" href="https://dribbble.com/shots/5556684-Find-food-nearby">
        <span class="accessibility-text">View Find food nearby</span>
</a>
    <div class="shot-thumbnail-overlay">
      <div class="shot-thumbnail-overlay-content">
            <div class="shot-title">Find food nearby</div>

            <ul class="js-dribbble-shot-actions shot-actions-container">
                <li data-bucket-container="true" class="shot-action">
                  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m22 5h-11l-2-3h-7c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h20c1.104 0 2-.896 2-2v-13c0-1.104-.896-2-2-2zm-6 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3v-3c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z"></path></svg>

</a>
                </li>

                

  <div class="like-action-5556684   shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(5556684, {
          likes_count: 667,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

            </ul>
      </div>
    </div>
</div>


    <div class="shot-details-container js-shot-details-container" style="visibility: visible;">
        <div class="user-information">
          <a class="hoverable url" rel="contact" href="https://dribbble.com/Hiwow">
            <img class="photo lazyloaded" alt="Hiwow" width="24" height="24" data-src="https://cdn.dribbble.com/users/2201653/avatars/mini/678fa137a5343bec67ce2ff50ab41630.png?1556090940" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/678fa137a5343bec67ce2ff50ab41630.png">
            <span class="display-name">Hiwow</span>
</a>          <a class="badge-link" href="https://dribbble.com/pro">
  <span class="badge badge-team">Team</span>
</a>
        </div>
        <div class="shot-statistics-container js-shot-statistics">

    <div class="shot-statistic js-shot-likes-container">
        

  <div class="like-action-5556684  compact-true shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(5556684, {
          likes_count: 667,
          liked: false
      });
      }
    </script>
      <a class=" tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current shot-tools-icon"><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

      <span class="js-shot-likes-count color-deep-blue-sea-light-20 font-weight-500">667</span>
    </div>
  <div class="shot-statistic js-shot-views-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11" viewBox="0 0 14 11" fill="none" role="img" class="icon fill-current shot-tools-icon">
<path d="M7 3.45833C5.87242 3.45833 4.95833 4.37242 4.95833 5.5C4.95833 6.62758 5.87242 7.54167 7 7.54167C8.12758 7.54167 9.04167 6.62758 9.04167 5.5C9.04167 4.37242 8.12758 3.45833 7 3.45833ZM7 0.25C3.13425 0.25 0 4.625 0 5.5C0 6.375 3.13425 10.75 7 10.75C10.8657 10.75 14 6.375 14 5.5C14 4.625 10.8657 0.25 7 0.25ZM7 8.70833C5.22783 8.70833 3.79167 7.27217 3.79167 5.5C3.79167 3.72783 5.22783 2.29167 7 2.29167C8.77217 2.29167 10.2083 3.72783 10.2083 5.5C10.2083 7.27217 8.77217 8.70833 7 8.70833Z"></path>
</svg>

    <span class="js-shot-views-count color-deep-blue-sea-light-20 font-weight-500">125k</span>
  </div>
</div>

    </div>



</li>


<li id="screenshot-10801819" data-thumbnail-id="10801819" class="shot-thumbnail js-thumbnail shot-thumbnail-container      " data-ad-data="" data-boost-id="" data-is-boost-fallback="">
  

<div class="js-thumbnail-base shot-thumbnail-base disabled-shot-section dribbble-shot dribbble  ">
      <figure style="background-color: #EDF3F3" class="js-thumbnail-placeholder shot-thumbnail-placeholder">
  <noscript>
      <img src="https://cdn.dribbble.com/users/1008889/screenshots/10801819/media/409d0e1b1c830c0e45abd458c411b598.jpg?compress=1&amp;resize=400x300" alt="Find Your Doctor / Mobile UI Concept appointment card coronavirus covid19 doctor find hospital map medical mobile app nearby online profile service ui ux">
  </noscript>
  <img alt="Find Your Doctor / Mobile UI Concept appointment card coronavirus covid19 doctor find hospital map medical mobile app nearby online profile service ui ux" width="330" height="247" data-srcset="https://cdn.dribbble.com/users/1008889/screenshots/10801819/media/409d0e1b1c830c0e45abd458c411b598.jpg?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/1008889/screenshots/10801819/media/409d0e1b1c830c0e45abd458c411b598.jpg?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/1008889/screenshots/10801819/media/409d0e1b1c830c0e45abd458c411b598.jpg?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/1008889/screenshots/10801819/media/409d0e1b1c830c0e45abd458c411b598.jpg?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/1008889/screenshots/10801819/media/409d0e1b1c830c0e45abd458c411b598.jpg?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/1008889/screenshots/10801819/media/409d0e1b1c830c0e45abd458c411b598.jpg?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/1008889/screenshots/10801819/media/409d0e1b1c830c0e45abd458c411b598.jpg?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/1008889/screenshots/10801819/media/409d0e1b1c830c0e45abd458c411b598.jpg?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/1008889/screenshots/10801819/media/409d0e1b1c830c0e45abd458c411b598.jpg?compress=1&amp;resize=1200x900&amp;vertical=top 1200w" data-src="https://cdn.dribbble.com/users/1008889/screenshots/10801819/media/409d0e1b1c830c0e45abd458c411b598.jpg?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class="lazyautosizes lazyloaded" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/409d0e1b1c830c0e45abd458c411b598.jpg" sizes="295px" srcset="https://cdn.dribbble.com/users/1008889/screenshots/10801819/media/409d0e1b1c830c0e45abd458c411b598.jpg?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/1008889/screenshots/10801819/media/409d0e1b1c830c0e45abd458c411b598.jpg?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/1008889/screenshots/10801819/media/409d0e1b1c830c0e45abd458c411b598.jpg?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/1008889/screenshots/10801819/media/409d0e1b1c830c0e45abd458c411b598.jpg?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/1008889/screenshots/10801819/media/409d0e1b1c830c0e45abd458c411b598.jpg?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/1008889/screenshots/10801819/media/409d0e1b1c830c0e45abd458c411b598.jpg?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/1008889/screenshots/10801819/media/409d0e1b1c830c0e45abd458c411b598.jpg?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/1008889/screenshots/10801819/media/409d0e1b1c830c0e45abd458c411b598.jpg?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/1008889/screenshots/10801819/media/409d0e1b1c830c0e45abd458c411b598.jpg?compress=1&amp;resize=1200x900&amp;vertical=top 1200w">
</figure>


    <div class="shot-thumbnail-extras">
      
    </div>

      <a class="shot-thumbnail-link dribbble-link js-shot-link" href="https://dribbble.com/shots/10801819-Find-Your-Doctor-Mobile-UI-Concept">
        <span class="accessibility-text">View Find Your Doctor / Mobile UI Concept</span>
</a>
    <div class="shot-thumbnail-overlay">
      <div class="shot-thumbnail-overlay-content">
            <div class="shot-title">Find Your Doctor / Mobile UI Concept</div>

            <ul class="js-dribbble-shot-actions shot-actions-container">
                <li data-bucket-container="true" class="shot-action">
                  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m22 5h-11l-2-3h-7c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h20c1.104 0 2-.896 2-2v-13c0-1.104-.896-2-2-2zm-6 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3v-3c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z"></path></svg>

</a>
                </li>

                

  <div class="like-action-10801819   shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(10801819, {
          likes_count: 167,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

            </ul>
      </div>
    </div>
</div>


    <div class="shot-details-container js-shot-details-container" style="visibility: visible;">
        <div class="user-information">
          <a class="hoverable url" rel="contact" href="https://dribbble.com/halallab">
            <img class="photo lazyloaded" alt="Halal Lab" width="24" height="24" data-src="https://cdn.dribbble.com/users/5920881/avatars/mini/376d9fa5c26c3a51d99ac1a8b6ee5cda.png?1607425475" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/376d9fa5c26c3a51d99ac1a8b6ee5cda.png">
            <span class="display-name">Halal Lab</span>
</a>          <a class="badge-link" href="https://dribbble.com/pro">
  <span class="badge badge-team">Team</span>
</a>
        </div>
        <div class="shot-statistics-container js-shot-statistics">

    <div class="shot-statistic js-shot-likes-container">
        

  <div class="like-action-10801819  compact-true shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(10801819, {
          likes_count: 167,
          liked: false
      });
      }
    </script>
      <a class=" tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current shot-tools-icon"><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

      <span class="js-shot-likes-count color-deep-blue-sea-light-20 font-weight-500">167</span>
    </div>
  <div class="shot-statistic js-shot-views-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11" viewBox="0 0 14 11" fill="none" role="img" class="icon fill-current shot-tools-icon">
<path d="M7 3.45833C5.87242 3.45833 4.95833 4.37242 4.95833 5.5C4.95833 6.62758 5.87242 7.54167 7 7.54167C8.12758 7.54167 9.04167 6.62758 9.04167 5.5C9.04167 4.37242 8.12758 3.45833 7 3.45833ZM7 0.25C3.13425 0.25 0 4.625 0 5.5C0 6.375 3.13425 10.75 7 10.75C10.8657 10.75 14 6.375 14 5.5C14 4.625 10.8657 0.25 7 0.25ZM7 8.70833C5.22783 8.70833 3.79167 7.27217 3.79167 5.5C3.79167 3.72783 5.22783 2.29167 7 2.29167C8.77217 2.29167 10.2083 3.72783 10.2083 5.5C10.2083 7.27217 8.77217 8.70833 7 8.70833Z"></path>
</svg>

    <span class="js-shot-views-count color-deep-blue-sea-light-20 font-weight-500">52.8k</span>
  </div>
</div>

    </div>



</li>


<li id="screenshot-16161060" data-thumbnail-id="16161060" class="shot-thumbnail js-thumbnail shot-thumbnail-container      " data-ad-data="" data-boost-id="" data-is-boost-fallback="">
  

<div class="js-thumbnail-base shot-thumbnail-base disabled-shot-section dribbble-shot dribbble  ">
      <figure style="background-color: #E5E0E5" class="js-thumbnail-placeholder shot-thumbnail-placeholder">
  <noscript>
      <img src="https://cdn.dribbble.com/users/4612288/screenshots/16161060/media/e7525eab499863b093a4e9d47ff045bc.png?compress=1&amp;resize=400x300" alt="Places to dine search app app best booking concept description design design concept dine eat find gallery great meal pictures places reccomendation review search ui ux">
  </noscript>
  <img alt="Places to dine search app app best booking concept description design design concept dine eat find gallery great meal pictures places reccomendation review search ui ux" width="330" height="247" data-srcset="https://cdn.dribbble.com/users/4612288/screenshots/16161060/media/e7525eab499863b093a4e9d47ff045bc.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/4612288/screenshots/16161060/media/e7525eab499863b093a4e9d47ff045bc.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/4612288/screenshots/16161060/media/e7525eab499863b093a4e9d47ff045bc.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/4612288/screenshots/16161060/media/e7525eab499863b093a4e9d47ff045bc.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/4612288/screenshots/16161060/media/e7525eab499863b093a4e9d47ff045bc.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/4612288/screenshots/16161060/media/e7525eab499863b093a4e9d47ff045bc.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/4612288/screenshots/16161060/media/e7525eab499863b093a4e9d47ff045bc.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/4612288/screenshots/16161060/media/e7525eab499863b093a4e9d47ff045bc.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/4612288/screenshots/16161060/media/e7525eab499863b093a4e9d47ff045bc.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w" data-src="https://cdn.dribbble.com/users/4612288/screenshots/16161060/media/e7525eab499863b093a4e9d47ff045bc.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class="lazyautosizes lazyloaded" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/e7525eab499863b093a4e9d47ff045bc.png" sizes="295px" srcset="https://cdn.dribbble.com/users/4612288/screenshots/16161060/media/e7525eab499863b093a4e9d47ff045bc.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/4612288/screenshots/16161060/media/e7525eab499863b093a4e9d47ff045bc.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/4612288/screenshots/16161060/media/e7525eab499863b093a4e9d47ff045bc.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/4612288/screenshots/16161060/media/e7525eab499863b093a4e9d47ff045bc.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/4612288/screenshots/16161060/media/e7525eab499863b093a4e9d47ff045bc.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/4612288/screenshots/16161060/media/e7525eab499863b093a4e9d47ff045bc.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/4612288/screenshots/16161060/media/e7525eab499863b093a4e9d47ff045bc.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/4612288/screenshots/16161060/media/e7525eab499863b093a4e9d47ff045bc.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/4612288/screenshots/16161060/media/e7525eab499863b093a4e9d47ff045bc.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w">
</figure>


    <div class="shot-thumbnail-extras">
      
    </div>

      <a class="shot-thumbnail-link dribbble-link js-shot-link" href="https://dribbble.com/shots/16161060-Places-to-dine-search-app">
        <span class="accessibility-text">View Places to dine search app</span>
</a>
    <div class="shot-thumbnail-overlay">
      <div class="shot-thumbnail-overlay-content">
            <div class="shot-title">Places to dine search app</div>

            <ul class="js-dribbble-shot-actions shot-actions-container">
                <li data-bucket-container="true" class="shot-action">
                  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m22 5h-11l-2-3h-7c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h20c1.104 0 2-.896 2-2v-13c0-1.104-.896-2-2-2zm-6 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3v-3c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z"></path></svg>

</a>
                </li>

                

  <div class="like-action-16161060   shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(16161060, {
          likes_count: 92,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

            </ul>
      </div>
    </div>
</div>


    <div class="shot-details-container js-shot-details-container" style="visibility: visible;">
        <div class="user-information">
          <a class="hoverable url" rel="contact" href="https://dribbble.com/andersen-design">
            <img class="photo lazyloaded" alt="Andersen Design" width="24" height="24" data-src="https://cdn.dribbble.com/users/5287235/avatars/mini/e02b5a37d8f4ebe12c39961a0eef3eeb.png?1588940175" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/e02b5a37d8f4ebe12c39961a0eef3eeb.png">
            <span class="display-name">Andersen Design</span>
</a>          <a class="badge-link" href="https://dribbble.com/pro">
  <span class="badge badge-team">Team</span>
</a>
        </div>
        <div class="shot-statistics-container js-shot-statistics">

    <div class="shot-statistic js-shot-likes-container">
        

  <div class="like-action-16161060  compact-true shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(16161060, {
          likes_count: 92,
          liked: false
      });
      }
    </script>
      <a class=" tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current shot-tools-icon"><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

      <span class="js-shot-likes-count color-deep-blue-sea-light-20 font-weight-500">92</span>
    </div>
  <div class="shot-statistic js-shot-views-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11" viewBox="0 0 14 11" fill="none" role="img" class="icon fill-current shot-tools-icon">
<path d="M7 3.45833C5.87242 3.45833 4.95833 4.37242 4.95833 5.5C4.95833 6.62758 5.87242 7.54167 7 7.54167C8.12758 7.54167 9.04167 6.62758 9.04167 5.5C9.04167 4.37242 8.12758 3.45833 7 3.45833ZM7 0.25C3.13425 0.25 0 4.625 0 5.5C0 6.375 3.13425 10.75 7 10.75C10.8657 10.75 14 6.375 14 5.5C14 4.625 10.8657 0.25 7 0.25ZM7 8.70833C5.22783 8.70833 3.79167 7.27217 3.79167 5.5C3.79167 3.72783 5.22783 2.29167 7 2.29167C8.77217 2.29167 10.2083 3.72783 10.2083 5.5C10.2083 7.27217 8.77217 8.70833 7 8.70833Z"></path>
</svg>

    <span class="js-shot-views-count color-deep-blue-sea-light-20 font-weight-500">17.7k</span>
  </div>
</div>

    </div>



</li>


<li id="screenshot-17116311" data-thumbnail-id="17116311" class="shot-thumbnail js-thumbnail shot-thumbnail-container      " data-ad-data="" data-boost-id="" data-is-boost-fallback="">
  

<div class="js-thumbnail-base shot-thumbnail-base disabled-shot-section dribbble-shot dribbble  ">
      <figure style="background-color: #DEE6B2" class="js-thumbnail-placeholder shot-thumbnail-placeholder">
  <noscript>
      <img src="https://cdn.dribbble.com/users/1083899/screenshots/17116311/media/19a255e2c205ebc0e9b3c06fc0ef5837.png?compress=1&amp;resize=400x300" alt="Search items nearby app buy and sell buy things ecommerce find nearby searc nearby">
  </noscript>
  <img alt="Search items nearby app buy and sell buy things ecommerce find nearby searc nearby" width="330" height="247" data-srcset="https://cdn.dribbble.com/users/1083899/screenshots/17116311/media/19a255e2c205ebc0e9b3c06fc0ef5837.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/1083899/screenshots/17116311/media/19a255e2c205ebc0e9b3c06fc0ef5837.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/1083899/screenshots/17116311/media/19a255e2c205ebc0e9b3c06fc0ef5837.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/1083899/screenshots/17116311/media/19a255e2c205ebc0e9b3c06fc0ef5837.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/1083899/screenshots/17116311/media/19a255e2c205ebc0e9b3c06fc0ef5837.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/1083899/screenshots/17116311/media/19a255e2c205ebc0e9b3c06fc0ef5837.png?compress=1&amp;resize=800x600&amp;vertical=top 800w" data-src="https://cdn.dribbble.com/users/1083899/screenshots/17116311/media/19a255e2c205ebc0e9b3c06fc0ef5837.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class="lazyautosizes lazyloaded" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/19a255e2c205ebc0e9b3c06fc0ef5837.png" sizes="295px" srcset="https://cdn.dribbble.com/users/1083899/screenshots/17116311/media/19a255e2c205ebc0e9b3c06fc0ef5837.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/1083899/screenshots/17116311/media/19a255e2c205ebc0e9b3c06fc0ef5837.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/1083899/screenshots/17116311/media/19a255e2c205ebc0e9b3c06fc0ef5837.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/1083899/screenshots/17116311/media/19a255e2c205ebc0e9b3c06fc0ef5837.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/1083899/screenshots/17116311/media/19a255e2c205ebc0e9b3c06fc0ef5837.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/1083899/screenshots/17116311/media/19a255e2c205ebc0e9b3c06fc0ef5837.png?compress=1&amp;resize=800x600&amp;vertical=top 800w">
</figure>


    <div class="shot-thumbnail-extras">
      
    </div>

      <a class="shot-thumbnail-link dribbble-link js-shot-link" href="https://dribbble.com/shots/17116311-Search-items-nearby-app">
        <span class="accessibility-text">View Search items nearby app</span>
</a>
    <div class="shot-thumbnail-overlay">
      <div class="shot-thumbnail-overlay-content">
            <div class="shot-title">Search items nearby app</div>

            <ul class="js-dribbble-shot-actions shot-actions-container">
                <li data-bucket-container="true" class="shot-action">
                  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m22 5h-11l-2-3h-7c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h20c1.104 0 2-.896 2-2v-13c0-1.104-.896-2-2-2zm-6 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3v-3c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z"></path></svg>

</a>
                </li>

                

  <div class="like-action-17116311   shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(17116311, {
          likes_count: 16,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

            </ul>
      </div>
    </div>
</div>


    <div class="shot-details-container js-shot-details-container" style="visibility: visible;">
        <div class="user-information">
          <a class="hoverable url" rel="contact" href="https://dribbble.com/adiatmabani">
            <img class="photo ls-is-cached lazyloaded" alt="adiatma bani" width="24" height="24" data-src="https://cdn.dribbble.com/users/1083899/avatars/mini/8eac4b930006c41606cced98f012502e.jpg?1589202052" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/8eac4b930006c41606cced98f012502e.jpg">
            <span class="display-name">adiatma bani</span>
</a>          <a class="badge-link" href="https://dribbble.com/pro">
  <span class="badge badge-pro">Pro</span>

</a>
        </div>
        <div class="shot-statistics-container js-shot-statistics">

    <div class="shot-statistic js-shot-likes-container">
        

  <div class="like-action-17116311  compact-true shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(17116311, {
          likes_count: 16,
          liked: false
      });
      }
    </script>
      <a class=" tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current shot-tools-icon"><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

      <span class="js-shot-likes-count color-deep-blue-sea-light-20 font-weight-500">16</span>
    </div>
  <div class="shot-statistic js-shot-views-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11" viewBox="0 0 14 11" fill="none" role="img" class="icon fill-current shot-tools-icon">
<path d="M7 3.45833C5.87242 3.45833 4.95833 4.37242 4.95833 5.5C4.95833 6.62758 5.87242 7.54167 7 7.54167C8.12758 7.54167 9.04167 6.62758 9.04167 5.5C9.04167 4.37242 8.12758 3.45833 7 3.45833ZM7 0.25C3.13425 0.25 0 4.625 0 5.5C0 6.375 3.13425 10.75 7 10.75C10.8657 10.75 14 6.375 14 5.5C14 4.625 10.8657 0.25 7 0.25ZM7 8.70833C5.22783 8.70833 3.79167 7.27217 3.79167 5.5C3.79167 3.72783 5.22783 2.29167 7 2.29167C8.77217 2.29167 10.2083 3.72783 10.2083 5.5C10.2083 7.27217 8.77217 8.70833 7 8.70833Z"></path>
</svg>

    <span class="js-shot-views-count color-deep-blue-sea-light-20 font-weight-500">4.2k</span>
  </div>
</div>

    </div>



</li>


<li id="screenshot-15541255" data-thumbnail-id="15541255" class="shot-thumbnail js-thumbnail shot-thumbnail-container      " data-ad-data="" data-boost-id="" data-is-boost-fallback="">
  

<div class="js-thumbnail-base shot-thumbnail-base disabled-shot-section dribbble-shot dribbble  ">
      <figure style="background-color: #FFFFFF" class="js-thumbnail-placeholder shot-thumbnail-placeholder">
  <noscript>
      <img src="https://cdn.dribbble.com/users/5279042/screenshots/15541255/media/066dc7fe29ece44a1363a8cdc6764a23.jpg?compress=1&amp;resize=400x300" alt="Home Find us Logo design | real estate | branding brand identity app booking branding building colorful design falt home finder house icon identity mark symbol local area pinpoint pin logo maps modern logo design places map location property real estate agency search vector logos images web online booking">
  </noscript>
  <img alt="Home Find us Logo design | real estate | branding brand identity app booking branding building colorful design falt home finder house icon identity mark symbol local area pinpoint pin logo maps modern logo design places map location property real estate agency search vector logos images web online booking" width="330" height="247" data-srcset="https://cdn.dribbble.com/users/5279042/screenshots/15541255/media/066dc7fe29ece44a1363a8cdc6764a23.jpg?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/5279042/screenshots/15541255/media/066dc7fe29ece44a1363a8cdc6764a23.jpg?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/5279042/screenshots/15541255/media/066dc7fe29ece44a1363a8cdc6764a23.jpg?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/5279042/screenshots/15541255/media/066dc7fe29ece44a1363a8cdc6764a23.jpg?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/5279042/screenshots/15541255/media/066dc7fe29ece44a1363a8cdc6764a23.jpg?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/5279042/screenshots/15541255/media/066dc7fe29ece44a1363a8cdc6764a23.jpg?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/5279042/screenshots/15541255/media/066dc7fe29ece44a1363a8cdc6764a23.jpg?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/5279042/screenshots/15541255/media/066dc7fe29ece44a1363a8cdc6764a23.jpg?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/5279042/screenshots/15541255/media/066dc7fe29ece44a1363a8cdc6764a23.jpg?compress=1&amp;resize=1200x900&amp;vertical=top 1200w" data-src="https://cdn.dribbble.com/users/5279042/screenshots/15541255/media/066dc7fe29ece44a1363a8cdc6764a23.jpg?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class="lazyautosizes lazyloaded" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/066dc7fe29ece44a1363a8cdc6764a23.jpg" sizes="295px" srcset="https://cdn.dribbble.com/users/5279042/screenshots/15541255/media/066dc7fe29ece44a1363a8cdc6764a23.jpg?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/5279042/screenshots/15541255/media/066dc7fe29ece44a1363a8cdc6764a23.jpg?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/5279042/screenshots/15541255/media/066dc7fe29ece44a1363a8cdc6764a23.jpg?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/5279042/screenshots/15541255/media/066dc7fe29ece44a1363a8cdc6764a23.jpg?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/5279042/screenshots/15541255/media/066dc7fe29ece44a1363a8cdc6764a23.jpg?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/5279042/screenshots/15541255/media/066dc7fe29ece44a1363a8cdc6764a23.jpg?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/5279042/screenshots/15541255/media/066dc7fe29ece44a1363a8cdc6764a23.jpg?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/5279042/screenshots/15541255/media/066dc7fe29ece44a1363a8cdc6764a23.jpg?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/5279042/screenshots/15541255/media/066dc7fe29ece44a1363a8cdc6764a23.jpg?compress=1&amp;resize=1200x900&amp;vertical=top 1200w">
</figure>


    <div class="shot-thumbnail-extras">
          <div class="has-rebound shot-thumbnail-extras-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="12" viewBox="0 0 16 12" fill="none" role="img" class="icon fill-current">
<path d="M6.40436 0.522824C6.936 0.0682789 7.79709 0.0682789 8.32873 0.522824C8.59491 0.749733 8.72873 1.04792 8.72727 1.34537V3.12355C12.9542 3.45373 16 6.65955 16 11.6363C16 11.6363 13.5076 9.16719 8.72727 8.93228V10.6545C8.72873 10.9526 8.59491 11.2501 8.32873 11.477C8.06327 11.7039 7.71491 11.8181 7.36654 11.8181C7.01818 11.8181 6.67054 11.7046 6.40436 11.477L-4.76837e-07 5.99992L6.40436 0.522824Z" fill="white"></path>
</svg>

            <span class="has-rebound-count">1</span>
          </div>
      
    </div>

      <a class="shot-thumbnail-link dribbble-link js-shot-link" href="https://dribbble.com/shots/15541255-Home-Find-us-Logo-design-real-estate-branding-brand-identity">
        <span class="accessibility-text">View Home Find us Logo design | real estate | branding brand identity</span>
</a>
    <div class="shot-thumbnail-overlay">
      <div class="shot-thumbnail-overlay-content">
            <div class="shot-title">Home Find us Logo design | real estate | branding brand identity</div>

            <ul class="js-dribbble-shot-actions shot-actions-container">
                <li data-bucket-container="true" class="shot-action">
                  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m22 5h-11l-2-3h-7c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h20c1.104 0 2-.896 2-2v-13c0-1.104-.896-2-2-2zm-6 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3v-3c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z"></path></svg>

</a>
                </li>

                

  <div class="like-action-15541255   shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(15541255, {
          likes_count: 93,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

            </ul>
      </div>
    </div>
</div>


    <div class="shot-details-container js-shot-details-container" style="visibility: visible;">
        <div class="user-information">
          <a class="hoverable url" rel="contact" href="https://dribbble.com/imarifhsn">
            <img class="photo lazyloaded" alt="Md Arif Hossain" width="24" height="24" data-src="https://cdn.dribbble.com/users/5279042/avatars/mini/6f9ca93751f4be379bf4356d879959da.png?1638895017" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/6f9ca93751f4be379bf4356d879959da.png">
            <span class="display-name">Md Arif Hossain</span>
</a>          <a class="badge-link" href="https://dribbble.com/pro">
  <span class="badge badge-pro">Pro</span>

</a>
        </div>
        <div class="shot-statistics-container js-shot-statistics">

    <div class="shot-statistic js-shot-likes-container">
        

  <div class="like-action-15541255  compact-true shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(15541255, {
          likes_count: 93,
          liked: false
      });
      }
    </script>
      <a class=" tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current shot-tools-icon"><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

      <span class="js-shot-likes-count color-deep-blue-sea-light-20 font-weight-500">93</span>
    </div>
  <div class="shot-statistic js-shot-views-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11" viewBox="0 0 14 11" fill="none" role="img" class="icon fill-current shot-tools-icon">
<path d="M7 3.45833C5.87242 3.45833 4.95833 4.37242 4.95833 5.5C4.95833 6.62758 5.87242 7.54167 7 7.54167C8.12758 7.54167 9.04167 6.62758 9.04167 5.5C9.04167 4.37242 8.12758 3.45833 7 3.45833ZM7 0.25C3.13425 0.25 0 4.625 0 5.5C0 6.375 3.13425 10.75 7 10.75C10.8657 10.75 14 6.375 14 5.5C14 4.625 10.8657 0.25 7 0.25ZM7 8.70833C5.22783 8.70833 3.79167 7.27217 3.79167 5.5C3.79167 3.72783 5.22783 2.29167 7 2.29167C8.77217 2.29167 10.2083 3.72783 10.2083 5.5C10.2083 7.27217 8.77217 8.70833 7 8.70833Z"></path>
</svg>

    <span class="js-shot-views-count color-deep-blue-sea-light-20 font-weight-500">40.9k</span>
  </div>
</div>

    </div>



</li>


<li id="screenshot-3816653" data-thumbnail-id="3816653" class="shot-thumbnail js-thumbnail shot-thumbnail-container      " data-ad-data="" data-boost-id="" data-is-boost-fallback="">
  

<div class="js-thumbnail-base shot-thumbnail-base disabled-shot-section dribbble-shot dribbble  ">
      <figure style="background-color: #F5F4F3" class="js-thumbnail-placeholder shot-thumbnail-placeholder">
  <noscript>
      <img src="https://cdn.dribbble.com/users/16521/screenshots/3816653/media/e84cf7fcc2ca8046a1942a692eb5508f.png?compress=1&amp;resize=400x300" alt="Find Gym &amp; Trainer Nearby app discover gym interface ios iphonex map profile search trainer">
  </noscript>
  <img alt="Find Gym &amp; Trainer Nearby app discover gym interface ios iphonex map profile search trainer" width="330" height="247" data-srcset="https://cdn.dribbble.com/users/16521/screenshots/3816653/media/e84cf7fcc2ca8046a1942a692eb5508f.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/16521/screenshots/3816653/media/e84cf7fcc2ca8046a1942a692eb5508f.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/16521/screenshots/3816653/media/e84cf7fcc2ca8046a1942a692eb5508f.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/16521/screenshots/3816653/media/e84cf7fcc2ca8046a1942a692eb5508f.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/16521/screenshots/3816653/media/e84cf7fcc2ca8046a1942a692eb5508f.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/16521/screenshots/3816653/media/e84cf7fcc2ca8046a1942a692eb5508f.png?compress=1&amp;resize=800x600&amp;vertical=top 800w" data-src="https://cdn.dribbble.com/users/16521/screenshots/3816653/media/e84cf7fcc2ca8046a1942a692eb5508f.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class="lazyautosizes lazyloaded" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/e84cf7fcc2ca8046a1942a692eb5508f.png" sizes="295px" srcset="https://cdn.dribbble.com/users/16521/screenshots/3816653/media/e84cf7fcc2ca8046a1942a692eb5508f.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/16521/screenshots/3816653/media/e84cf7fcc2ca8046a1942a692eb5508f.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/16521/screenshots/3816653/media/e84cf7fcc2ca8046a1942a692eb5508f.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/16521/screenshots/3816653/media/e84cf7fcc2ca8046a1942a692eb5508f.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/16521/screenshots/3816653/media/e84cf7fcc2ca8046a1942a692eb5508f.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/16521/screenshots/3816653/media/e84cf7fcc2ca8046a1942a692eb5508f.png?compress=1&amp;resize=800x600&amp;vertical=top 800w">
</figure>


    <div class="shot-thumbnail-extras">
      
    </div>

      <a class="shot-thumbnail-link dribbble-link js-shot-link" href="https://dribbble.com/shots/3816653-Find-Gym-Trainer-Nearby">
        <span class="accessibility-text">View Find Gym &amp; Trainer Nearby</span>
</a>
    <div class="shot-thumbnail-overlay">
      <div class="shot-thumbnail-overlay-content">
            <div class="shot-title">Find Gym &amp; Trainer Nearby</div>

            <ul class="js-dribbble-shot-actions shot-actions-container">
                <li data-bucket-container="true" class="shot-action">
                  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m22 5h-11l-2-3h-7c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h20c1.104 0 2-.896 2-2v-13c0-1.104-.896-2-2-2zm-6 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3v-3c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z"></path></svg>

</a>
                </li>

                

  <div class="like-action-3816653   shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(3816653, {
          likes_count: 143,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

            </ul>
      </div>
    </div>
</div>


    <div class="shot-details-container js-shot-details-container" style="visibility: visible;">
        <div class="user-information">
          <a class="hoverable url" rel="contact" href="https://dribbble.com/kpdesigns">
            <img class="photo lazyload" alt="Paresh Khatri" width="24" height="24" data-src="https://cdn.dribbble.com/users/16521/avatars/mini/f39be81ce3c33566eff03f41267e043b.jpg?1480424801" src="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail">
            <span class="display-name">Paresh Khatri</span>
</a>          <a class="badge-link" href="https://dribbble.com/pro">
  <span class="badge badge-pro">Pro</span>

</a>
        </div>
        <div class="shot-statistics-container js-shot-statistics">

    <div class="shot-statistic js-shot-likes-container">
        

  <div class="like-action-3816653  compact-true shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(3816653, {
          likes_count: 143,
          liked: false
      });
      }
    </script>
      <a class=" tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current shot-tools-icon"><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

      <span class="js-shot-likes-count color-deep-blue-sea-light-20 font-weight-500">143</span>
    </div>
  <div class="shot-statistic js-shot-views-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11" viewBox="0 0 14 11" fill="none" role="img" class="icon fill-current shot-tools-icon">
<path d="M7 3.45833C5.87242 3.45833 4.95833 4.37242 4.95833 5.5C4.95833 6.62758 5.87242 7.54167 7 7.54167C8.12758 7.54167 9.04167 6.62758 9.04167 5.5C9.04167 4.37242 8.12758 3.45833 7 3.45833ZM7 0.25C3.13425 0.25 0 4.625 0 5.5C0 6.375 3.13425 10.75 7 10.75C10.8657 10.75 14 6.375 14 5.5C14 4.625 10.8657 0.25 7 0.25ZM7 8.70833C5.22783 8.70833 3.79167 7.27217 3.79167 5.5C3.79167 3.72783 5.22783 2.29167 7 2.29167C8.77217 2.29167 10.2083 3.72783 10.2083 5.5C10.2083 7.27217 8.77217 8.70833 7 8.70833Z"></path>
</svg>

    <span class="js-shot-views-count color-deep-blue-sea-light-20 font-weight-500">13.1k</span>
  </div>
</div>

    </div>



</li>


<li id="screenshot-14354807" data-thumbnail-id="14354807" class="shot-thumbnail js-thumbnail shot-thumbnail-container      " data-ad-data="" data-boost-id="" data-is-boost-fallback="">
  

<div class="js-thumbnail-base shot-thumbnail-base disabled-shot-section dribbble-shot dribbble  multi-shot">
      <figure style="background-color: #F3F4F5" class="js-thumbnail-placeholder shot-thumbnail-placeholder">
  <noscript>
      <img src="https://cdn.dribbble.com/users/1785039/screenshots/14354807/media/29f08f20e34d974df0fdcb310a9a7e16.png?compress=1&amp;resize=400x300" alt="Doctor Finding App app app healthcare app healthcare doctor app find doctor health app health care healthcare medical record medical specialist medicines nearby doctors pharmacy">
  </noscript>
  <img alt="Doctor Finding App app app healthcare app healthcare doctor app find doctor health app health care healthcare medical record medical specialist medicines nearby doctors pharmacy" width="330" height="247" data-srcset="https://cdn.dribbble.com/users/1785039/screenshots/14354807/media/29f08f20e34d974df0fdcb310a9a7e16.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/1785039/screenshots/14354807/media/29f08f20e34d974df0fdcb310a9a7e16.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/1785039/screenshots/14354807/media/29f08f20e34d974df0fdcb310a9a7e16.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/1785039/screenshots/14354807/media/29f08f20e34d974df0fdcb310a9a7e16.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/1785039/screenshots/14354807/media/29f08f20e34d974df0fdcb310a9a7e16.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/1785039/screenshots/14354807/media/29f08f20e34d974df0fdcb310a9a7e16.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/1785039/screenshots/14354807/media/29f08f20e34d974df0fdcb310a9a7e16.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/1785039/screenshots/14354807/media/29f08f20e34d974df0fdcb310a9a7e16.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/1785039/screenshots/14354807/media/29f08f20e34d974df0fdcb310a9a7e16.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w" data-src="https://cdn.dribbble.com/users/1785039/screenshots/14354807/media/29f08f20e34d974df0fdcb310a9a7e16.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class="lazyautosizes lazyloaded" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/29f08f20e34d974df0fdcb310a9a7e16.png" sizes="295px" srcset="https://cdn.dribbble.com/users/1785039/screenshots/14354807/media/29f08f20e34d974df0fdcb310a9a7e16.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/1785039/screenshots/14354807/media/29f08f20e34d974df0fdcb310a9a7e16.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/1785039/screenshots/14354807/media/29f08f20e34d974df0fdcb310a9a7e16.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/1785039/screenshots/14354807/media/29f08f20e34d974df0fdcb310a9a7e16.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/1785039/screenshots/14354807/media/29f08f20e34d974df0fdcb310a9a7e16.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/1785039/screenshots/14354807/media/29f08f20e34d974df0fdcb310a9a7e16.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/1785039/screenshots/14354807/media/29f08f20e34d974df0fdcb310a9a7e16.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/1785039/screenshots/14354807/media/29f08f20e34d974df0fdcb310a9a7e16.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/1785039/screenshots/14354807/media/29f08f20e34d974df0fdcb310a9a7e16.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w">
</figure>


    <div class="shot-thumbnail-extras">
        <div class="loading-indicator shot-thumbnail-extras-icon"></div>
    <a class="animated-target" href="https://dribbble.com/shots/14354807-Doctor-Finding-App">
      <span class="accessibility-text">Shot Link</span>
</a>
    </div>

      <a class="shot-thumbnail-link dribbble-link js-shot-link" href="https://dribbble.com/shots/14354807-Doctor-Finding-App">
        <span class="accessibility-text">View Doctor Finding App</span>
</a>
    <div class="shot-thumbnail-overlay">
      <div class="shot-thumbnail-overlay-content">
            <div class="shot-title">Doctor Finding App</div>

            <ul class="js-dribbble-shot-actions shot-actions-container">
                <li data-bucket-container="true" class="shot-action">
                  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m22 5h-11l-2-3h-7c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h20c1.104 0 2-.896 2-2v-13c0-1.104-.896-2-2-2zm-6 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3v-3c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z"></path></svg>

</a>
                </li>

                

  <div class="like-action-14354807   shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(14354807, {
          likes_count: 116,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

            </ul>
      </div>
    </div>
</div>


    <div class="shot-details-container js-shot-details-container" style="visibility: visible;">
        <div class="user-information">
          <a class="hoverable url" rel="contact" href="https://dribbble.com/arvind_world">
            <img class="photo lazyload" alt="Arvind Patel" width="24" height="24" data-src="https://cdn.dribbble.com/users/1785039/avatars/mini/832257a9c2d5979cd58de42a08943d2f.png?1637007974" src="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail">
            <span class="display-name">Arvind Patel</span>
</a>          <a class="badge-link" href="https://dribbble.com/pro">
  <span class="badge badge-pro">Pro</span>

</a>
        </div>
        <div class="shot-statistics-container js-shot-statistics">

    <div class="shot-statistic js-shot-likes-container">
        

  <div class="like-action-14354807  compact-true shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(14354807, {
          likes_count: 116,
          liked: false
      });
      }
    </script>
      <a class=" tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current shot-tools-icon"><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

      <span class="js-shot-likes-count color-deep-blue-sea-light-20 font-weight-500">116</span>
    </div>
  <div class="shot-statistic js-shot-views-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11" viewBox="0 0 14 11" fill="none" role="img" class="icon fill-current shot-tools-icon">
<path d="M7 3.45833C5.87242 3.45833 4.95833 4.37242 4.95833 5.5C4.95833 6.62758 5.87242 7.54167 7 7.54167C8.12758 7.54167 9.04167 6.62758 9.04167 5.5C9.04167 4.37242 8.12758 3.45833 7 3.45833ZM7 0.25C3.13425 0.25 0 4.625 0 5.5C0 6.375 3.13425 10.75 7 10.75C10.8657 10.75 14 6.375 14 5.5C14 4.625 10.8657 0.25 7 0.25ZM7 8.70833C5.22783 8.70833 3.79167 7.27217 3.79167 5.5C3.79167 3.72783 5.22783 2.29167 7 2.29167C8.77217 2.29167 10.2083 3.72783 10.2083 5.5C10.2083 7.27217 8.77217 8.70833 7 8.70833Z"></path>
</svg>

    <span class="js-shot-views-count color-deep-blue-sea-light-20 font-weight-500">32.9k</span>
  </div>
</div>

    </div>



</li>


<li id="screenshot-6531656" data-thumbnail-id="6531656" class="shot-thumbnail js-thumbnail shot-thumbnail-container      " data-ad-data="" data-boost-id="" data-is-boost-fallback="">
  

<div class="js-thumbnail-base shot-thumbnail-base disabled-shot-section dribbble-shot dribbble  ">
      <figure style="background-color: #BABACF" class="js-thumbnail-placeholder shot-thumbnail-placeholder">
  <noscript>
      <img src="https://cdn.dribbble.com/users/1123841/screenshots/6531656/finder-1.0_4x.png?compress=1&amp;resize=400x300" alt="Let&#39;s find a place app application browse classy clean find finder iphone location mobile modern places search sleek tracker ui ux visit world xs max">
  </noscript>
  <img alt="Let&#39;s find a place app application browse classy clean find finder iphone location mobile modern places search sleek tracker ui ux visit world xs max" width="330" height="247" data-srcset="https://cdn.dribbble.com/users/1123841/screenshots/6531656/finder-1.0_4x.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/1123841/screenshots/6531656/finder-1.0_4x.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/1123841/screenshots/6531656/finder-1.0_4x.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/1123841/screenshots/6531656/finder-1.0_4x.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/1123841/screenshots/6531656/finder-1.0_4x.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/1123841/screenshots/6531656/finder-1.0_4x.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/1123841/screenshots/6531656/finder-1.0_4x.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/1123841/screenshots/6531656/finder-1.0_4x.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/1123841/screenshots/6531656/finder-1.0_4x.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w" data-src="https://cdn.dribbble.com/users/1123841/screenshots/6531656/finder-1.0_4x.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class="lazyautosizes lazyloaded" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/finder-1.0_4x.png" sizes="295px" srcset="https://cdn.dribbble.com/users/1123841/screenshots/6531656/finder-1.0_4x.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/1123841/screenshots/6531656/finder-1.0_4x.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/1123841/screenshots/6531656/finder-1.0_4x.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/1123841/screenshots/6531656/finder-1.0_4x.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/1123841/screenshots/6531656/finder-1.0_4x.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/1123841/screenshots/6531656/finder-1.0_4x.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/1123841/screenshots/6531656/finder-1.0_4x.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/1123841/screenshots/6531656/finder-1.0_4x.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/1123841/screenshots/6531656/finder-1.0_4x.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w">
</figure>


    <div class="shot-thumbnail-extras">
      
    </div>

      <a class="shot-thumbnail-link dribbble-link js-shot-link" href="https://dribbble.com/shots/6531656-Let-s-find-a-place">
        <span class="accessibility-text">View Let's find a place</span>
</a>
    <div class="shot-thumbnail-overlay">
      <div class="shot-thumbnail-overlay-content">
            <div class="shot-title">Let's find a place</div>

            <ul class="js-dribbble-shot-actions shot-actions-container">
                <li data-bucket-container="true" class="shot-action">
                  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m22 5h-11l-2-3h-7c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h20c1.104 0 2-.896 2-2v-13c0-1.104-.896-2-2-2zm-6 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3v-3c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z"></path></svg>

</a>
                </li>

                

  <div class="like-action-6531656   shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(6531656, {
          likes_count: 57,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

            </ul>
      </div>
    </div>
</div>


    <div class="shot-details-container js-shot-details-container" style="visibility: visible;">
        <div class="user-information">
          <a class="hoverable url" rel="contact" href="https://dribbble.com/fragblo">
            <img class="photo lazyload" alt="Luke Pyrzynski" width="24" height="24" data-src="https://cdn.dribbble.com/users/1123841/avatars/mini/c691e79ca9477ad04ac8877316a02269.jpg?1630238557" src="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail">
            <span class="display-name">Luke Pyrzynski</span>
</a>          <a class="badge-link" href="https://dribbble.com/pro">
  <span class="badge badge-pro">Pro</span>

</a>
        </div>
        <div class="shot-statistics-container js-shot-statistics">

    <div class="shot-statistic js-shot-likes-container">
        

  <div class="like-action-6531656  compact-true shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(6531656, {
          likes_count: 57,
          liked: false
      });
      }
    </script>
      <a class=" tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current shot-tools-icon"><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

      <span class="js-shot-likes-count color-deep-blue-sea-light-20 font-weight-500">57</span>
    </div>
  <div class="shot-statistic js-shot-views-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11" viewBox="0 0 14 11" fill="none" role="img" class="icon fill-current shot-tools-icon">
<path d="M7 3.45833C5.87242 3.45833 4.95833 4.37242 4.95833 5.5C4.95833 6.62758 5.87242 7.54167 7 7.54167C8.12758 7.54167 9.04167 6.62758 9.04167 5.5C9.04167 4.37242 8.12758 3.45833 7 3.45833ZM7 0.25C3.13425 0.25 0 4.625 0 5.5C0 6.375 3.13425 10.75 7 10.75C10.8657 10.75 14 6.375 14 5.5C14 4.625 10.8657 0.25 7 0.25ZM7 8.70833C5.22783 8.70833 3.79167 7.27217 3.79167 5.5C3.79167 3.72783 5.22783 2.29167 7 2.29167C8.77217 2.29167 10.2083 3.72783 10.2083 5.5C10.2083 7.27217 8.77217 8.70833 7 8.70833Z"></path>
</svg>

    <span class="js-shot-views-count color-deep-blue-sea-light-20 font-weight-500">9.8k</span>
  </div>
</div>

    </div>



</li>


<li id="screenshot-8343752" data-thumbnail-id="8343752" class="shot-thumbnail js-thumbnail shot-thumbnail-container      " data-ad-data="" data-boost-id="" data-is-boost-fallback="">
  

<div class="js-thumbnail-base shot-thumbnail-base disabled-shot-section dribbble-shot dribbble video " data-video-teaser-small="https://cdn.dribbble.com/users/1358177/screenshots/8343752/media/f29804eb322f087bb5251adb1324a2ae.mp4" data-video-teaser-medium="https://cdn.dribbble.com/users/1358177/screenshots/8343752/media/fdae29defad815a5350b1ac201be55da.mp4" data-video-teaser-large="https://cdn.dribbble.com/users/1358177/screenshots/8343752/media/b62ef45def5b9561073d5ea710619b02.mp4">
      <figure style="background-color: #EFEFEF" class="js-thumbnail-placeholder shot-thumbnail-placeholder">
  <noscript>
      <img src="https://cdn.dribbble.com/users/1358177/screenshots/8343752/dribbble_1.png?compress=1&amp;resize=400x300" alt="Find nearby Monese users animation bluetooth design fintech interaction monese payment radar ux">
  </noscript>
  <img alt="Find nearby Monese users animation bluetooth design fintech interaction monese payment radar ux" width="330" height="247" data-srcset="https://cdn.dribbble.com/users/1358177/screenshots/8343752/dribbble_1.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/1358177/screenshots/8343752/dribbble_1.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/1358177/screenshots/8343752/dribbble_1.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/1358177/screenshots/8343752/dribbble_1.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/1358177/screenshots/8343752/dribbble_1.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/1358177/screenshots/8343752/dribbble_1.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/1358177/screenshots/8343752/dribbble_1.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/1358177/screenshots/8343752/dribbble_1.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/1358177/screenshots/8343752/dribbble_1.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w" data-src="https://cdn.dribbble.com/users/1358177/screenshots/8343752/dribbble_1.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class="lazyautosizes lazyloaded" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/dribbble_1.png" sizes="295px" srcset="https://cdn.dribbble.com/users/1358177/screenshots/8343752/dribbble_1.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/1358177/screenshots/8343752/dribbble_1.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/1358177/screenshots/8343752/dribbble_1.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/1358177/screenshots/8343752/dribbble_1.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/1358177/screenshots/8343752/dribbble_1.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/1358177/screenshots/8343752/dribbble_1.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/1358177/screenshots/8343752/dribbble_1.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/1358177/screenshots/8343752/dribbble_1.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/1358177/screenshots/8343752/dribbble_1.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w">
</figure>


    <div class="shot-thumbnail-extras">
        <div class="loading-indicator shot-thumbnail-extras-icon"></div>
    <a class="animated-target" href="https://dribbble.com/shots/8343752-Find-nearby-Monese-users">
      <span class="accessibility-text">Shot Link</span>
</a>
    </div>

      <a class="shot-thumbnail-link dribbble-link js-shot-link" href="https://dribbble.com/shots/8343752-Find-nearby-Monese-users">
        <span class="accessibility-text">View Find nearby Monese users</span>
</a>
    <div class="shot-thumbnail-overlay">
      <div class="shot-thumbnail-overlay-content">
            <div class="shot-title">Find nearby Monese users</div>

            <ul class="js-dribbble-shot-actions shot-actions-container">
                <li data-bucket-container="true" class="shot-action">
                  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m22 5h-11l-2-3h-7c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h20c1.104 0 2-.896 2-2v-13c0-1.104-.896-2-2-2zm-6 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3v-3c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z"></path></svg>

</a>
                </li>

                

  <div class="like-action-8343752   shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(8343752, {
          likes_count: 78,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

            </ul>
      </div>
    </div>
</div>


    <div class="shot-details-container js-shot-details-container" style="visibility: visible;">
        <div class="user-information">
          <a class="hoverable url" rel="contact" href="https://dribbble.com/monese">
            <img class="photo lazyload" alt="Monese" width="24" height="24" data-src="https://cdn.dribbble.com/users/1093106/avatars/mini/b69c1dd874ed4a8ce15e04b773f01fae.png?1642609758" src="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail">
            <span class="display-name">Monese</span>
</a>          <a class="badge-link" href="https://dribbble.com/pro">
  <span class="badge badge-team">Team</span>
</a>
        </div>
        <div class="shot-statistics-container js-shot-statistics">

    <div class="shot-statistic js-shot-likes-container">
        

  <div class="like-action-8343752  compact-true shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(8343752, {
          likes_count: 78,
          liked: false
      });
      }
    </script>
      <a class=" tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current shot-tools-icon"><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

      <span class="js-shot-likes-count color-deep-blue-sea-light-20 font-weight-500">78</span>
    </div>
  <div class="shot-statistic js-shot-views-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11" viewBox="0 0 14 11" fill="none" role="img" class="icon fill-current shot-tools-icon">
<path d="M7 3.45833C5.87242 3.45833 4.95833 4.37242 4.95833 5.5C4.95833 6.62758 5.87242 7.54167 7 7.54167C8.12758 7.54167 9.04167 6.62758 9.04167 5.5C9.04167 4.37242 8.12758 3.45833 7 3.45833ZM7 0.25C3.13425 0.25 0 4.625 0 5.5C0 6.375 3.13425 10.75 7 10.75C10.8657 10.75 14 6.375 14 5.5C14 4.625 10.8657 0.25 7 0.25ZM7 8.70833C5.22783 8.70833 3.79167 7.27217 3.79167 5.5C3.79167 3.72783 5.22783 2.29167 7 2.29167C8.77217 2.29167 10.2083 3.72783 10.2083 5.5C10.2083 7.27217 8.77217 8.70833 7 8.70833Z"></path>
</svg>

    <span class="js-shot-views-count color-deep-blue-sea-light-20 font-weight-500">18.1k</span>
  </div>
</div>

    </div>



</li>


<li id="screenshot-4750623" data-thumbnail-id="4750623" class="shot-thumbnail js-thumbnail shot-thumbnail-container      " data-ad-data="" data-boost-id="" data-is-boost-fallback="">
  

<div class="js-thumbnail-base shot-thumbnail-base disabled-shot-section dribbble-shot dribbble  ">
      <figure style="background-color: #F5F6F7" class="js-thumbnail-placeholder shot-thumbnail-placeholder">
  <noscript>
      <img src="https://cdn.dribbble.com/users/1418924/screenshots/4750623/bonus_2_copy.png?compress=1&amp;resize=400x300" alt="Find Food | Restaurent App concept | Sketch food app food search freebie ios app iphone x nearby restaurent app">
  </noscript>
  <img alt="Find Food | Restaurent App concept | Sketch food app food search freebie ios app iphone x nearby restaurent app" width="330" height="247" data-srcset="https://cdn.dribbble.com/users/1418924/screenshots/4750623/bonus_2_copy.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/1418924/screenshots/4750623/bonus_2_copy.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/1418924/screenshots/4750623/bonus_2_copy.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/1418924/screenshots/4750623/bonus_2_copy.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/1418924/screenshots/4750623/bonus_2_copy.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/1418924/screenshots/4750623/bonus_2_copy.png?compress=1&amp;resize=800x600&amp;vertical=top 800w" data-src="https://cdn.dribbble.com/users/1418924/screenshots/4750623/bonus_2_copy.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class=" lazyload" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==">
</figure>


    <div class="shot-thumbnail-extras">
          <div class="has-attachment shot-thumbnail-extras-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" role="img" class="icon ">
<path d="M14.0369 1.96266C13.2016 1.12666 12.0903 0.666656 10.9083 0.666656C9.72559 0.666656 8.61493 1.12666 7.77959 1.96266C7.59093 2.15066 7.48693 2.40066 7.48693 2.66732C7.48693 2.93399 7.59093 3.18399 7.77893 3.37199C7.95959 3.55266 8.21626 3.65599 8.48359 3.65666C8.75093 3.65666 9.00759 3.55266 9.18826 3.37199C10.1043 2.45666 11.7116 2.45666 12.6269 3.37199C13.0856 3.83132 13.3389 4.44199 13.3389 5.09132C13.3389 5.74066 13.0856 6.35132 12.6269 6.81066L6.80959 12.628C5.89359 13.5433 4.28693 13.5433 3.37093 12.628C2.91293 12.168 2.65959 11.558 2.65959 10.9087C2.65959 10.2593 2.91293 9.64866 3.37159 9.18932L5.85026 6.71066C6.13026 6.43199 6.58493 6.43199 6.86493 6.71066C7.00026 6.84599 7.07493 7.02666 7.07493 7.21799C7.07493 7.40999 7.00026 7.59066 6.86493 7.72532L5.09359 9.49666C4.90493 9.68466 4.80093 9.93466 4.80093 10.2013C4.80093 10.468 4.90493 10.718 5.09293 10.906C5.45359 11.2673 6.14159 11.2673 6.50226 10.906L8.27359 9.13466C8.78493 8.62332 9.06693 7.94266 9.06693 7.21799C9.06693 6.49266 8.78559 5.81199 8.27359 5.30132C7.76226 4.78999 7.08159 4.50866 6.35693 4.50866C5.63226 4.50866 4.95293 4.78999 4.44093 5.30066L1.96226 7.77999C1.12626 8.61466 0.66626 9.72666 0.66626 10.9087C0.66626 12.0907 1.12626 13.2013 1.96226 14.0373C2.79759 14.8733 3.90893 15.3333 5.09093 15.3333C6.27359 15.3333 7.38426 14.8733 8.21959 14.0373L14.0369 8.21999C14.8729 7.38532 15.3329 6.27332 15.3329 5.09132C15.3329 3.90932 14.8729 2.79866 14.0369 1.96266V1.96266Z" fill="white"></path>
</svg>

          </div>
      
    </div>

      <a class="shot-thumbnail-link dribbble-link js-shot-link" href="https://dribbble.com/shots/4750623-Find-Food-Restaurent-App-concept-Sketch">
        <span class="accessibility-text">View Find Food | Restaurent App concept | Sketch</span>
</a>
    <div class="shot-thumbnail-overlay">
      <div class="shot-thumbnail-overlay-content">
            <div class="shot-title">Find Food | Restaurent App concept | Sketch</div>

            <ul class="js-dribbble-shot-actions shot-actions-container">
                <li data-bucket-container="true" class="shot-action">
                  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m22 5h-11l-2-3h-7c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h20c1.104 0 2-.896 2-2v-13c0-1.104-.896-2-2-2zm-6 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3v-3c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z"></path></svg>

</a>
                </li>

                

  <div class="like-action-4750623   shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(4750623, {
          likes_count: 71,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

            </ul>
      </div>
    </div>
</div>


    <div class="shot-details-container js-shot-details-container" style="visibility: visible;">
        <div class="user-information">
          <a class="hoverable url" rel="contact" href="https://dribbble.com/Flatastic">
            <img class="photo lazyload" alt="Flatastic" width="24" height="24" data-src="https://cdn.dribbble.com/users/2097200/avatars/mini/3f8142b7a5c9d33995a312d0fdd0419e.png?1516806885" src="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail">
            <span class="display-name">Flatastic</span>
</a>          <a class="badge-link" href="https://dribbble.com/pro">
  <span class="badge badge-team">Team</span>
</a>
        </div>
        <div class="shot-statistics-container js-shot-statistics">

    <div class="shot-statistic js-shot-likes-container">
        

  <div class="like-action-4750623  compact-true shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(4750623, {
          likes_count: 71,
          liked: false
      });
      }
    </script>
      <a class=" tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current shot-tools-icon"><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

      <span class="js-shot-likes-count color-deep-blue-sea-light-20 font-weight-500">71</span>
    </div>
  <div class="shot-statistic js-shot-views-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11" viewBox="0 0 14 11" fill="none" role="img" class="icon fill-current shot-tools-icon">
<path d="M7 3.45833C5.87242 3.45833 4.95833 4.37242 4.95833 5.5C4.95833 6.62758 5.87242 7.54167 7 7.54167C8.12758 7.54167 9.04167 6.62758 9.04167 5.5C9.04167 4.37242 8.12758 3.45833 7 3.45833ZM7 0.25C3.13425 0.25 0 4.625 0 5.5C0 6.375 3.13425 10.75 7 10.75C10.8657 10.75 14 6.375 14 5.5C14 4.625 10.8657 0.25 7 0.25ZM7 8.70833C5.22783 8.70833 3.79167 7.27217 3.79167 5.5C3.79167 3.72783 5.22783 2.29167 7 2.29167C8.77217 2.29167 10.2083 3.72783 10.2083 5.5C10.2083 7.27217 8.77217 8.70833 7 8.70833Z"></path>
</svg>

    <span class="js-shot-views-count color-deep-blue-sea-light-20 font-weight-500">7k</span>
  </div>
</div>

    </div>



</li>


<li id="screenshot-3843786" data-thumbnail-id="3843786" class="shot-thumbnail js-thumbnail shot-thumbnail-container      " data-ad-data="" data-boost-id="" data-is-boost-fallback="">
  

<div class="js-thumbnail-base shot-thumbnail-base disabled-shot-section dribbble-shot dribbble  ">
      <figure style="background-color: #FAFAFC" class="js-thumbnail-placeholder shot-thumbnail-placeholder">
  <noscript>
      <img src="https://cdn.dribbble.com/users/1828625/screenshots/3843786/media/7a4d8654df3db3e7dae41de1b948a2da.png?compress=1&amp;resize=400x300" alt="Neighby app kit find gradient ios near.app nearby neighby ui">
  </noscript>
  <img alt="Neighby app kit find gradient ios near.app nearby neighby ui" width="330" height="247" data-srcset="https://cdn.dribbble.com/users/1828625/screenshots/3843786/media/7a4d8654df3db3e7dae41de1b948a2da.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/1828625/screenshots/3843786/media/7a4d8654df3db3e7dae41de1b948a2da.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/1828625/screenshots/3843786/media/7a4d8654df3db3e7dae41de1b948a2da.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/1828625/screenshots/3843786/media/7a4d8654df3db3e7dae41de1b948a2da.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/1828625/screenshots/3843786/media/7a4d8654df3db3e7dae41de1b948a2da.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/1828625/screenshots/3843786/media/7a4d8654df3db3e7dae41de1b948a2da.png?compress=1&amp;resize=800x600&amp;vertical=top 800w" data-src="https://cdn.dribbble.com/users/1828625/screenshots/3843786/media/7a4d8654df3db3e7dae41de1b948a2da.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class=" lazyload" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==">
</figure>


    <div class="shot-thumbnail-extras">
      
    </div>

      <a class="shot-thumbnail-link dribbble-link js-shot-link" href="https://dribbble.com/shots/3843786-Neighby-app-kit">
        <span class="accessibility-text">View Neighby app kit</span>
</a>
    <div class="shot-thumbnail-overlay">
      <div class="shot-thumbnail-overlay-content">
            <div class="shot-title">Neighby app kit</div>

            <ul class="js-dribbble-shot-actions shot-actions-container">
                <li data-bucket-container="true" class="shot-action">
                  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m22 5h-11l-2-3h-7c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h20c1.104 0 2-.896 2-2v-13c0-1.104-.896-2-2-2zm-6 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3v-3c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z"></path></svg>

</a>
                </li>

                

  <div class="like-action-3843786   shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(3843786, {
          likes_count: 91,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

            </ul>
      </div>
    </div>
</div>


    <div class="shot-details-container js-shot-details-container" style="visibility: visible;">
        <div class="user-information">
          <a class="hoverable url" rel="contact" href="https://dribbble.com/pravin_rj">
            <img class="photo lazyload" alt="Praveen N" width="24" height="24" data-src="https://cdn.dribbble.com/users/1828625/avatars/mini/33f1be049cfc7f11b17e87764a8d9117.jpg?1632155710" src="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail">
            <span class="display-name">Praveen N</span>
</a>          <a class="badge-link" href="https://dribbble.com/pro">
  <span class="badge badge-pro">Pro</span>

</a>
        </div>
        <div class="shot-statistics-container js-shot-statistics">

    <div class="shot-statistic js-shot-likes-container">
        

  <div class="like-action-3843786  compact-true shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(3843786, {
          likes_count: 91,
          liked: false
      });
      }
    </script>
      <a class=" tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current shot-tools-icon"><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

      <span class="js-shot-likes-count color-deep-blue-sea-light-20 font-weight-500">91</span>
    </div>
  <div class="shot-statistic js-shot-views-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11" viewBox="0 0 14 11" fill="none" role="img" class="icon fill-current shot-tools-icon">
<path d="M7 3.45833C5.87242 3.45833 4.95833 4.37242 4.95833 5.5C4.95833 6.62758 5.87242 7.54167 7 7.54167C8.12758 7.54167 9.04167 6.62758 9.04167 5.5C9.04167 4.37242 8.12758 3.45833 7 3.45833ZM7 0.25C3.13425 0.25 0 4.625 0 5.5C0 6.375 3.13425 10.75 7 10.75C10.8657 10.75 14 6.375 14 5.5C14 4.625 10.8657 0.25 7 0.25ZM7 8.70833C5.22783 8.70833 3.79167 7.27217 3.79167 5.5C3.79167 3.72783 5.22783 2.29167 7 2.29167C8.77217 2.29167 10.2083 3.72783 10.2083 5.5C10.2083 7.27217 8.77217 8.70833 7 8.70833Z"></path>
</svg>

    <span class="js-shot-views-count color-deep-blue-sea-light-20 font-weight-500">4.2k</span>
  </div>
</div>

    </div>



</li>


<li id="screenshot-3909269" data-thumbnail-id="3909269" class="shot-thumbnail js-thumbnail shot-thumbnail-container      " data-ad-data="" data-boost-id="" data-is-boost-fallback="">
  

<div class="js-thumbnail-base shot-thumbnail-base disabled-shot-section dribbble-shot dribbble  ">
      <figure style="background-color: #F8F9FC" class="js-thumbnail-placeholder shot-thumbnail-placeholder">
  <noscript>
      <img src="https://cdn.dribbble.com/users/937198/screenshots/3909269/5.png?compress=1&amp;resize=400x300" alt="Barbara App Concept - Find The Perfect Barberman barber chat clean discover inbox ios minimal mobile app nearby profile ui design ux design">
  </noscript>
  <img alt="Barbara App Concept - Find The Perfect Barberman barber chat clean discover inbox ios minimal mobile app nearby profile ui design ux design" width="330" height="247" data-srcset="https://cdn.dribbble.com/users/937198/screenshots/3909269/5.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/937198/screenshots/3909269/5.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/937198/screenshots/3909269/5.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/937198/screenshots/3909269/5.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/937198/screenshots/3909269/5.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/937198/screenshots/3909269/5.png?compress=1&amp;resize=800x600&amp;vertical=top 800w" data-src="https://cdn.dribbble.com/users/937198/screenshots/3909269/5.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class=" lazyload" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==">
</figure>


    <div class="shot-thumbnail-extras">
          <div class="has-attachment shot-thumbnail-extras-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" role="img" class="icon ">
<path d="M14.0369 1.96266C13.2016 1.12666 12.0903 0.666656 10.9083 0.666656C9.72559 0.666656 8.61493 1.12666 7.77959 1.96266C7.59093 2.15066 7.48693 2.40066 7.48693 2.66732C7.48693 2.93399 7.59093 3.18399 7.77893 3.37199C7.95959 3.55266 8.21626 3.65599 8.48359 3.65666C8.75093 3.65666 9.00759 3.55266 9.18826 3.37199C10.1043 2.45666 11.7116 2.45666 12.6269 3.37199C13.0856 3.83132 13.3389 4.44199 13.3389 5.09132C13.3389 5.74066 13.0856 6.35132 12.6269 6.81066L6.80959 12.628C5.89359 13.5433 4.28693 13.5433 3.37093 12.628C2.91293 12.168 2.65959 11.558 2.65959 10.9087C2.65959 10.2593 2.91293 9.64866 3.37159 9.18932L5.85026 6.71066C6.13026 6.43199 6.58493 6.43199 6.86493 6.71066C7.00026 6.84599 7.07493 7.02666 7.07493 7.21799C7.07493 7.40999 7.00026 7.59066 6.86493 7.72532L5.09359 9.49666C4.90493 9.68466 4.80093 9.93466 4.80093 10.2013C4.80093 10.468 4.90493 10.718 5.09293 10.906C5.45359 11.2673 6.14159 11.2673 6.50226 10.906L8.27359 9.13466C8.78493 8.62332 9.06693 7.94266 9.06693 7.21799C9.06693 6.49266 8.78559 5.81199 8.27359 5.30132C7.76226 4.78999 7.08159 4.50866 6.35693 4.50866C5.63226 4.50866 4.95293 4.78999 4.44093 5.30066L1.96226 7.77999C1.12626 8.61466 0.66626 9.72666 0.66626 10.9087C0.66626 12.0907 1.12626 13.2013 1.96226 14.0373C2.79759 14.8733 3.90893 15.3333 5.09093 15.3333C6.27359 15.3333 7.38426 14.8733 8.21959 14.0373L14.0369 8.21999C14.8729 7.38532 15.3329 6.27332 15.3329 5.09132C15.3329 3.90932 14.8729 2.79866 14.0369 1.96266V1.96266Z" fill="white"></path>
</svg>

          </div>
      
    </div>

      <a class="shot-thumbnail-link dribbble-link js-shot-link" href="https://dribbble.com/shots/3909269-Barbara-App-Concept-Find-The-Perfect-Barberman">
        <span class="accessibility-text">View Barbara App Concept - Find The Perfect Barberman</span>
</a>
    <div class="shot-thumbnail-overlay">
      <div class="shot-thumbnail-overlay-content">
            <div class="shot-title">Barbara App Concept - Find The Perfect Barberman</div>

            <ul class="js-dribbble-shot-actions shot-actions-container">
                <li data-bucket-container="true" class="shot-action">
                  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m22 5h-11l-2-3h-7c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h20c1.104 0 2-.896 2-2v-13c0-1.104-.896-2-2-2zm-6 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3v-3c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z"></path></svg>

</a>
                </li>

                

  <div class="like-action-3909269   shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(3909269, {
          likes_count: 146,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

            </ul>
      </div>
    </div>
</div>


    <div class="shot-details-container js-shot-details-container" style="visibility: visible;">
        <div class="user-information">
          <a class="hoverable url" rel="contact" href="https://dribbble.com/ahmadfawaid">
            <img class="photo lazyload" alt="Ahmad Fawaid" width="24" height="24" data-src="https://cdn.dribbble.com/users/937198/avatars/mini/b81995e5a4ffd38a351a38e266bdba19.jpg?1640183012" src="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail">
            <span class="display-name">Ahmad Fawaid</span>
</a>          <a class="badge-link" href="https://dribbble.com/pro">
  <span class="badge badge-pro">Pro</span>

</a>
        </div>
        <div class="shot-statistics-container js-shot-statistics">

    <div class="shot-statistic js-shot-likes-container">
        

  <div class="like-action-3909269  compact-true shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(3909269, {
          likes_count: 146,
          liked: false
      });
      }
    </script>
      <a class=" tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current shot-tools-icon"><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

      <span class="js-shot-likes-count color-deep-blue-sea-light-20 font-weight-500">146</span>
    </div>
  <div class="shot-statistic js-shot-views-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11" viewBox="0 0 14 11" fill="none" role="img" class="icon fill-current shot-tools-icon">
<path d="M7 3.45833C5.87242 3.45833 4.95833 4.37242 4.95833 5.5C4.95833 6.62758 5.87242 7.54167 7 7.54167C8.12758 7.54167 9.04167 6.62758 9.04167 5.5C9.04167 4.37242 8.12758 3.45833 7 3.45833ZM7 0.25C3.13425 0.25 0 4.625 0 5.5C0 6.375 3.13425 10.75 7 10.75C10.8657 10.75 14 6.375 14 5.5C14 4.625 10.8657 0.25 7 0.25ZM7 8.70833C5.22783 8.70833 3.79167 7.27217 3.79167 5.5C3.79167 3.72783 5.22783 2.29167 7 2.29167C8.77217 2.29167 10.2083 3.72783 10.2083 5.5C10.2083 7.27217 8.77217 8.70833 7 8.70833Z"></path>
</svg>

    <span class="js-shot-views-count color-deep-blue-sea-light-20 font-weight-500">14.4k</span>
  </div>
</div>

    </div>



</li>


<li id="screenshot-14614588" data-thumbnail-id="14614588" class="shot-thumbnail js-thumbnail shot-thumbnail-container      " data-ad-data="" data-boost-id="" data-is-boost-fallback="">
  

<div class="js-thumbnail-base shot-thumbnail-base disabled-shot-section dribbble-shot dribbble  ">
      <figure style="background-color: #D3BC8D" class="js-thumbnail-placeholder shot-thumbnail-placeholder">
  <noscript>
      <img src="https://cdn.dribbble.com/users/4930678/screenshots/14614588/media/a3a136afdd6151b79e12715b134114c0.png?compress=1&amp;resize=400x300" alt="Find great attractions in nearby area application design ios mobile ui uiux ux">
  </noscript>
  <img alt="Find great attractions in nearby area application design ios mobile ui uiux ux" width="330" height="247" data-srcset="https://cdn.dribbble.com/users/4930678/screenshots/14614588/media/a3a136afdd6151b79e12715b134114c0.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/4930678/screenshots/14614588/media/a3a136afdd6151b79e12715b134114c0.png?compress=1&amp;resize=400x300&amp;vertical=top 400w" data-src="https://cdn.dribbble.com/users/4930678/screenshots/14614588/media/a3a136afdd6151b79e12715b134114c0.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class=" lazyload" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==">
</figure>


    <div class="shot-thumbnail-extras">
      
    </div>

      <a class="shot-thumbnail-link dribbble-link js-shot-link" href="https://dribbble.com/shots/14614588-Find-great-attractions-in-nearby-area">
        <span class="accessibility-text">View Find great attractions in nearby area</span>
</a>
    <div class="shot-thumbnail-overlay">
      <div class="shot-thumbnail-overlay-content">
            <div class="shot-title">Find great attractions in nearby area</div>

            <ul class="js-dribbble-shot-actions shot-actions-container">
                <li data-bucket-container="true" class="shot-action">
                  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m22 5h-11l-2-3h-7c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h20c1.104 0 2-.896 2-2v-13c0-1.104-.896-2-2-2zm-6 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3v-3c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z"></path></svg>

</a>
                </li>

                

  <div class="like-action-14614588   shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(14614588, {
          likes_count: 21,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

            </ul>
      </div>
    </div>
</div>


    <div class="shot-details-container js-shot-details-container" style="visibility: visible;">
        <div class="user-information">
          <a class="hoverable url" rel="contact" href="https://dribbble.com/OllieVak">
            <img class="photo lazyload" alt="Ollie Vakhniy" width="24" height="24" data-src="https://cdn.dribbble.com/users/4930678/avatars/mini/404846667c2fd40a7232521796b1474b.jpg?1614793981" src="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail">
            <span class="display-name">Ollie Vakhniy</span>
</a>          
        </div>
        <div class="shot-statistics-container js-shot-statistics">

    <div class="shot-statistic js-shot-likes-container">
        

  <div class="like-action-14614588  compact-true shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(14614588, {
          likes_count: 21,
          liked: false
      });
      }
    </script>
      <a class=" tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current shot-tools-icon"><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

      <span class="js-shot-likes-count color-deep-blue-sea-light-20 font-weight-500">21</span>
    </div>
  <div class="shot-statistic js-shot-views-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11" viewBox="0 0 14 11" fill="none" role="img" class="icon fill-current shot-tools-icon">
<path d="M7 3.45833C5.87242 3.45833 4.95833 4.37242 4.95833 5.5C4.95833 6.62758 5.87242 7.54167 7 7.54167C8.12758 7.54167 9.04167 6.62758 9.04167 5.5C9.04167 4.37242 8.12758 3.45833 7 3.45833ZM7 0.25C3.13425 0.25 0 4.625 0 5.5C0 6.375 3.13425 10.75 7 10.75C10.8657 10.75 14 6.375 14 5.5C14 4.625 10.8657 0.25 7 0.25ZM7 8.70833C5.22783 8.70833 3.79167 7.27217 3.79167 5.5C3.79167 3.72783 5.22783 2.29167 7 2.29167C8.77217 2.29167 10.2083 3.72783 10.2083 5.5C10.2083 7.27217 8.77217 8.70833 7 8.70833Z"></path>
</svg>

    <span class="js-shot-views-count color-deep-blue-sea-light-20 font-weight-500">3.1k</span>
  </div>
</div>

    </div>



</li>


<li id="screenshot-4020293" data-thumbnail-id="4020293" class="shot-thumbnail js-thumbnail shot-thumbnail-container      " data-ad-data="" data-boost-id="" data-is-boost-fallback="">
  

<div class="js-thumbnail-base shot-thumbnail-base disabled-shot-section dribbble-shot dribbble  ">
      <figure style="background-color: #F2EDF5" class="js-thumbnail-placeholder shot-thumbnail-placeholder">
  <noscript>
      <img src="https://cdn.dribbble.com/users/18903/screenshots/4020293/media/772bafc447ca2a3707e99e4db3c46b9b.png?compress=1&amp;resize=400x300" alt="Find People Nearby dating gps hangouts iphone app iphone x location map meeting meetups mobile app nearby people">
  </noscript>
  <img alt="Find People Nearby dating gps hangouts iphone app iphone x location map meeting meetups mobile app nearby people" width="330" height="247" data-srcset="https://cdn.dribbble.com/users/18903/screenshots/4020293/media/772bafc447ca2a3707e99e4db3c46b9b.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/18903/screenshots/4020293/media/772bafc447ca2a3707e99e4db3c46b9b.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/18903/screenshots/4020293/media/772bafc447ca2a3707e99e4db3c46b9b.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/18903/screenshots/4020293/media/772bafc447ca2a3707e99e4db3c46b9b.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/18903/screenshots/4020293/media/772bafc447ca2a3707e99e4db3c46b9b.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/18903/screenshots/4020293/media/772bafc447ca2a3707e99e4db3c46b9b.png?compress=1&amp;resize=800x600&amp;vertical=top 800w" data-src="https://cdn.dribbble.com/users/18903/screenshots/4020293/media/772bafc447ca2a3707e99e4db3c46b9b.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class=" lazyload" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==">
</figure>


    <div class="shot-thumbnail-extras">
          <div class="is-rebound shot-thumbnail-extras-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" role="img" class="icon ">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 8C0 12.4183 3.58172 16 8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0C3.58172 0 0 3.58172 0 8ZM8.16504 6.24902V5.06161C8.16585 4.89576 8.09142 4.73031 7.94336 4.60411C7.64766 4.35128 7.16872 4.35128 6.87302 4.60411L3.31087 7.6505L6.87302 10.6969C7.02107 10.8231 7.21443 10.8866 7.40819 10.8866C7.60195 10.8866 7.79531 10.8235 7.94336 10.6969C8.09142 10.5707 8.16585 10.4048 8.16504 10.2394V8.98145C10.8239 9.1121 12.2102 10.7855 12.2102 10.7855C12.2102 8.0174 10.5161 6.43227 8.16504 6.24902Z" fill="white"></path>
</svg>

          </div>
      
    </div>

      <a class="shot-thumbnail-link dribbble-link js-shot-link" href="https://dribbble.com/shots/4020293-Find-People-Nearby">
        <span class="accessibility-text">View Find People Nearby</span>
</a>
    <div class="shot-thumbnail-overlay">
      <div class="shot-thumbnail-overlay-content">
            <div class="shot-title">Find People Nearby</div>

            <ul class="js-dribbble-shot-actions shot-actions-container">
                <li data-bucket-container="true" class="shot-action">
                  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m22 5h-11l-2-3h-7c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h20c1.104 0 2-.896 2-2v-13c0-1.104-.896-2-2-2zm-6 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3v-3c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z"></path></svg>

</a>
                </li>

                

  <div class="like-action-4020293   shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(4020293, {
          likes_count: 78,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

            </ul>
      </div>
    </div>
</div>


    <div class="shot-details-container js-shot-details-container" style="visibility: visible;">
        <div class="user-information">
          <a class="hoverable url" rel="contact" href="https://dribbble.com/najdenovski">
            <img class="photo lazyload" alt="Zlatko Najdenovski" width="24" height="24" data-src="https://cdn.dribbble.com/users/18903/avatars/mini/7c42fe69ddd202f99910b5326839128a.jpg?1620189875" src="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail">
            <span class="display-name">Zlatko Najdenovski</span>
</a>          <a class="badge-link" href="https://dribbble.com/pro">
  <span class="badge badge-pro">Pro</span>

</a>
        </div>
        <div class="shot-statistics-container js-shot-statistics">

    <div class="shot-statistic js-shot-likes-container">
        

  <div class="like-action-4020293  compact-true shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(4020293, {
          likes_count: 78,
          liked: false
      });
      }
    </script>
      <a class=" tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current shot-tools-icon"><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

      <span class="js-shot-likes-count color-deep-blue-sea-light-20 font-weight-500">78</span>
    </div>
  <div class="shot-statistic js-shot-views-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11" viewBox="0 0 14 11" fill="none" role="img" class="icon fill-current shot-tools-icon">
<path d="M7 3.45833C5.87242 3.45833 4.95833 4.37242 4.95833 5.5C4.95833 6.62758 5.87242 7.54167 7 7.54167C8.12758 7.54167 9.04167 6.62758 9.04167 5.5C9.04167 4.37242 8.12758 3.45833 7 3.45833ZM7 0.25C3.13425 0.25 0 4.625 0 5.5C0 6.375 3.13425 10.75 7 10.75C10.8657 10.75 14 6.375 14 5.5C14 4.625 10.8657 0.25 7 0.25ZM7 8.70833C5.22783 8.70833 3.79167 7.27217 3.79167 5.5C3.79167 3.72783 5.22783 2.29167 7 2.29167C8.77217 2.29167 10.2083 3.72783 10.2083 5.5C10.2083 7.27217 8.77217 8.70833 7 8.70833Z"></path>
</svg>

    <span class="js-shot-views-count color-deep-blue-sea-light-20 font-weight-500">9.5k</span>
  </div>
</div>

    </div>



</li>


<li id="screenshot-4696472" data-thumbnail-id="4696472" class="shot-thumbnail js-thumbnail shot-thumbnail-container      " data-ad-data="" data-boost-id="" data-is-boost-fallback="">
  

<div class="js-thumbnail-base shot-thumbnail-base disabled-shot-section dribbble-shot dribbble  ">
      <figure style="background-color: #F1F1F1" class="js-thumbnail-placeholder shot-thumbnail-placeholder">
  <noscript>
      <img src="https://cdn.dribbble.com/users/1998372/screenshots/4696472/hack_secondshot_800x600.png?compress=1&amp;resize=400x300" alt="Hack - Find and organize hackathons app concept event find hack hackathon interaction minimalist nearby search ui ux">
  </noscript>
  <img alt="Hack - Find and organize hackathons app concept event find hack hackathon interaction minimalist nearby search ui ux" width="330" height="247" data-srcset="https://cdn.dribbble.com/users/1998372/screenshots/4696472/hack_secondshot_800x600.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/1998372/screenshots/4696472/hack_secondshot_800x600.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/1998372/screenshots/4696472/hack_secondshot_800x600.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/1998372/screenshots/4696472/hack_secondshot_800x600.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/1998372/screenshots/4696472/hack_secondshot_800x600.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/1998372/screenshots/4696472/hack_secondshot_800x600.png?compress=1&amp;resize=800x600&amp;vertical=top 800w" data-src="https://cdn.dribbble.com/users/1998372/screenshots/4696472/hack_secondshot_800x600.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class=" lazyload" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==">
</figure>


    <div class="shot-thumbnail-extras">
          <div class="has-attachment shot-thumbnail-extras-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" role="img" class="icon ">
<path d="M14.0369 1.96266C13.2016 1.12666 12.0903 0.666656 10.9083 0.666656C9.72559 0.666656 8.61493 1.12666 7.77959 1.96266C7.59093 2.15066 7.48693 2.40066 7.48693 2.66732C7.48693 2.93399 7.59093 3.18399 7.77893 3.37199C7.95959 3.55266 8.21626 3.65599 8.48359 3.65666C8.75093 3.65666 9.00759 3.55266 9.18826 3.37199C10.1043 2.45666 11.7116 2.45666 12.6269 3.37199C13.0856 3.83132 13.3389 4.44199 13.3389 5.09132C13.3389 5.74066 13.0856 6.35132 12.6269 6.81066L6.80959 12.628C5.89359 13.5433 4.28693 13.5433 3.37093 12.628C2.91293 12.168 2.65959 11.558 2.65959 10.9087C2.65959 10.2593 2.91293 9.64866 3.37159 9.18932L5.85026 6.71066C6.13026 6.43199 6.58493 6.43199 6.86493 6.71066C7.00026 6.84599 7.07493 7.02666 7.07493 7.21799C7.07493 7.40999 7.00026 7.59066 6.86493 7.72532L5.09359 9.49666C4.90493 9.68466 4.80093 9.93466 4.80093 10.2013C4.80093 10.468 4.90493 10.718 5.09293 10.906C5.45359 11.2673 6.14159 11.2673 6.50226 10.906L8.27359 9.13466C8.78493 8.62332 9.06693 7.94266 9.06693 7.21799C9.06693 6.49266 8.78559 5.81199 8.27359 5.30132C7.76226 4.78999 7.08159 4.50866 6.35693 4.50866C5.63226 4.50866 4.95293 4.78999 4.44093 5.30066L1.96226 7.77999C1.12626 8.61466 0.66626 9.72666 0.66626 10.9087C0.66626 12.0907 1.12626 13.2013 1.96226 14.0373C2.79759 14.8733 3.90893 15.3333 5.09093 15.3333C6.27359 15.3333 7.38426 14.8733 8.21959 14.0373L14.0369 8.21999C14.8729 7.38532 15.3329 6.27332 15.3329 5.09132C15.3329 3.90932 14.8729 2.79866 14.0369 1.96266V1.96266Z" fill="white"></path>
</svg>

          </div>
      
    </div>

      <a class="shot-thumbnail-link dribbble-link js-shot-link" href="https://dribbble.com/shots/4696472-Hack-Find-and-organize-hackathons">
        <span class="accessibility-text">View Hack - Find and organize hackathons</span>
</a>
    <div class="shot-thumbnail-overlay">
      <div class="shot-thumbnail-overlay-content">
            <div class="shot-title">Hack - Find and organize hackathons</div>

            <ul class="js-dribbble-shot-actions shot-actions-container">
                <li data-bucket-container="true" class="shot-action">
                  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m22 5h-11l-2-3h-7c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h20c1.104 0 2-.896 2-2v-13c0-1.104-.896-2-2-2zm-6 10h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3h-3c-.55 0-1-.45-1-1s.45-1 1-1h3v-3c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z"></path></svg>

</a>
                </li>

                

  <div class="like-action-4696472   shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(4696472, {
          likes_count: 96,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

            </ul>
      </div>
    </div>
</div>


    <div class="shot-details-container js-shot-details-container" style="visibility: visible;">
        <div class="user-information">
          <a class="hoverable url" rel="contact" href="https://dribbble.com/lorenzodolfi">
            <img class="photo lazyload" alt="Lorenzo Dolfi" width="24" height="24" data-src="https://cdn.dribbble.com/users/1998372/avatars/mini/002dbea2fd825fef65e99f0eeb5e7488.jpg?1511276459" src="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail">
            <span class="display-name">Lorenzo Dolfi</span>
</a>          
        </div>
        <div class="shot-statistics-container js-shot-statistics">

    <div class="shot-statistic js-shot-likes-container">
        

  <div class="like-action-4696472  compact-true shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(4696472, {
          likes_count: 96,
          liked: false
      });
      }
    </script>
      <a class=" tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current shot-tools-icon"><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          <span class="accessibility-text">Like</span>
</a>  </div>

      <span class="js-shot-likes-count color-deep-blue-sea-light-20 font-weight-500">96</span>
    </div>
  <div class="shot-statistic js-shot-views-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11" viewBox="0 0 14 11" fill="none" role="img" class="icon fill-current shot-tools-icon">
<path d="M7 3.45833C5.87242 3.45833 4.95833 4.37242 4.95833 5.5C4.95833 6.62758 5.87242 7.54167 7 7.54167C8.12758 7.54167 9.04167 6.62758 9.04167 5.5C9.04167 4.37242 8.12758 3.45833 7 3.45833ZM7 0.25C3.13425 0.25 0 4.625 0 5.5C0 6.375 3.13425 10.75 7 10.75C10.8657 10.75 14 6.375 14 5.5C14 4.625 10.8657 0.25 7 0.25ZM7 8.70833C5.22783 8.70833 3.79167 7.27217 3.79167 5.5C3.79167 3.72783 5.22783 2.29167 7 2.29167C8.77217 2.29167 10.2083 3.72783 10.2083 5.5C10.2083 7.27217 8.77217 8.70833 7 8.70833Z"></path>
</svg>

    <span class="js-shot-views-count color-deep-blue-sea-light-20 font-weight-500">9.2k</span>
  </div>
</div>

    </div>



</li>








<script>
var newestShots = [{
    id: 16490714,
    title: "Parking Mobile App Exploration",
    path: "/shots/16490714-Parking-Mobile-App-Exploration",
    published_at: 'September 20, 2021',
    is_rebound: true,
    rebounds_count: 0,
    attachments_count: 0,
    view_count: '72.9k',
    comments_count: '39',
    likes_count: '320',
    liked: false,
    ga: []
  },{
    id: 17116279,
    title: "Search items nearby app",
    path: "/shots/17116279-Search-items-nearby-app",
    published_at: 'December 22, 2021',
    is_rebound: false,
    rebounds_count: 0,
    attachments_count: 0,
    view_count: '12.2k',
    comments_count: '2',
    likes_count: '32',
    liked: false,
    ga: []
  },{
    id: 15133535,
    title: "Inner. Find out how your feelings matches with people nearby",
    path: "/shots/15133535-Inner-Find-out-how-your-feelings-matches-with-people-nearby",
    published_at: 'February 16, 2021',
    is_rebound: false,
    rebounds_count: 1,
    attachments_count: 0,
    view_count: '61.3k',
    comments_count: '23',
    likes_count: '241',
    liked: false,
    ga: []
  },{
    id: 7217612,
    title: "Travel App - Search",
    path: "/shots/7217612-Travel-App-Search",
    published_at: 'September 26, 2019',
    is_rebound: false,
    rebounds_count: 0,
    attachments_count: 0,
    view_count: '91.7k',
    comments_count: '0',
    likes_count: '191',
    liked: false,
    ga: [["plat4m","UA-17021718-8"]]
  },{
    id: 3388355,
    title: "My Nearest Places Ios App Icon",
    path: "/shots/3388355-My-Nearest-Places-Ios-App-Icon",
    published_at: 'March 25, 2017',
    is_rebound: false,
    rebounds_count: 0,
    attachments_count: 0,
    view_count: '1.6k',
    comments_count: '0',
    likes_count: '31',
    liked: false,
    ga: []
  },{
    id: 5556684,
    title: "Find food nearby",
    path: "/shots/5556684-Find-food-nearby",
    published_at: 'November 15, 2018',
    is_rebound: false,
    rebounds_count: 0,
    attachments_count: 3,
    view_count: '125k',
    comments_count: '25',
    likes_count: '667',
    liked: false,
    ga: []
  },{
    id: 10801819,
    title: "Find Your Doctor / Mobile UI Concept",
    path: "/shots/10801819-Find-Your-Doctor-Mobile-UI-Concept",
    published_at: 'March 22, 2020',
    is_rebound: false,
    rebounds_count: 0,
    attachments_count: 0,
    view_count: '52.8k',
    comments_count: '5',
    likes_count: '167',
    liked: false,
    ga: []
  },{
    id: 16161060,
    title: "Places to dine search app",
    path: "/shots/16161060-Places-to-dine-search-app",
    published_at: 'August 02, 2021',
    is_rebound: false,
    rebounds_count: 0,
    attachments_count: 0,
    view_count: '17.7k',
    comments_count: '2',
    likes_count: '92',
    liked: false,
    ga: [["andersen-design","UA-72753011-14"]]
  },{
    id: 17116311,
    title: "Search items nearby app",
    path: "/shots/17116311-Search-items-nearby-app",
    published_at: 'December 22, 2021',
    is_rebound: false,
    rebounds_count: 0,
    attachments_count: 0,
    view_count: '4.2k',
    comments_count: '0',
    likes_count: '16',
    liked: false,
    ga: []
  },{
    id: 15541255,
    title: "Home Find us Logo design | real estate | branding brand identity",
    path: "/shots/15541255-Home-Find-us-Logo-design-real-estate-branding-brand-identity",
    published_at: 'April 25, 2021',
    is_rebound: false,
    rebounds_count: 1,
    attachments_count: 0,
    view_count: '40.9k',
    comments_count: '12',
    likes_count: '93',
    liked: false,
    ga: []
  },{
    id: 3816653,
    title: "Find Gym \u0026 Trainer Nearby",
    path: "/shots/3816653-Find-Gym-Trainer-Nearby",
    published_at: 'September 19, 2017',
    is_rebound: false,
    rebounds_count: 0,
    attachments_count: 0,
    view_count: '13.1k',
    comments_count: '7',
    likes_count: '143',
    liked: false,
    ga: []
  },{
    id: 14354807,
    title: "Doctor Finding App",
    path: "/shots/14354807-Doctor-Finding-App",
    published_at: 'October 09, 2020',
    is_rebound: false,
    rebounds_count: 0,
    attachments_count: 0,
    view_count: '32.9k',
    comments_count: '5',
    likes_count: '116',
    liked: false,
    ga: []
  },{
    id: 6531656,
    title: "Let's find a place",
    path: "/shots/6531656-Let-s-find-a-place",
    published_at: 'May 24, 2019',
    is_rebound: false,
    rebounds_count: 0,
    attachments_count: 0,
    view_count: '9.8k',
    comments_count: '1',
    likes_count: '57',
    liked: false,
    ga: []
  },{
    id: 8343752,
    title: "Find nearby Monese users",
    path: "/shots/8343752-Find-nearby-Monese-users",
    published_at: 'November 19, 2019',
    is_rebound: false,
    rebounds_count: 0,
    attachments_count: 0,
    view_count: '18.1k',
    comments_count: '1',
    likes_count: '78',
    liked: false,
    ga: []
  },{
    id: 4750623,
    title: "Find Food | Restaurent App concept | Sketch",
    path: "/shots/4750623-Find-Food-Restaurent-App-concept-Sketch",
    published_at: 'June 26, 2018',
    is_rebound: false,
    rebounds_count: 0,
    attachments_count: 1,
    view_count: '7k',
    comments_count: '4',
    likes_count: '71',
    liked: false,
    ga: [["Flatastic","UA-128942952-4"]]
  },{
    id: 3843786,
    title: "Neighby app kit",
    path: "/shots/3843786-Neighby-app-kit",
    published_at: 'October 01, 2017',
    is_rebound: false,
    rebounds_count: 0,
    attachments_count: 0,
    view_count: '4.2k',
    comments_count: '2',
    likes_count: '91',
    liked: false,
    ga: [["pravin_rj","UA-128994615-1"]]
  },{
    id: 3909269,
    title: "Barbara App Concept - Find The Perfect Barberman",
    path: "/shots/3909269-Barbara-App-Concept-Find-The-Perfect-Barberman",
    published_at: 'October 30, 2017',
    is_rebound: false,
    rebounds_count: 0,
    attachments_count: 1,
    view_count: '14.4k',
    comments_count: '10',
    likes_count: '146',
    liked: false,
    ga: [["ahmadfawaid","UA-161432762-1"]]
  },{
    id: 14614588,
    title: "Find great attractions in nearby area",
    path: "/shots/14614588-Find-great-attractions-in-nearby-area",
    published_at: 'November 20, 2020',
    is_rebound: false,
    rebounds_count: 0,
    attachments_count: 0,
    view_count: '3.1k',
    comments_count: '4',
    likes_count: '21',
    liked: false,
    ga: []
  },{
    id: 4020293,
    title: "Find People Nearby",
    path: "/shots/4020293-Find-People-Nearby",
    published_at: 'December 14, 2017',
    is_rebound: true,
    rebounds_count: 0,
    attachments_count: 0,
    view_count: '9.5k',
    comments_count: '0',
    likes_count: '78',
    liked: false,
    ga: [["najdenovski","UA-169829319-1"]]
  },{
    id: 4696472,
    title: "Hack - Find and organize hackathons",
    path: "/shots/4696472-Hack-Find-and-organize-hackathons",
    published_at: 'June 14, 2018',
    is_rebound: false,
    rebounds_count: 0,
    attachments_count: 2,
    view_count: '9.2k',
    comments_count: '7',
    likes_count: '96',
    liked: false,
    ga: []
  },{
    id: 3831671,
    title: "Onboarding screens",
    path: "/shots/3831671-Onboarding-screens",
    published_at: 'September 26, 2017',
    is_rebound: false,
    rebounds_count: 0,
    attachments_count: 0,
    view_count: '20.1k',
    comments_count: '4',
    likes_count: '156',
    liked: false,
    ga: [["pravin_rj","UA-128994615-1"]]
  },{
    id: 4915385,
    title: "Healthcare Mobile App - Find Nearby Doctor",
    path: "/shots/4915385-Healthcare-Mobile-App-Find-Nearby-Doctor",
    published_at: 'August 05, 2018',
    is_rebound: false,
    rebounds_count: 0,
    attachments_count: 0,
    view_count: '5k',
    comments_count: '6',
    likes_count: '66',
    liked: false,
    ga: []
  }];

if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
  Dribbble.Shots.add(newestShots);
  Dribbble.TeaserStats.init(newestShots);

    Dribbble.Thumbnails.cursor = '**********.330948';
}
else {
  if (typeof newShots === "undefined") {
    var newShots = [];
  }
  newShots = newShots.concat(newestShots);
}

</script>


</ol>

  <div id="boosted-shots-app"></div>

  

<div class="infinite shot-grid-width container-fluid infinite-login-actions">
      <a class="form-sub sign-up-to-continue" data-signup-trigger="true" data-href="/signup/new" data-context="homepage-load-more" href="https://dribbble.com/signup/new">Sign up to continue</a>
<a href="https://dribbble.com/session/new?return_to=%2Ftags%2Ffind_nearby_places">or sign in</a>

</div>

<div class="loading-more">
  <span class="processing">Loading more…</span>
</div>

<div class="back-to-top" style="">
  <a href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail#">
    <img width="50" height="50" class="lazyloading-hidden lazyloaded" data-src="https://cdn.dribbble.com/assets/icon-backtotop-d9d209c36a169637612a8fe4a1f15ab9e5763a20dbe5b7706df4e23aadf6052e.png" alt="Back to top" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/icon-backtotop-d9d209c36a169637612a8fe4a1f15ab9e5763a20dbe5b7706df4e23aadf6052e.png">
  </a>
</div>






</div>

<!-- the following is needed to pass the query to the pack tag, do not remove -->
<div class="js-tag-query display-none" data-query-term="find nearby places"></div>
<script src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/tags-page-096d90463b8f1bf0d39b.js"></script>




    </div>
  </div>
</div>

<hr>

<div id="footer" class="site-footer js-site-footer" role="contentinfo">
  <div class="container-large">
      <div class="footer-main-content">
        <div>
          <a href="https://dribbble.com/">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 96 24" role="img" aria-labelledby="adoke2dqk9fsrcogyf5jelvckyk1zxwj" class="icon dribbble-wordmark">
<title id="adoke2dqk9fsrcogyf5jelvckyk1zxwj">Dribbble: the community for graphic design</title>
<path fill-rule="evenodd" clip-rule="evenodd" d="M95.8512 18.5141C91.8395 25.3156 85.4862 23.342 83.5781 21.7968C82.7661 22.3478 81.342 23.5554 79.4433 23.4161C75.3996 23.1192 73.9514 17.3405 73.9514 17.3405C73.9804 17.3636 72.747 17.7662 72.0802 17.759C72.0716 19.4536 70.6067 23.5018 66.6533 23.4501C62.217 23.3922 61.3151 16.7828 61.3151 16.7828C61.3151 16.7828 60.8736 17.3959 59.195 18.0064C59.2795 16.7045 59.2177 23.2519 53.9006 23.3481C49.6971 23.4242 48.5623 16.6809 48.5623 16.6809C48.5623 16.6809 47.8331 17.4385 46.4199 17.7012C46.5097 16.378 46.3637 23.3703 41.0459 23.3481C37.4523 23.3331 36.2242 19.1941 36.3197 18.6197C36.4416 17.8875 35.1052 23.4511 31.6145 23.3644C30.1739 23.322 29.1 22.2791 28.4261 20.8885C27.524 21.9209 26.2142 23.3644 24.7448 23.3644C22.1075 23.3644 20.9446 21.1584 21.1416 14.8577C21.1583 14.1105 21.1044 13.8165 20.3616 13.7047C19.9157 13.6304 19.459 13.4895 18.976 13.4152C18.8211 13.932 17.5076 23.1962 12.9912 23.372C11.476 23.4311 10.6475 22.1186 9.96715 21.1443C8.92417 22.5241 7.54738 23.4161 5.58603 23.4161C2.26365 23.4161 0 20.7302 0 17.417C0 14.1038 2.26365 11.4182 5.58603 11.4182C6.17345 11.4182 6.33836 11.5024 6.87502 11.659C5.77137 1.61058 8.37774 0.0401515 10.6578 0.0401515C12.8467 0.0401515 16.5863 5.12064 11.3244 18.0074C12.4926 21.8512 15.0111 21.6338 16.2214 13.7212C16.4676 12.1128 15.809 9.9423 16.8335 9.63937C18.7061 9.08575 18.9048 10.7468 19.7828 11.0235C20.7112 11.316 21.2531 11.2875 22.1444 11.4736C23.6674 11.771 24.2618 12.5892 24.0761 14.4113C23.8533 16.7171 23.4636 20.0729 24.652 20.4818C25.5091 20.779 27.0739 19.0016 27.3485 18.0291C27.623 17.0566 27.6803 16.7237 27.7047 16.0105C27.7419 14.4859 27.7884 13.3684 28.0484 12.2154C28.1597 11.7693 28.2865 11.4739 28.7912 11.4537C29.2066 11.4431 29.9661 11.318 30.3005 11.5782C30.7461 11.9131 30.6905 12.2529 30.6393 13.1471C30.121 25.8966 34.11 19.5319 35.2994 14.4357C34.876 8.67313 35.1667 0.145675 38.7779 0.00265405C40.6559 -0.0717249 41.4861 1.43282 41.5775 2.55581C41.8357 5.72757 40.3888 10.9815 38.4859 14.5148C37.3984 21.7242 43.2411 23.1498 44.1754 17.3952C42.6467 16.6684 40.9947 13.7265 42.339 12.2928C43.0934 11.4882 46.2335 12.6441 46.2849 15.1651C47.8252 14.7531 48.0308 13.8835 48.0522 14.0276C47.6287 8.265 48.0214 0.145749 51.6328 0.00272768C53.5106 -0.0716513 54.3408 1.43289 54.4321 2.55589C54.6904 5.72764 53.2435 10.9816 51.3408 14.5149C50.253 21.7243 56.096 23.1498 57.0301 17.3953C55.8983 17.1769 53.5091 14.0478 54.8876 12.2929C55.6243 11.3551 58.7528 13.3053 59.1032 15.2486C60.5829 14.8298 60.7838 13.9878 60.805 14.1296C60.3815 8.36712 60.7742 0.247876 64.3855 0.104854C66.2634 0.0304754 67.0936 1.53502 67.1849 2.65802C67.4432 5.82977 65.9962 11.0838 64.0933 14.6171C63.0058 21.8266 68.8485 23.2519 69.7829 17.4973C68.2276 17.2383 66.0171 13.9344 67.7962 12.2442C68.507 11.5689 71.2229 13.3219 71.8586 15.3218C72.742 15.2878 73.2918 14.9833 73.4097 14.9525C71.9995 8.18754 73.0493 0.0705829 76.9342 0.00282686C79.0338 -0.0337594 81.0867 1.13799 80.1856 7.57394C79.3256 13.7146 76.234 16.2916 76.2411 16.331C76.4211 17.0667 78.0074 23.2233 82.0023 19.9749C81.7955 19.5066 81.5885 19.0282 81.4728 18.4486C80.8107 15.0729 82.1112 11.2599 85.6462 10.6436C87.6715 10.2906 89.5793 11.2766 89.881 13.4996C90.3773 17.1371 87.0927 19.7715 85.8437 20.3429C85.2843 20.0251 90.9148 23.6362 94.2563 16.3995C94.45 15.9863 94.6837 16.0213 94.9863 16.2343C95.2 16.3847 96.4175 17.5379 95.8512 18.5141ZM8.00277 16.5233C7.83274 16.0149 7.48381 14.8949 7.36044 14.4096C6.68091 13.8187 6.19588 13.7227 5.32365 13.7227C3.38526 13.7227 2.24437 15.5148 2.24437 17.4473C2.24437 19.3798 3.48729 21.1722 5.42567 21.1722C7.10552 21.1722 8.38402 20.03 8.77408 18.4132C8.50106 17.7829 8.23024 17.2036 8.00277 16.5233ZM10.6103 2.70004C9.24825 2.70004 8.78622 5.94913 8.87589 8.72092C8.95519 11.1715 9.63996 13.329 9.99519 14.2956C10.0854 14.4168 10.0686 14.338 10.1491 14.4665C12.514 9.28488 11.5331 2.70004 10.6103 2.70004ZM38.9724 2.80209C37.212 2.60021 37.2233 9.93334 37.4419 11.5782C38.3561 10.1157 39.9444 3.1959 38.9724 2.80209V2.80209ZM51.827 2.80209C50.0667 2.60021 50.078 9.93334 50.2966 11.5782C51.2108 10.1157 52.7991 3.1959 51.827 2.80209ZM64.5798 2.90412C62.8194 2.70223 62.8307 10.0354 63.0494 11.6804C63.9635 10.2177 65.5518 3.2979 64.5798 2.90412V2.90412ZM77.1284 2.37283C74.3857 2.9236 75.0244 12.0682 75.4409 13.672C78.6717 9.23475 78.7381 2.20615 77.1284 2.37283V2.37283ZM87.4073 13.8005C87.268 13.2175 86.5707 12.9058 86.0894 12.9826C84.7123 13.1707 83.3767 14.8858 83.8937 17.497C84.0087 18.0786 84.2967 18.6138 84.2921 18.5961C87.3741 16.5285 87.636 14.8991 87.4073 13.8005ZM29.3312 9.43526C28.9376 9.43534 28.5528 9.31869 28.2255 9.10007C27.8982 8.88145 27.6431 8.57067 27.4924 8.20705C27.3417 7.84344 27.3023 7.4433 27.379 7.05726C27.4558 6.67122 27.6453 6.31661 27.9236 6.03827C28.2019 5.75994 28.5565 5.57039 28.9425 5.49359C29.3285 5.41679 29.7287 5.45619 30.0923 5.60681C30.456 5.75744 30.7668 6.01252 30.9854 6.33979C31.2041 6.66706 31.3208 7.05183 31.3208 7.44542C31.3208 7.70672 31.2693 7.96546 31.1693 8.20686C31.0694 8.44827 30.9228 8.66763 30.7381 8.8524C30.5533 9.03718 30.334 9.18375 30.0926 9.28376C29.8512 9.38377 29.5925 9.43525 29.3312 9.43526V9.43526Z"></path>
</svg>

            <span class="accessibility-text">Back to home page</span>
          </a>
          <div class="tagline">
            Dribbble is the world’s leading community for creatives to share, grow, and get hired.
          </div>
          <div class="social-links-container">
            <a href="https://dribbble.com/dribbble">
              <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none" role="img" class="icon fill-current">
<rect width="32" height="32" fill="black" fill-opacity="0"></rect>
<path fill-rule="evenodd" clip-rule="evenodd" d="M16 0C7.16703 0 0 7.16703 0 16C0 24.833 7.16703 32 16 32C24.8156 32 32 24.833 32 16C32 7.16703 24.8156 0 16 0ZM26.5683 7.37527C28.4772 9.70065 29.6226 12.6681 29.6573 15.8785C29.2061 15.7918 24.6941 14.872 20.1475 15.4447C20.0434 15.2191 19.9566 14.9761 19.8525 14.7332C19.5748 14.0738 19.2625 13.397 18.9501 12.7549C23.9826 10.7072 26.2733 7.75705 26.5683 7.37527ZM16 2.36009C19.4707 2.36009 22.6464 3.66161 25.0586 5.7961C24.8156 6.14317 22.7505 8.90239 17.8915 10.7245C15.6529 6.61171 13.1714 3.24512 12.7896 2.72451C13.8134 2.48156 14.8894 2.36009 16 2.36009ZM10.1866 3.64425C10.551 4.13015 12.9805 7.5141 15.2538 11.5401C8.86768 13.2408 3.22777 13.2061 2.62039 13.2061C3.50542 8.9718 6.36876 5.44902 10.1866 3.64425ZM2.32538 16.0174C2.32538 15.8785 2.32538 15.7397 2.32538 15.6009C2.9154 15.6182 9.54447 15.705 16.3644 13.6573C16.7636 14.4208 17.128 15.2017 17.4751 15.9826C17.3015 16.0347 17.1106 16.0868 16.9371 16.1388C9.89154 18.4121 6.14317 24.6247 5.8308 25.1453C3.6616 22.7332 2.32538 19.5228 2.32538 16.0174ZM16 29.6746C12.8416 29.6746 9.92625 28.5987 7.61822 26.7939C7.86117 26.2907 10.6377 20.9458 18.3427 18.256C18.3774 18.2386 18.3948 18.2386 18.4295 18.2213C20.3557 23.2017 21.1367 27.3839 21.3449 28.5813C19.6963 29.2928 17.8915 29.6746 16 29.6746ZM23.6182 27.3319C23.4794 26.4989 22.7505 22.5076 20.9631 17.5965C25.2495 16.9197 28.9978 18.0304 29.4664 18.1866C28.8764 21.987 26.6898 25.2668 23.6182 27.3319Z"></path>
</svg>

              <span class="accessibility-text">Back to home page</span>
            </a>
            <a href="http://twitter.com/dribbble">
              <svg xmlns="http://www.w3.org/2000/svg" aria-labelledby="a5akxgzvwszgwwze8tejtzmzdtvgls3m" role="img" viewBox="0 0 24 24" class="icon fill-current"><title id="a5akxgzvwszgwwze8tejtzmzdtvgls3m">Twitter icon</title><path d="M23.954 4.569c-.885.389-1.83.654-2.825.775 1.014-.611 1.794-1.574 2.163-2.723-.951.555-2.005.959-3.127 1.184-.896-.959-2.173-1.559-3.591-1.559-2.717 0-4.92 2.203-4.92 4.917 0 .39.045.765.127 1.124C7.691 8.094 4.066 6.13 1.64 3.161c-.427.722-.666 1.561-.666 2.475 0 1.71.87 3.213 2.188 4.096-.807-.026-1.566-.248-2.228-.616v.061c0 2.385 1.693 4.374 3.946 4.827-.413.111-.849.171-1.296.171-.314 0-.615-.03-.916-.086.631 1.953 2.445 3.377 4.604 3.417-1.68 1.319-3.809 2.105-6.102 2.105-.39 0-.779-.023-1.17-.067 2.189 1.394 4.768 2.209 7.557 2.209 9.054 0 13.999-7.496 13.999-13.986 0-.209 0-.42-.015-.63.961-.689 1.8-1.56 2.46-2.548l-.047-.02z"></path></svg>

              <span class="accessibility-text">Twitter</span>
            </a>
            <a href="http://facebook.com/dribbble">
            <svg xmlns="http://www.w3.org/2000/svg" aria-labelledby="abljzpv1uk0f75io6wsh6avjwffq2szf" role="img" viewBox="0 0 24 24" class="icon fill-current"><title id="abljzpv1uk0f75io6wsh6avjwffq2szf">Facebook icon</title><path d="M22.676 0H1.324C.593 0 0 .593 0 1.324v21.352C0 23.408.593 24 1.324 24h11.494v-9.294H9.689v-3.621h3.129V8.41c0-3.099 1.894-4.785 4.659-4.785 1.325 0 2.464.097 2.796.141v3.24h-1.921c-1.5 0-1.792.721-1.792 1.771v2.311h3.584l-.465 3.63H16.56V24h6.115c.733 0 1.325-.592 1.325-1.324V1.324C24 .593 23.408 0 22.676 0"></path></svg>

            <span class="accessibility-text">Facebook</span>
            </a>
            <a href="http://instagram.com/dribbble">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill-rule="evenodd" clip-rule="evenodd" stroke-linejoin="round" stroke-miterlimit="1.414" role="img" class="icon fill-current"><path d="M8 0C5.827 0 5.555.01 4.702.048 3.85.088 3.27.222 2.76.42c-.526.204-.973.478-1.417.923-.445.444-.72.89-.923 1.417-.198.51-.333 1.09-.372 1.942C.008 5.555 0 5.827 0 8s.01 2.445.048 3.298c.04.852.174 1.433.372 1.942.204.526.478.973.923 1.417.444.445.89.72 1.417.923.51.198 1.09.333 1.942.372.853.04 1.125.048 3.298.048s2.445-.01 3.298-.048c.852-.04 1.433-.174 1.942-.372.526-.204.973-.478 1.417-.923.445-.444.72-.89.923-1.417.198-.51.333-1.09.372-1.942.04-.853.048-1.125.048-3.298s-.01-2.445-.048-3.298c-.04-.852-.174-1.433-.372-1.942-.204-.526-.478-.973-.923-1.417-.444-.445-.89-.72-1.417-.923-.51-.198-1.09-.333-1.942-.372C10.445.008 10.173 0 8 0zm0 1.44c2.136 0 2.39.01 ************.036 1.203.166 1.485.276.374.145.64.318.92.598.28.28.453.546.598.92.11.282.24.705.276 1.485.038.844.047 1.097.047 3.233s-.01 2.39-.048 3.233c-.036.78-.166 1.203-.276 1.485-.145.374-.318.64-.598.92-.28.28-.546.453-.92.598-.282.11-.705.24-1.485.276-.844.038-1.097.047-3.233.047s-2.39-.01-3.233-.048c-.78-.036-1.203-.166-1.485-.276-.374-.145-.64-.318-.92-.598-.28-.28-.453-.546-.598-.92-.11-.282-.24-.705-.276-1.485C1.45 10.39 1.44 10.136 1.44 8s.01-2.39.048-3.233c.036-.78.166-1.203.276-1.485.145-.374.318-.64.598-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276C5.61 1.45 5.864 1.44 8 1.44zm0 2.452c-2.27 0-4.108 1.84-4.108 4.108 0 2.27 1.84 4.108 4.108 4.108 2.27 0 4.108-1.84 4.108-4.108 0-2.27-1.84-4.108-4.108-4.108zm0 6.775c-1.473 0-2.667-1.194-2.667-2.667 0-1.473 1.194-2.667 2.667-2.667 1.473 0 2.667 1.194 2.667 2.667 0 1.473-1.194 2.667-2.667 2.667zm5.23-6.937c0 .53-.43.96-.96.96s-.96-.43-.96-.96.43-.96.96-.96.96.43.96.96z"></path></svg>

            <span class="accessibility-text">Instagram</span>
            </a>
            <a href="http://www.pinterest.com/Pinner13145726/">
            <svg xmlns="http://www.w3.org/2000/svg" aria-labelledby="ah7gg207tph7o8s7lcoy5wn9q0h65kix" role="img" viewBox="0 0 24 24" class="icon fill-current"><title id="ah7gg207tph7o8s7lcoy5wn9q0h65kix">Pinterest icon</title><path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.162-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.401.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.354-.629-2.758-1.379l-.749 2.848c-.269 1.045-1.004 2.352-1.498 3.146 1.123.345 2.306.535 3.55.535 6.607 0 11.985-5.365 11.985-11.987C23.97 5.39 18.592.026 11.985.026L12.017 0z"></path></svg>

            <span class="accessibility-text">Pinterest</span>
            </a>
          </div>
        </div>
        <div class="links-container">
            <div class="links-column">
                <div class="link-heading">For designers</div>
                    <a class="link" href="https://dribbble.com/pro">Go Pro!</a>
                    <a class="link" href="https://dribbble.com/shots">Explore design work</a>
                    <a class="link" href="https://dribbble.com/stories">Design blog</a>
                    <a class="link" href="https://dribbble.com/overtime">Overtime podcast</a>
                    <a class="link" href="https://dribbble.com/meetups">Dribbble meetups</a>
                    <a class="link" href="https://dribbble.com/shots?list=playoffs">Playoffs</a>
                    <a class="link" href="https://dribbble.com/guidelines">Code of conduct</a>
            </div>
            <div class="links-column">
                <div class="link-heading">Hire designers</div>
                    <a class="link" href="https://dribbble.com/jobs/new">Post a job opening</a>
                    <a class="link" href="https://dribbble.com/freelance-jobs">Post a freelance project</a>
                    <a class="link" href="https://dribbble.com/designers">Search for designers</a>
                <div class="link-heading">Brands</div>
                    <a class="link" href="https://dribbble.com/advertise">Advertise with us</a>
            </div>
            <div class="links-column">
                <div class="link-heading">Company</div>
                    <a class="link" href="https://dribbble.com/about">About</a>
                    <a class="link" href="https://dribbble.com/careers">Careers</a>
                    <a class="link" href="https://dribbble.com/contact">Support</a>
                    <a class="link" href="https://dribbble.com/branding">Media kit</a>
                    <a class="link" href="https://dribbble.com/testimonials">Testimonials</a>
                    <a class="link" href="https://developer.dribbble.com/">API</a>
                    <a class="link" href="https://dribbble.com/terms">Terms of service</a>
                    <a class="link" href="https://dribbble.com/privacy">Privacy policy</a>
            </div>
            <div class="links-column">
                <div class="link-heading">Directories</div>
                    <a class="link" href="https://dribbble.com/directories/jobs">Design jobs</a>
                    <a class="link" href="https://dribbble.com/directories/designers">Designers for hire</a>
                    <a class="link" href="https://dribbble.com/directories/freelance-designers">Freelance designers for hire</a>
                    <a class="link" href="https://dribbble.com/tags">Tags</a>
                    <a class="link" href="https://dribbble.com/places">Places</a>
                <div class="link-heading">Design assets</div>
                    <a target="_blank" rel="noopener" class="link" href="https://creativemarket.com/?utm_source=dribbble&amp;utm_medium=referral&amp;utm_campaign=footer">Creative Market</a>
                    <a target="_blank" rel="noopener" class="link" href="https://www.fontspring.com/?utm_source=dribbble&amp;utm_medium=referral&amp;utm_campaign=footer">Fontspring</a>
                    <a target="_blank" rel="noopener" class="link" href="https://www.fontsquirrel.com/?utm_source=dribbble&amp;utm_medium=referral&amp;utm_campaign=footer">Font Squirrel</a>
            </div>
            <div class="links-column">
                <div class="link-heading">Design Resources</div>
                    <a rel="noopener" class="link" href="https://dribbble.com/resources/freelance">Freelancing</a>
                    <a rel="noopener" class="link" href="https://dribbble.com/resources/design-hiring">Design Hiring</a>
                    <a rel="noopener" class="link" href="https://dribbble.com/resources/design-portfolio">Design Portfolio</a>
                    <a rel="noopener" class="link" href="https://dribbble.com/resources/education">Design Education</a>
                    <a rel="noopener" class="link" href="https://dribbble.com/resources/creative-process">Creative Process</a>
                    <a rel="noopener" class="link" href="https://www.industry-trends.dribbble.com/">Design Industry Trends</a>
            </div>
        </div>
      </div>
    <div class="footer-lower-content">
      <div>© 2022 Dribbble. All rights reserved.</div>
      <div class="total-shots-container">
        <div>
          <span class="shots-count">17,609,919</span>
          shots dribbbled
        </div>
        <img class="dribbble-ball lazyload" data-src="https://cdn.dribbble.com/assets/dribbble-ball-icon-4e54c54abecf8efe027abe6f8bc7794553b8abef3bdb49cd15797067cf80ca53.svg" width="24px" height="24px" alt="Dribbble ball icon">
      </div>
    </div>
  </div>
</div>





  

    <div class="shot-overlay js-shot-overlay lazyloading-hidden-container lazyloaded overlay-visible" tabindex="-1" data-include="css:https://cdn.dribbble.com/assets/components/media-overlay-modal-e335ee62734f01cca5c77637ef3fea3a0363b61dce89f701bbadff6f752a4419.css" data-currentinclude="">
  <div class="overlay-content js-overlay-content">



  <script>
  var config = Dribbble.JsConfig || {}
  // the assign is so this partial can be called in other partials, e.g. modals
  // without overriding parent instantiations of JsConfig
  Dribbble.JsConfig = Object.assign(config, {
      user: {"isLoggedIn":false,"reCaptchaSiteKey":"6LdmBTIUAAAAAM4NIokaWu8p3BBuYEw3CxuDdyg_","canPurchasePro":true},
      hiringProfile: {},
      features: {"applePay":true,"braintreeHiring":false,"braintreePaymentButtons":true,"proSale2021":true,"courses50DiscountLink":true,"caseStudies":false,"proDiscountToasty":false,"caseStudiesAlpha":false,"marketplaceUploads":false,"directBillingCheckout":false,"clientSidePaymentMethods":false},
      isRobot: null,
      remoteIp: "**************",
      isProduction: true,
      permissions: {"userPermissions":["canSeeThirdPartyPaymentMethods"],"marketplacePermissions":[]},
      screenshot_boost: {"buttonText":[{"label":"Buy Now","value":"buy-now"},{"label":"Download","value":"download"},{"label":"Learn More","value":"learn-more"},{"label":"Shop Now","value":"shop-now"},{"label":"Apply Now","value":"apply-now"},{"label":"Try Now","value":"try-now"},{"label":"Get Offer","value":"get-offer"},{"label":"Contact Us","value":"contact-us"}],"tiers":{"lowTier":{"daysToServe":7,"range":{"lowerLimit":0,"upperLimit":10000}},"midTier":{"daysToServe":14,"range":{"lowerLimit":10001,"upperLimit":100000}},"highTier":{"daysToServe":28,"range":{"lowerLimit":100001,"upperLimit":null}}},"pricePerImpression":"0.006","purchase":{"stripePublicKey":"pk_live_9EfFSEE6iTCgHghKqBqnixxR","savedCreditCardInformation":null},"discount":null,"minimumImpressions":2000,"targetPlacements":{"following":"Following Feed","popular":"Popular Feed","search":"Search Feed","goods":"Goods Feed","recent":"New \u0026 Noteworthy Feed","shot_modal":"Shot Modal","similar_work":"Similar Work","tags":"Tag Feed","popular_first":"Popular Feed First"},"priorities":["self_serve","sales","remnant","sales_priority"]},
      braintree: {"savedPaymentMethods":[],"customerId":null},
      shotData: {"attachmentCount":0,"boost":{"id":null,"canBoostShot":false,"hasBoostedShots":false,"status":null,"statusReason":null,"statusReasonSentence":null,"clicksCount":null,"impressionsCount":null,"totalImpressions":null},"canCurrentUserComment":false,"canCurrentUserRebound":false,"commentsCount":33,"commentsDisabled":false,"contentBlocks":null,"featureShotUrl":"https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png","hasDownloads":true,"hasGood":true,"height":1200,"isAnimated":false,"isAnimatedGif":false,"isVideo":false,"isFromModal":true,"isLiked":false,"isMultiShot":true,"isOwnedByCurrentUser":false,"isPublished":true,"isPublishedByTeam":false,"isSaved":false,"likesCount":310,"postedOn":"Dec 8, 2020","reboundPath":"/uploads/new?rebound=14728277-Holeswing-Golf-Courses-List-and-Detail","savesCount":85,"shareUtms":{"source":"Shot","campaign":"aveef","content":"Holeswing - Golf Courses List and Detail"},"shotHasRebounds":false,"shotId":14728277,"shotMediaPreview":{"mediaType":null,"shotImageUrl":"https://cdn.dribbble.com/users/124243/screenshots/14728277/hs-1_1x.png","shotGifUrl":"https://cdn.dribbble.com/users/124243/screenshots/14728277/hs-1.png","shotVideoUrl":null},"shotUser":{"id":124243,"forHire":true,"username":"aveef","name":"⭐️ Afif Bimantara","isTeam":false,"avatarUrl":"https://cdn.dribbble.com/users/124243/avatars/normal/8d25b91a17d31c2ab49566f80e6c2f72.jpg?1562051899","url":"/aveef","team":{"avatarUrl":"https://cdn.dribbble.com/users/2547736/avatars/normal/0c4aff997b5469101760bff0f3c5c535.png?1536649020","id":2547736,"name":"⚡️Agensip UI UX Agency","url":"/agensip"}},"tags":["ios","mobile app","mobile ui","uxdesign","uidesign","country club","places","nearby","round","scoring","golf","golf club","mobilegolf","sportsapp","golfrange","golfcourse","golfapp","mobile","clean","app"],"viewsCount":52206,"width":1600,"title":"Holeswing - Golf Courses List and Detail","hasProFeatures":true,"shouldDisplayAds":true},
  })
</script>

  <script>
    if (Dribbble.ShotOverlay) {
      Dribbble.ShotOverlay.setRestricted(true);
    }
  </script>
  <script>
  Dribbble.Analytics.googleAnalyticsKey = "UA-24041469-1"
  if (window.location.pathname == "/") {
    Dribbble.Analytics.namedRoot = "/shots";
    Dribbble.Analytics.logDribbbleGAPageView("/")
  } else {
    Dribbble.Analytics.logDribbbleGAPageView("/shots/14728277-Holeswing-Golf-Courses-List-and-Detail?showSimilarShots=true\u0026_=1646031216669")
  }

  Dribbble.Itly.pageViewed({ controller: "shots", action: "show"})
    gtag('event', 'page_view', { "name": "124243_aveef", "send_to": "UA-120360070-1" });
    gtag('event', 'page_view', { "name": "2547736_agensip", "send_to": "UA-120360070-3" });
</script>









<div class="shot-container js-shot-container lazyload-hidden-element ">
  <div class="shot-content js-media-content ">
    <div class="shot-content-container">

      
<div class="shot-header js-shot-header">
  <div class="shot-header-content">
    <div class="shot-user-container">
      <div class="shot-user-avatar shot-user-avatar-large-screens">
        <a class="hoverable url" rel="contact" title="⭐️ Afif Bimantara" href="https://dribbble.com/aveef"><picture>
  <source srcset="https://cdn.dribbble.com/users/124243/avatars/normal/8d25b91a17d31c2ab49566f80e6c2f72.jpg?1562051899" media="(-webkit-min-device-pixel-ratio: 1.5), (min--moz-device-pixel-ratio: 1.5), (-o-min-device-pixel-ratio: 3/2), (min-device-pixel-ratio: 1.5), (min-resolution: 1.5dppx)">
  <source srcset="https://cdn.dribbble.com/users/124243/avatars/small/8d25b91a17d31c2ab49566f80e6c2f72.jpg?1562051899">
  <img class="photo" alt="⭐️ Afif Bimantara" width="48" height="48" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/8d25b91a17d31c2ab49566f80e6c2f72.jpg">
</picture>
</a>
          <div class="team-avatar"><a class="hoverable url" rel="contact" title="⚡️Agensip UI UX Agency" href="https://dribbble.com/agensip"><picture>
  <source srcset="https://cdn.dribbble.com/users/2547736/avatars/normal/0c4aff997b5469101760bff0f3c5c535.png?1536649020" media="(-webkit-min-device-pixel-ratio: 1.5), (min--moz-device-pixel-ratio: 1.5), (-o-min-device-pixel-ratio: 3/2), (min-device-pixel-ratio: 1.5), (min-resolution: 1.5dppx)">
  <source srcset="https://cdn.dribbble.com/users/2547736/avatars/small/0c4aff997b5469101760bff0f3c5c535.png?1536649020">
  <img class="photo" alt="⚡️Agensip UI UX Agency" width="20" height="20" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/0c4aff997b5469101760bff0f3c5c535(1).png">
</picture>
</a></div>
      </div>
      <div class="shot-header-details">
        <h1 class="shot-header-title">Holeswing - Golf Courses List and Detail</h1>
        <div class="display-flex align-center">
          <div class="shot-user-avatar shot-user-avatar-small-screens">
            <a class="hoverable url" rel="contact" title="⭐️ Afif Bimantara" href="https://dribbble.com/aveef"><picture>
  <source srcset="https://cdn.dribbble.com/users/124243/avatars/normal/8d25b91a17d31c2ab49566f80e6c2f72.jpg?1562051899" media="(-webkit-min-device-pixel-ratio: 1.5), (min--moz-device-pixel-ratio: 1.5), (-o-min-device-pixel-ratio: 3/2), (min-device-pixel-ratio: 1.5), (min-resolution: 1.5dppx)">
  <source srcset="https://cdn.dribbble.com/users/124243/avatars/small/8d25b91a17d31c2ab49566f80e6c2f72.jpg?1562051899">
  <img class="photo" alt="⭐️ Afif Bimantara" width="48" height="48" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/8d25b91a17d31c2ab49566f80e6c2f72.jpg">
</picture>
</a>
              <div class="team-avatar"><a class="hoverable url" rel="contact" title="⚡️Agensip UI UX Agency" href="https://dribbble.com/agensip"><picture>
  <source srcset="https://cdn.dribbble.com/users/2547736/avatars/normal/0c4aff997b5469101760bff0f3c5c535.png?1536649020" media="(-webkit-min-device-pixel-ratio: 1.5), (min--moz-device-pixel-ratio: 1.5), (-o-min-device-pixel-ratio: 3/2), (min-device-pixel-ratio: 1.5), (min-resolution: 1.5dppx)">
  <source srcset="https://cdn.dribbble.com/users/2547736/avatars/small/0c4aff997b5469101760bff0f3c5c535.png?1536649020">
  <img class="photo" alt="⚡️Agensip UI UX Agency" width="20" height="20" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/0c4aff997b5469101760bff0f3c5c535(1).png">
</picture>
</a></div>
          </div>
          <div class="shot-header-author-and-actions">
            <div class="shot-user-details">
              <a class="hoverable shot-user-link" rel="contact" href="https://dribbble.com/aveef">⭐️ Afif Bimantara</a>
                for
                <a class="hoverable shot-user-link" rel="contact" href="https://dribbble.com/agensip">⚡️Agensip UI UX Agency</a>
            </div>
            <div class="shot-header-action-links js-shot-header-action-links ">
                <span class="shot-user-divider-dot large-screens-only">•</span>
                <div class="follow-prompt follow-prompt-text follow-link" data-ga-name="aveef" data-ga-key="UA-120360070-1">

      <a class="form-btn follow shot-follow sentence-btn " rel="tipsy" data-signup-trigger="" data-context="follow-user" href="https://dribbble.com/aveef/followers" original-title="Follow ⭐️ Afif Bimantara">Follow</a>
      <a class="form-btn tipsy-mobile-disabled unfollow stripped-btn" href="https://dribbble.com/aveef/followers/aveef">
        <span>Following</span>
</a></div>
                <span class="shot-user-divider-dot">•</span>
                



<script>
var trigger = 'a.contact';

if (!Dribbble.MessageModal ) {
  Dribbble.MessageModal = {
    init: function() {
      this.eventElements = [];
      this.newContent = true;
      Dribbble.EventBus.$on('searchResult:updated', this.bindEvents.bind(this));
      this.bindEvents();
    },

    bindEvents: function() {
      this.unbindEvents();

      this.setClickEvent();

      document.querySelectorAll(trigger).forEach(function(el) {
        this.eventElements.push(el);
        el.addEventListener('click', this.onClickTrigger, true);
      }.bind(this));
    },

    unbindEvents: function() {
      // remove previous event listeners
      if (this.eventElements.length) {
        this.eventElements.forEach(function(el) {
          el.removeEventListener('click', this.onClickTrigger, true);
        });
      }

      this.eventElements = [];
    },

    extractParams: function(e) {
      return Dribbble.Url.extractParams($(e.target).closest('a').attr('href'));
    },

    setClickEvent: function() {
      if (!Dribbble.MessageModal.newMessage) {
        Dribbble.MessageModal.newMessage = new Dribbble.MessageModalNew();
        Dribbble.MessageModal.newMessage.init({ maxLength: 8000 });
      }

        var designerSearchData = Dribbble.JsConfig.designerSearchData;

        if (designerSearchData
          && designerSearchData.activeSearch.workType
          && !designerSearchData[designerSearchData.activeSearch.workType].enabled
        ) {
          this.onClickTrigger = this.triggerUpSell;
        } else {
          if (this.newContent) {
            Dribbble.MessageModal.newMessage.messageAttemptOverlay = this.createMessageModal();
          }
          this.onClickTrigger = this.showMessageModal;
        }
    },

    triggerUpSell: function(e) {
      e.preventDefault();
      Dribbble.EventBus.$emit('designer-search-upsell', {
        isOpen: true,
        referrer: 'message',
      });
    },

    showMessageModal: function(e) {
      e.preventDefault();
      Dribbble.MessageModal.newMessage.messageAttemptOverlay.currentTarget = $(e.target).closest('a');
      Dribbble.MessageModal.newMessage.messageAttemptOverlay.show();
    },

    upsellRedirect: function(e) {
      e.preventDefault();
      window.location = "https://dribbble.com/hiring?message=true";
    },

    extractParams: function(e) {
      return Dribbble.Url.extractParams($(e.target).closest('a').attr('href'));
    },

    unverifiedRedirect: function(e) {
      e.preventDefault();
      window.location = "/account/unverified?" + this.extractParams(e);
    },

    messagesDisabledAlert: function(e) {
      e.preventDefault();
      Dribbble.Notify.alert("Messaging is temporarily disabled while we perform some maintenance. Thanks for your patience, please try again shortly.");
    },

    createMessageModal: function() {
      var contactOverlayId = "shot-header-contact-overlay";

      return new Dribbble.Overlay.RemoteForm({
        $el: $("#" + contactOverlayId),
        onShow: function() {
          document.querySelector(".message-overlay").scrollIntoView();
        },
        onSubmit: Dribbble.MessageModal.newMessage.onSubmit.bind(Dribbble.MessageModal.newMessage),
        containerToUpdateOnFailure: '#' + contactOverlayId + ' .display',
        remoteContent: {
          el: '#' + contactOverlayId + ' .display',
          url: function() {

            return this.currentTarget.attr('href');
          },
          onComplete: Dribbble.MessageModal.newMessage.overlayLoaded.bind(Dribbble.MessageModal.newMessage)
        },
      });
    }
  };

  Dribbble.MessageModal.init();
  Dribbble.EventBus.$on('messageTriggerAdded', () => {
    Dribbble.MessageModal.bindEvents();
  });
} else {
  Dribbble.MessageModal.bindEvents();
}
</script>




<div class="profile-message">

  <a class="contact message-btn " rel="tipsy" data-message-recipient="2547736" data-button-location="profile" href="https://dribbble.com/messages/new?recipient_id=agensip" original-title="Send a message about a work opportunity">
      <span>Hire Us</span>
</a></div>

            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="shot-header-actions-container">
      <ul class="actions shot-actions">

          <div class="shot-action-item shot-action-item-desktop-only">
            <li data-bucket-container="true">
  

  <a class="bucket-shot form-btn" title="Save shot" data-signup-trigger="true" data-href="/signup/new" data-context="bucket-shot" href="https://dribbble.com/signup/new">
      Save
</a>
</li>

          </div>

          <div class="shot-action-item shot-action-item-desktop-only">
            

  <div class="like-action-14728277 modal-true  shot-action">
    <script>
      if (typeof(Dribbble) != "undefined" && typeof(Dribbble.Shots) != "undefined") {
        Dribbble.Shots.update(14728277, {
          likes_count: 310,
          liked: false
      });
      }
    </script>
      <a class="form-btn tipsy-mobile-disabled stats-action like-shot" rel="no-follow" title="Like this shot" data-signup-trigger="true" data-href="/signup/new" data-context="like-shot" href="https://dribbble.com/signup/new">
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m18.199 2.04c-2.606-.284-4.262.961-6.199 3.008-2.045-2.047-3.593-3.292-6.199-3.008-3.544.388-6.321 4.43-5.718 7.96.966 5.659 5.944 9 11.917 12 5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"></path></svg>

          Like
</a>  </div>

          </div>
      </ul>
      <!---->
    </div>
  </div>
</div>


      

        <section class="shot-media-section  " data-screenshot_id="14728277">
          <div class="js-media-container shot-media-container has-gallery " data-shot-id="14728277" data-type="modal">
            

              <div class="shot-image-color-palette-container">
                <div class="position-relative">
  <div class="js-media-gallery media-gallery initial" style="overflow: hidden; cursor: inherit;"><div class="dribbble-carousel-wrapper display-flex" style="width: 1536px; transition: transform 200ms ease-out 0s; transform: translate3d(0px, 0px, 0px);"><div class="dribbble-carousel-slide" style="width: 50%;"><div class="media-slide">
          <a class="full-screen-page" href="https://dribbble.com/shots/14728277/attachments/6429532?mode=media">
            
<div class="js-media-shot media-shot" data-media-id="14728277">
  <div class="media-content">
      <img alt="Holeswing - Golf Courses List and Detail" width="732" height="558" data-id="14728277" data-optimize-for-bots="true" data-animated-url="https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png" skip_resize="false" srcset="https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w" sizes="(max-width: 919px) 100vw, (min-width: 920px) and (max-width: 1200px) 74vw, 1172px" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/8937cca595068ed1cadc1ad15577470d.png">

  </div>
</div>

</a>      </div></div><div class="dribbble-carousel-slide" style="width: 50%;"><div class="media-slide">
          <a class="full-screen-page" href="https://dribbble.com/shots/14728277/attachments/6429533?mode=media">
            
<div class="js-media-shot media-shot" data-media-id="14728277">
  <div class="media-content">
      <img alt="Holeswing - Golf Courses List and Detail" width="732" height="558" data-id="14728277" data-optimize-for-bots="true" data-srcset="https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w" data-src="https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" data-animated-url="https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png" skip_resize="false" class="lazyautosizes lazyloaded" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/4bdb53db0c94fbf83a0998e31f0b1231.png" sizes="768px" srcset="https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=400x300&amp;vertical=top 400w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=450x338&amp;vertical=top 450w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=640x480&amp;vertical=top 640w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=700x525&amp;vertical=top 700w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=800x600&amp;vertical=top 800w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=840x630&amp;vertical=top 840w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=1000x750&amp;vertical=top 1000w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=1200x900&amp;vertical=top 1200w">

  </div>
</div>

</a>      </div></div></div></div>

  <div class="js-media-gallery-navigation-target media-gallery-navigation-target previous" style="cursor: default;">
    <button class="js-media-gallery-navigation media-gallery-navigation previous d-none">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" role="img" class="icon ">
<rect width="13" height="15" transform="matrix(-1 0 0 1 19 5)" fill="black" fill-opacity="0.2"></rect>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 23C5.92487 23 1 18.0751 1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23ZM8.49975 11.9996L12.2659 15.7071C12.6614 16.0971 13.3064 16.0971 13.7024 15.7076C13.8944 15.5186 13.9999 15.2676 13.9999 15.0006C13.9999 14.7336 13.8944 14.4826 13.7024 14.2936L11.3728 12.0001L13.7024 9.70662C14.0984 9.31662 14.0984 8.68162 13.7024 8.29212C13.3064 7.90263 12.6619 7.90263 12.2659 8.29212L8.49975 11.9996Z" fill="white"></path>
<circle r="11.5" transform="matrix(-1 0 0 1 12 12)" stroke="black" stroke-opacity="0.1"></circle>
</svg>

    </button>
  </div>

  <div class="js-media-gallery-navigation-target media-gallery-navigation-target next" style="cursor: pointer;">
    <button class="js-media-gallery-navigation media-gallery-navigation next">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" role="img" class="icon ">
<rect x="5" y="5" width="13" height="15" fill="black" fill-opacity="0.2"></rect>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 23C18.0751 23 23 18.0751 23 12C23 5.92487 18.0751 1 12 1C5.92487 1 1 5.92487 1 12C1 18.0751 5.92487 23 12 23ZM15.5003 11.9996L11.7341 15.7071C11.3386 16.0971 10.6936 16.0971 10.2976 15.7076C10.1056 15.5186 10.0001 15.2676 10.0001 15.0006C10.0001 14.7336 10.1056 14.4826 10.2976 14.2936L12.6272 12.0001L10.2976 9.70662C9.90159 9.31662 9.90159 8.68162 10.2976 8.29212C10.6936 7.90263 11.3381 7.90263 11.7341 8.29212L15.5003 11.9996Z" fill="white"></path>
<circle cx="12" cy="12" r="11.5" stroke="black" stroke-opacity="0.1"></circle>
</svg>

    </button>
  </div>
</div>

                  <div class="color-palette-container ">
    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none" role="img" class="icon color-palette-icon">
<path fill-rule="evenodd" clip-rule="evenodd" d="M22 11C22 17.05 17.05 22 11 22C4.95 22 0 17.05 0 11C0 4.95 4.95 0 11 0C17.05 0 22 4.95 22 11ZM11 19C15.411 19 19 15.411 19 11C19 6.589 15.411 3 11 3V19Z" fill="#3D3D4E"></path>
</svg>


    <div class="color-chips-container">
      
<ul class="color-chips group">
      <li class="color">
        <a style="background-color: #C4E5B2" title="#C4E5B2" href="https://dribbble.com/shots?color=C4E5B2">#C4E5B2</a>
      </li>
      <li class="color">
        <a style="background-color: #E8F6E7" title="#E8F6E7" href="https://dribbble.com/shots?color=E8F6E7">#E8F6E7</a>
      </li>
      <li class="color">
        <a style="background-color: #0C1209" title="#0C1209" href="https://dribbble.com/shots?color=0C1209">#0C1209</a>
      </li>
      <li class="color">
        <a style="background-color: #B3D9A1" title="#B3D9A1" href="https://dribbble.com/shots?color=B3D9A1">#B3D9A1</a>
      </li>
      <li class="color">
        <a style="background-color: #415445" title="#415445" href="https://dribbble.com/shots?color=415445">#415445</a>
      </li>
      <li class="color">
        <a style="background-color: #9BA3A0" title="#9BA3A0" href="https://dribbble.com/shots?color=9BA3A0">#9BA3A0</a>
      </li>
      <li class="color">
        <a style="background-color: #519585" title="#519585" href="https://dribbble.com/shots?color=519585">#519585</a>
      </li>
</ul>


      <a class="download-color-palette-button" rel="tipsy nofollow" href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail/colors.aco" original-title="Download palette (.aco)">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" role="img" class="icon fill-current">
<path d="M12 17L18.439 10.561C19.384 9.616 18.715 8 17.379 8H14V2C14 0.896 13.105 0 12 0C10.895 0 10 0.896 10 2V8H6.621C5.285 8 4.615 9.616 5.56 10.561L12 17ZM20 20H4C2.9 20 2 20.9 2 22C2 23.1 2.9 24 4 24H20C21.1 24 22 23.1 22 22C22 20.9 21.1 20 20 20Z"></path>
</svg>

        <span class="accessibility-text">Download color palette</span>
</a>    </div>
  </div>

              </div>
              <div class="multishot-goods-container multishot-goods-container-without-good-card">
                <div class="multishot-goods-content">
                  <div class="media-gallery-thumbnails-wrapper">
  <ol class="js-media-gallery-thumbnails media-gallery-thumbnails">
      <li class="media-gallery-thumbnail still">
        <div class="js-media-shot-small media-shot-small active" data-index="0">
          <img alt="hs-1.png" width="76" height="57" data-id="6429532" data-srcset="https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=80x60&amp;vertical=top 80w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=160x120&amp;vertical=top 160w" data-src="https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class="lazyautosizes lazyloaded" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/8937cca595068ed1cadc1ad15577470d.png" sizes="76px" srcset="https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=80x60&amp;vertical=top 80w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/8937cca595068ed1cadc1ad15577470d.png?compress=1&amp;resize=160x120&amp;vertical=top 160w">
        </div>
      </li>
      <li class="media-gallery-thumbnail still">
        <div class="js-media-shot-small media-shot-small" data-index="1">
          <img alt="hs-2.png" width="76" height="57" data-id="6429533" data-srcset="https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=80x60&amp;vertical=top 80w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=160x120&amp;vertical=top 160w" data-src="https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class="lazyautosizes lazyloaded" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/4bdb53db0c94fbf83a0998e31f0b1231.png" sizes="76px" srcset="https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=80x60&amp;vertical=top 80w, https://cdn.dribbble.com/users/124243/screenshots/14728277/media/4bdb53db0c94fbf83a0998e31f0b1231.png?compress=1&amp;resize=160x120&amp;vertical=top 160w">
        </div>
      </li>
  </ol>
  <span class="scroll-indicator scroll-backward"></span>
  <span class="scroll-indicator scroll-forward"></span>
</div>

                </div>
              </div>
          </div>
        </section>


        <div class="shot-description-container ">
              <div class="">
                <p>Hello Awesome People 👋</p><p>Holeswing is a premium high quality UI Kit for Golf sports app containing 180+ well designed screens. This UI Kit was designed very specially for Golf sport playing and social media.</p><p>And here are the Courses screens preview, consist of Golf Course list in thumbnails and CourseDetail screens.</p><p></p><p><a href="https://agensip.gumroad.com/l/holeswing" rel="nofollow noreferrer"><strong>BUY THIS PRODUCT HERE</strong></a></p><p><a href="https://agensip.gumroad.com/" rel="nofollow noreferrer">Our Product</a> | <a href="https://instagram.com/agensip" rel="nofollow noreferrer">IG</a> | <a href="https://fb.me/agensip" rel="nofollow noreferrer">FB</a> | <a href="https://tiwtter.com/agensip" rel="nofollow noreferrer">TW</a></p><p>---</p><p>We are Agensip, a creative digital agency focusing on UI and UX stuff. Kindly visit our website at <a href="http://www.agensip.com/" rel="noreferrer nofollow">www.agensip.com</a> to see more detail about us. Never hesitate to contact us via email at <a href="mailto:<EMAIL>" rel="noreferrer nofollow"><EMAIL></a></p>
              </div>
        </div>

        

        

          <div class="associated-good-container">
    <span class="associated-good-header">Associated product</span>
    <div class="associated-good-preview">
       <img width="76" height="57" data-id="1962300" data-srcset="https://cdn.dribbble.com/userupload/1962300/file/original-c6e202a8ca4de4b5276edd79a08ad9ce.png?compress=1&amp;resize=80x60&amp;vertical=top 80w, https://cdn.dribbble.com/userupload/1962300/file/original-c6e202a8ca4de4b5276edd79a08ad9ce.png?compress=1&amp;resize=160x120&amp;vertical=top 160w" data-src="https://cdn.dribbble.com/userupload/1962300/file/original-c6e202a8ca4de4b5276edd79a08ad9ce.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" class=" lazyload" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==">
      <div class="associated-good-details">
        <div class="associated-good-title">Holeswing Golf GPS Track UI Kit</div>
        <div class="color-forest font-heading-5">$75.00</div>
      </div>
      <a class="associated-good-btn form-forest" href="https://dribbble.com/goods/856224-Holeswing-Golf-GPS-Track-UI-Kit">View Product </a>
    </div>
  </div>


        

        

        <div class="user-details-section">
  <div class="user-avatar-container">
    <span class="user-avatar-line"></span>
    <a class="url" rel="contact" title="⚡️Agensip UI UX Agency" href="https://dribbble.com/agensip"><picture>
  <source srcset="https://cdn.dribbble.com/users/2547736/avatars/normal/0c4aff997b5469101760bff0f3c5c535.png?1536649020" media="(-webkit-min-device-pixel-ratio: 1.5), (min--moz-device-pixel-ratio: 1.5), (-o-min-device-pixel-ratio: 3/2), (min-device-pixel-ratio: 1.5), (min-resolution: 1.5dppx)">
  <source srcset="https://cdn.dribbble.com/users/2547736/avatars/small/0c4aff997b5469101760bff0f3c5c535.png?1536649020">
  <img class="photo" alt="⚡️Agensip UI UX Agency" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/0c4aff997b5469101760bff0f3c5c535(1).png">
</picture>
</a>
    <span class="user-avatar-line"></span>
  </div>

  <a class="user-name" rel="contact" href="https://dribbble.com/agensip">⚡️Agensip UI UX Agency</a>

    <div class="user-bio">
      your digital product design partner 👉🏻
    </div>


    



<script>
var trigger = 'a.contact';

if (!Dribbble.MessageModal ) {
  Dribbble.MessageModal = {
    init: function() {
      this.eventElements = [];
      this.newContent = true;
      Dribbble.EventBus.$on('searchResult:updated', this.bindEvents.bind(this));
      this.bindEvents();
    },

    bindEvents: function() {
      this.unbindEvents();

      this.setClickEvent();

      document.querySelectorAll(trigger).forEach(function(el) {
        this.eventElements.push(el);
        el.addEventListener('click', this.onClickTrigger, true);
      }.bind(this));
    },

    unbindEvents: function() {
      // remove previous event listeners
      if (this.eventElements.length) {
        this.eventElements.forEach(function(el) {
          el.removeEventListener('click', this.onClickTrigger, true);
        });
      }

      this.eventElements = [];
    },

    extractParams: function(e) {
      return Dribbble.Url.extractParams($(e.target).closest('a').attr('href'));
    },

    setClickEvent: function() {
      if (!Dribbble.MessageModal.newMessage) {
        Dribbble.MessageModal.newMessage = new Dribbble.MessageModalNew();
        Dribbble.MessageModal.newMessage.init({ maxLength: 8000 });
      }

        var designerSearchData = Dribbble.JsConfig.designerSearchData;

        if (designerSearchData
          && designerSearchData.activeSearch.workType
          && !designerSearchData[designerSearchData.activeSearch.workType].enabled
        ) {
          this.onClickTrigger = this.triggerUpSell;
        } else {
          if (this.newContent) {
            Dribbble.MessageModal.newMessage.messageAttemptOverlay = this.createMessageModal();
          }
          this.onClickTrigger = this.showMessageModal;
        }
    },

    triggerUpSell: function(e) {
      e.preventDefault();
      Dribbble.EventBus.$emit('designer-search-upsell', {
        isOpen: true,
        referrer: 'message',
      });
    },

    showMessageModal: function(e) {
      e.preventDefault();
      Dribbble.MessageModal.newMessage.messageAttemptOverlay.currentTarget = $(e.target).closest('a');
      Dribbble.MessageModal.newMessage.messageAttemptOverlay.show();
    },

    upsellRedirect: function(e) {
      e.preventDefault();
      window.location = "https://dribbble.com/hiring?message=true";
    },

    extractParams: function(e) {
      return Dribbble.Url.extractParams($(e.target).closest('a').attr('href'));
    },

    unverifiedRedirect: function(e) {
      e.preventDefault();
      window.location = "/account/unverified?" + this.extractParams(e);
    },

    messagesDisabledAlert: function(e) {
      e.preventDefault();
      Dribbble.Notify.alert("Messaging is temporarily disabled while we perform some maintenance. Thanks for your patience, please try again shortly.");
    },

    createMessageModal: function() {
      var contactOverlayId = "user-details-contact-overlay";

      return new Dribbble.Overlay.RemoteForm({
        $el: $("#" + contactOverlayId),
        onShow: function() {
          document.querySelector(".message-overlay").scrollIntoView();
        },
        onSubmit: Dribbble.MessageModal.newMessage.onSubmit.bind(Dribbble.MessageModal.newMessage),
        containerToUpdateOnFailure: '#' + contactOverlayId + ' .display',
        remoteContent: {
          el: '#' + contactOverlayId + ' .display',
          url: function() {

            return this.currentTarget.attr('href');
          },
          onComplete: Dribbble.MessageModal.newMessage.overlayLoaded.bind(Dribbble.MessageModal.newMessage)
        },
      });
    }
  };

  Dribbble.MessageModal.init();
  Dribbble.EventBus.$on('messageTriggerAdded', () => {
    Dribbble.MessageModal.bindEvents();
  });
} else {
  Dribbble.MessageModal.bindEvents();
}
</script>




<div class="profile-message">

    <a class="contact message-btn form-sub text-align-center w-100 " rel="tipsy" data-message-recipient="2547736" data-button-location="profile" href="https://dribbble.com/messages/new?recipient_id=agensip" original-title="Send a message about a work opportunity">
      <span class="check"><svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m21.28 4.473c-.848-.721-2.109-.604-2.817.262l-8.849 10.835-4.504-3.064c-.918-.626-2.161-.372-2.773.566s-.364 2.205.555 2.83l7.494 5.098 11.151-13.653c.707-.866.592-2.152-.257-2.874z"></path></svg>
</span>
      <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon "><path d="m12 13.595c-.715 0-1.43-.153-2.095-.46l-9.905-4.572v11.437c0 1.105.895 2 2 2h20c1.105 0 2-.895 2-2v-11.437l-9.905 4.572c-.665.307-1.38.46-2.095.46zm10-11.595h-20c-1.105 0-2 .895-2 2v2.36l10.743 4.958c.799.368 1.716.369 2.515 0l10.742-4.958v-2.36c0-1.105-.895-2-2-2z"></path></svg>

      <span>Hire Us</span>
</a></div>


</div>


          <div class="more-by-user-section">
    <div class="more-by-heading-container">
      <h4>More by ⚡️Agensip UI UX Agency</h4>
      <a href="https://dribbble.com/agensip">View profile</a>
    </div>

    <div class="more-by-thumbnails-container js-more-by lazyload" data-include="/shots/14728277/more_by">
        <div class="more-by-shot-thumbnail lazy-0">
          
<li class="shot-thumbnail">
  <div class="media-placeholder shot-details-container js-shot-details-container hidden">
    <div class="loading-template shot animate-translate"></div>
  </div>
</li>

        </div>
        <div class="more-by-shot-thumbnail lazy-1">
          
<li class="shot-thumbnail">
  <div class="media-placeholder shot-details-container js-shot-details-container hidden">
    <div class="loading-template shot animate-translate"></div>
  </div>
</li>

        </div>
        <div class="more-by-shot-thumbnail lazy-2">
          
<li class="shot-thumbnail">
  <div class="media-placeholder shot-details-container js-shot-details-container hidden">
    <div class="loading-template shot animate-translate"></div>
  </div>
</li>

        </div>
        <div class="more-by-shot-thumbnail lazy-3">
          
<li class="shot-thumbnail">
  <div class="media-placeholder shot-details-container js-shot-details-container hidden">
    <div class="loading-template shot animate-translate"></div>
  </div>
</li>

        </div>
    </div>
  </div>


            <hr class="shot-content-divider-full">
    <div class="similar-work-section">
  <h5>You might also like</h5>
  <div class="similar-work-thumbnails-container lazyload js-similar-work" data-include="/shots/14728277/similar">
      
<li class="shot-thumbnail">
  <div class="media-placeholder shot-details-container js-shot-details-container hidden">
    <div class="loading-template shot animate-translate"></div>
      <div class="display-flex align-center margin-t-8">
        <div class="loading-template avatar small"></div>
        <div class="loading-template avatar bar w-50 hx-20 margin-l-8"></div>
      </div>
  </div>
</li>

      
<li class="shot-thumbnail">
  <div class="media-placeholder shot-details-container js-shot-details-container hidden">
    <div class="loading-template shot animate-translate"></div>
      <div class="display-flex align-center margin-t-8">
        <div class="loading-template avatar small"></div>
        <div class="loading-template avatar bar w-50 hx-20 margin-l-8"></div>
      </div>
  </div>
</li>

      
<li class="shot-thumbnail">
  <div class="media-placeholder shot-details-container js-shot-details-container hidden">
    <div class="loading-template shot animate-translate"></div>
      <div class="display-flex align-center margin-t-8">
        <div class="loading-template avatar small"></div>
        <div class="loading-template avatar bar w-50 hx-20 margin-l-8"></div>
      </div>
  </div>
</li>

      
<li class="shot-thumbnail">
  <div class="media-placeholder shot-details-container js-shot-details-container hidden">
    <div class="loading-template shot animate-translate"></div>
      <div class="display-flex align-center margin-t-8">
        <div class="loading-template avatar small"></div>
        <div class="loading-template avatar bar w-50 hx-20 margin-l-8"></div>
      </div>
  </div>
</li>

      
<li class="shot-thumbnail">
  <div class="media-placeholder shot-details-container js-shot-details-container hidden">
    <div class="loading-template shot animate-translate"></div>
      <div class="display-flex align-center margin-t-8">
        <div class="loading-template avatar small"></div>
        <div class="loading-template avatar bar w-50 hx-20 margin-l-8"></div>
      </div>
  </div>
</li>

      
<li class="shot-thumbnail">
  <div class="media-placeholder shot-details-container js-shot-details-container hidden">
    <div class="loading-template shot animate-translate"></div>
      <div class="display-flex align-center margin-t-8">
        <div class="loading-template avatar small"></div>
        <div class="loading-template avatar bar w-50 hx-20 margin-l-8"></div>
      </div>
  </div>
</li>

  </div>
</div>


    </div>
  </div>

    <div class="shots-related-goods-container">
      <div class="shots-grid">
        <div class="lazyloading-hidden grid-full-width lazyload injected-goods" data-include="css:https://cdn.dribbble.com/assets/components/good-thumbnails-1e6f91c5261abc212db3c9d49fb84da66ef9bf86a0541f64253d51451ad07192.css">
  <div class="container-fluid good-list-header">
    <span class="font-heading-4">Related marketplace goods</span>
    <a href="https://dribbble.com/marketplace">Browse Marketplace</a>
  </div>
  <div class="relative">
    <ol class="js-injected-good-grid injected-good-list scrollable">
      <li class="js-scroll-backward scroll-backward">
        <a href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail#">
          <svg><!-- SVG file not found: 'icons/caret-right-circle.svg.svg' --></svg>
</a>      </li>
      <li class="js-scroll-forward scroll-forward">
        <a href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail#">
            <svg><!-- SVG file not found: 'icons/caret-right-circle.svg.svg' --></svg>
</a>      </li>
      
<li data-thumbnail-id="856224" data-pointguard-token="gAAAAABiHHGJKnxWJi-tlOuGrvQeNz2Kc6QyAW8FYbfe4brkZ0gFjaUkYwNTe7k0FZplhB_GSQPBGivLRqq4JVeeWo3i7WokNBv297qR_1QVqhYp_vWPimBfCEBxj9hvdnPqCrrlZXH0zlucyHFKqydi-N7S56f71zSaWolLREFmldEMV6jtilE=" class="js-thumbnail thumbnail-container">
  <a class="js-good-link dribbble-link" href="https://dribbble.com/goods/856224-Holeswing-Golf-GPS-Track-UI-Kit?token=gAAAAABiHHGJKnxWJi-tlOuGrvQeNz2Kc6QyAW8FYbfe4brkZ0gFjaUkYwNTe7k0FZplhB_GSQPBGivLRqq4JVeeWo3i7WokNBv297qR_1QVqhYp_vWPimBfCEBxj9hvdnPqCrrlZXH0zlucyHFKqydi-N7S56f71zSaWolLREFmldEMV6jtilE%3D">
  <div class="js-thumbnail-base thumbnail-base margin-b-16 ">
        <div class="loading-indicator good-type-indicators">

            <div class="good-type-icon multi"></div>
        </div>
      <figure class="js-thumbnail-placeholder thumbnail-placeholder">
        <noscript>
          <img src="https://cdn.dribbble.com/userupload/1962300/file/original-c6e202a8ca4de4b5276edd79a08ad9ce.png?compress=1&amp;resize=400x300&amp;vertical=top" alt="Holeswing Golf GPS Track UI Kit">
        </noscript>
        <img alt="Holeswing Golf GPS Track UI Kit" width="330" height="247" data-srcset="https://cdn.dribbble.com/userupload/1962300/file/original-c6e202a8ca4de4b5276edd79a08ad9ce.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/userupload/1962300/file/original-c6e202a8ca4de4b5276edd79a08ad9ce.png?compress=1&amp;resize=400x300&amp;vertical=top 400w" data-src="https://cdn.dribbble.com/userupload/1962300/file/original-c6e202a8ca4de4b5276edd79a08ad9ce.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" data-animated-url="https://cdn.dribbble.com/userupload/1962300/file/original-c6e202a8ca4de4b5276edd79a08ad9ce.png" class=" lazyload" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==">
      </figure>
  </div>
</a>    <div class="display-flex justify-space-between">
      <div class="good-details">
          <img class="good-avatar lazyload" alt="⚡️Agensip UI UX Agency" width="32" height="32" data-src="https://cdn.dribbble.com/users/2547736/avatars/mini/0c4aff997b5469101760bff0f3c5c535.png?1536649020" src="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail">
        <div class="good-title margin-r-8">
          <a class="js-good-link dribbble-link font-sublabel-bold good-title-text" href="https://dribbble.com/goods/856224-Holeswing-Golf-GPS-Track-UI-Kit?token=gAAAAABiHHGJKnxWJi-tlOuGrvQeNz2Kc6QyAW8FYbfe4brkZ0gFjaUkYwNTe7k0FZplhB_GSQPBGivLRqq4JVeeWo3i7WokNBv297qR_1QVqhYp_vWPimBfCEBxj9hvdnPqCrrlZXH0zlucyHFKqydi-N7S56f71zSaWolLREFmldEMV6jtilE%3D">Holeswing Golf GPS Track UI Kit</a>
          <a href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail"></a><a class="font-body-small good-title-text" rel="contact" href="https://dribbble.com/agensip">⚡️Agensip UI UX Agency</a>
        </div>
      </div>
      <div class="font-sublabel-bold color-blurple">
        $75.00
      </div>
    </div>
</li>

<li data-thumbnail-id="380135" data-pointguard-token="gAAAAABiHHGJAKYX5xtg4MlrpSARqemRetc02VRB0uWlEftHW1g7Nrp_svnPygfkaY4a2L-ZWVZWgvxb6Y85WvctVYLpnIf4ONYemQcqmIhZ7MHHlrey4A6vTaPEvB8DUb7iu88fveUFY1wfsABYwwF0vSoCdi5EZ77blBDAGMjLyyZcGB4kvzY=" class="js-thumbnail thumbnail-container">
  <a class="js-good-link dribbble-link" href="https://dribbble.com/goods/380135-UNIVERSAL-ICON-SET?token=gAAAAABiHHGJAKYX5xtg4MlrpSARqemRetc02VRB0uWlEftHW1g7Nrp_svnPygfkaY4a2L-ZWVZWgvxb6Y85WvctVYLpnIf4ONYemQcqmIhZ7MHHlrey4A6vTaPEvB8DUb7iu88fveUFY1wfsABYwwF0vSoCdi5EZ77blBDAGMjLyyZcGB4kvzY%3D">
  <div class="js-thumbnail-base thumbnail-base margin-b-16 ">
        <div class="loading-indicator good-type-indicators">

            <div class="good-type-icon multi"></div>
        </div>
      <figure class="js-thumbnail-placeholder thumbnail-placeholder">
        <noscript>
          <img src="https://cdn.dribbble.com/userupload/768647/file/original-fd973492b5f9e50b646484bc1aee11ac.png?compress=1&amp;resize=400x300&amp;vertical=top" alt="UNIVERSAL ICON SET">
        </noscript>
        <img alt="UNIVERSAL ICON SET" width="330" height="247" data-srcset="https://cdn.dribbble.com/userupload/768647/file/original-fd973492b5f9e50b646484bc1aee11ac.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/userupload/768647/file/original-fd973492b5f9e50b646484bc1aee11ac.png?compress=1&amp;resize=400x300&amp;vertical=top 400w" data-src="https://cdn.dribbble.com/userupload/768647/file/original-fd973492b5f9e50b646484bc1aee11ac.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" data-animated-url="https://cdn.dribbble.com/userupload/768647/file/original-fd973492b5f9e50b646484bc1aee11ac.png" class=" lazyload" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==">
      </figure>
  </div>
</a>    <div class="display-flex justify-space-between">
      <div class="good-details">
          <img class="good-avatar lazyload" alt="Dima Groshev | 123done" width="32" height="32" data-src="https://cdn.dribbble.com/users/14243/avatars/mini/2f34a28079ec5350b2bf0e7321b493da.jpeg?1615132263" src="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail">
        <div class="good-title margin-r-8">
          <a class="js-good-link dribbble-link font-sublabel-bold good-title-text" href="https://dribbble.com/goods/380135-UNIVERSAL-ICON-SET?token=gAAAAABiHHGJAKYX5xtg4MlrpSARqemRetc02VRB0uWlEftHW1g7Nrp_svnPygfkaY4a2L-ZWVZWgvxb6Y85WvctVYLpnIf4ONYemQcqmIhZ7MHHlrey4A6vTaPEvB8DUb7iu88fveUFY1wfsABYwwF0vSoCdi5EZ77blBDAGMjLyyZcGB4kvzY%3D">UNIVERSAL ICON SET</a>
          <a href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail"></a><a class="font-body-small good-title-text" rel="contact" href="https://dribbble.com/dimagroshev">Dima Groshev | 123done</a>
        </div>
      </div>
      <div class="font-sublabel-bold color-blurple">
        $129.00
      </div>
    </div>
</li>

<li data-thumbnail-id="822964" data-pointguard-token="gAAAAABiHHGJHUNHKx4VXrDnA16X-PJRhHd8E4qK9CPuL8XsD9urn0LAcMoo3zmkv2EYsjUADI3JasNf29RsjJ1XaQFMtkRKMTAj5TT6vz-kg1t4zGCRXJ91sGkj1dcPrDdFVKLU6CS9CdqDySH-HDvfiEyZ0wTfkICvL9hMnI6QdiB5sOx-6ig=" class="js-thumbnail thumbnail-container">
  <a class="js-good-link dribbble-link" href="https://dribbble.com/goods/822964-Boxboard-Dashboard-Ui-Kit?token=gAAAAABiHHGJHUNHKx4VXrDnA16X-PJRhHd8E4qK9CPuL8XsD9urn0LAcMoo3zmkv2EYsjUADI3JasNf29RsjJ1XaQFMtkRKMTAj5TT6vz-kg1t4zGCRXJ91sGkj1dcPrDdFVKLU6CS9CdqDySH-HDvfiEyZ0wTfkICvL9hMnI6QdiB5sOx-6ig%3D">
  <div class="js-thumbnail-base thumbnail-base margin-b-16 ">
        <div class="loading-indicator good-type-indicators">

            <div class="good-type-icon multi"></div>
        </div>
      <figure class="js-thumbnail-placeholder thumbnail-placeholder">
        <noscript>
          <img src="https://cdn.dribbble.com/userupload/1865035/file/original-7798a9b0172596665998a1ca700fa498.png?compress=1&amp;resize=400x300&amp;vertical=top" alt="Boxboard Dashboard Ui Kit">
        </noscript>
        <img alt="Boxboard Dashboard Ui Kit" width="330" height="247" data-srcset="https://cdn.dribbble.com/userupload/1865035/file/original-7798a9b0172596665998a1ca700fa498.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/userupload/1865035/file/original-7798a9b0172596665998a1ca700fa498.png?compress=1&amp;resize=400x300&amp;vertical=top 400w" data-src="https://cdn.dribbble.com/userupload/1865035/file/original-7798a9b0172596665998a1ca700fa498.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" data-animated-url="https://cdn.dribbble.com/userupload/1865035/file/original-7798a9b0172596665998a1ca700fa498.png" class=" lazyload" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==">
      </figure>
  </div>
</a>    <div class="display-flex justify-space-between">
      <div class="good-details">
          <img class="good-avatar lazyload" alt="Uiscore" width="32" height="32" data-src="https://cdn.dribbble.com/users/1839804/avatars/mini/a1bbac5ba9a82b647d5a9c344a40c301.png?1645356222" src="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail">
        <div class="good-title margin-r-8">
          <a class="js-good-link dribbble-link font-sublabel-bold good-title-text" href="https://dribbble.com/goods/822964-Boxboard-Dashboard-Ui-Kit?token=gAAAAABiHHGJHUNHKx4VXrDnA16X-PJRhHd8E4qK9CPuL8XsD9urn0LAcMoo3zmkv2EYsjUADI3JasNf29RsjJ1XaQFMtkRKMTAj5TT6vz-kg1t4zGCRXJ91sGkj1dcPrDdFVKLU6CS9CdqDySH-HDvfiEyZ0wTfkICvL9hMnI6QdiB5sOx-6ig%3D">Boxboard Dashboard Ui Kit</a>
          <a href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail"></a><a class="font-body-small good-title-text" rel="contact" href="https://dribbble.com/uiscore">Uiscore</a>
        </div>
      </div>
      <div class="font-sublabel-bold color-blurple">
        $68.00
      </div>
    </div>
</li>

<li data-thumbnail-id="415499" data-pointguard-token="gAAAAABiHHGJ9AMax49A0UX2rT7dDMbZo-ihWA6ZX658P4QrDF526aUhkwjtF4EAJQ0pQ0zOsDELWOd_g40-ZFnmCtFJPsvfxDs-FgDzltcbj4pYxlUAfDhEotJgLLoDJolQoKzjQMD63qqKE20OopijKpxNdUC2wz7pEy7O6e5l8zc381Q49sE=" class="js-thumbnail thumbnail-container">
  <a class="js-good-link dribbble-link" href="https://dribbble.com/goods/415499-App-UI-Mockup-Phone-Screen-Mockup?token=gAAAAABiHHGJ9AMax49A0UX2rT7dDMbZo-ihWA6ZX658P4QrDF526aUhkwjtF4EAJQ0pQ0zOsDELWOd_g40-ZFnmCtFJPsvfxDs-FgDzltcbj4pYxlUAfDhEotJgLLoDJolQoKzjQMD63qqKE20OopijKpxNdUC2wz7pEy7O6e5l8zc381Q49sE%3D">
  <div class="js-thumbnail-base thumbnail-base margin-b-16 ">
        <div class="loading-indicator good-type-indicators">

            <div class="good-type-icon multi"></div>
        </div>
      <figure class="js-thumbnail-placeholder thumbnail-placeholder">
        <noscript>
          <img src="https://cdn.dribbble.com/userupload/899062/file/original-888ce94ff829226955ef496720bd1db9.jpg?compress=1&amp;resize=400x300&amp;vertical=top" alt="App UI Mockup / Phone Screen Mockup">
        </noscript>
        <img alt="App UI Mockup / Phone Screen Mockup" width="330" height="247" data-srcset="https://cdn.dribbble.com/userupload/899062/file/original-888ce94ff829226955ef496720bd1db9.jpg?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/userupload/899062/file/original-888ce94ff829226955ef496720bd1db9.jpg?compress=1&amp;resize=400x300&amp;vertical=top 400w" data-src="https://cdn.dribbble.com/userupload/899062/file/original-888ce94ff829226955ef496720bd1db9.jpg?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" data-animated-url="https://cdn.dribbble.com/userupload/899062/file/original-888ce94ff829226955ef496720bd1db9.jpg" class=" lazyload" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==">
      </figure>
  </div>
</a>    <div class="display-flex justify-space-between">
      <div class="good-details">
          <img class="good-avatar lazyload" alt="Eka Restu D. Putra" width="32" height="32" data-src="https://cdn.dribbble.com/users/2906263/avatars/mini/4b1b836da674e5cb13ba031a475eb74c.png?1637885675" src="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail">
        <div class="good-title margin-r-8">
          <a class="js-good-link dribbble-link font-sublabel-bold good-title-text" href="https://dribbble.com/goods/415499-App-UI-Mockup-Phone-Screen-Mockup?token=gAAAAABiHHGJ9AMax49A0UX2rT7dDMbZo-ihWA6ZX658P4QrDF526aUhkwjtF4EAJQ0pQ0zOsDELWOd_g40-ZFnmCtFJPsvfxDs-FgDzltcbj4pYxlUAfDhEotJgLLoDJolQoKzjQMD63qqKE20OopijKpxNdUC2wz7pEy7O6e5l8zc381Q49sE%3D">App UI Mockup / Phone Screen Mockup</a>
          <a href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail"></a><a class="font-body-small good-title-text" rel="contact" href="https://dribbble.com/erdpme">Eka Restu D. Putra</a>
        </div>
      </div>
      <div class="font-sublabel-bold color-blurple">
        $30.00
      </div>
    </div>
</li>

<li data-thumbnail-id="375176" data-pointguard-token="gAAAAABiHHGJY-TLdQnY7cC1BFidEhkrelmzJO1Ymcxid3mDwiXrODjCADfWUg0_EaLN8B01uq3s29qFhC1fFSE6MPKFtzrHXv3pyuBlb6hWrEvxptRNrtXUGt3xWUDlbSdUkPqoSikO-nrL7EbzB7o6gC07Sxe5gbbjh0WXyRmPQiNKr7zD0MY=" class="js-thumbnail thumbnail-container">
  <a class="js-good-link dribbble-link" href="https://dribbble.com/goods/375176-Neumorphic-Element-Mobile-App-UI-Kit?token=gAAAAABiHHGJY-TLdQnY7cC1BFidEhkrelmzJO1Ymcxid3mDwiXrODjCADfWUg0_EaLN8B01uq3s29qFhC1fFSE6MPKFtzrHXv3pyuBlb6hWrEvxptRNrtXUGt3xWUDlbSdUkPqoSikO-nrL7EbzB7o6gC07Sxe5gbbjh0WXyRmPQiNKr7zD0MY%3D">
  <div class="js-thumbnail-base thumbnail-base margin-b-16 ">
        <div class="loading-indicator good-type-indicators">

            <div class="good-type-icon multi"></div>
        </div>
      <figure class="js-thumbnail-placeholder thumbnail-placeholder">
        <noscript>
          <img src="https://cdn.dribbble.com/userupload/747193/file/original-e32707248ce99e71455e413d726d4e8c.jpg?compress=1&amp;resize=400x300&amp;vertical=top" alt="Neumorphic Element Mobile App UI Kit">
        </noscript>
        <img alt="Neumorphic Element Mobile App UI Kit" width="330" height="247" data-srcset="https://cdn.dribbble.com/userupload/747193/file/original-e32707248ce99e71455e413d726d4e8c.jpg?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/userupload/747193/file/original-e32707248ce99e71455e413d726d4e8c.jpg?compress=1&amp;resize=400x300&amp;vertical=top 400w" data-src="https://cdn.dribbble.com/userupload/747193/file/original-e32707248ce99e71455e413d726d4e8c.jpg?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" data-animated-url="https://cdn.dribbble.com/userupload/747193/file/original-e32707248ce99e71455e413d726d4e8c.jpg" class=" lazyload" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==">
      </figure>
  </div>
</a>    <div class="display-flex justify-space-between">
      <div class="good-details">
          <img class="good-avatar lazyload" alt="alexdndz" width="32" height="32" data-src="https://cdn.dribbble.com/users/2537629/avatars/mini/e39ddece5b612f838ff03901df0935cd.jpg?1601323941" src="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail">
        <div class="good-title margin-r-8">
          <a class="js-good-link dribbble-link font-sublabel-bold good-title-text" href="https://dribbble.com/goods/375176-Neumorphic-Element-Mobile-App-UI-Kit?token=gAAAAABiHHGJY-TLdQnY7cC1BFidEhkrelmzJO1Ymcxid3mDwiXrODjCADfWUg0_EaLN8B01uq3s29qFhC1fFSE6MPKFtzrHXv3pyuBlb6hWrEvxptRNrtXUGt3xWUDlbSdUkPqoSikO-nrL7EbzB7o6gC07Sxe5gbbjh0WXyRmPQiNKr7zD0MY%3D">Neumorphic Element Mobile App UI Kit</a>
          <a href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail"></a><a class="font-body-small good-title-text" rel="contact" href="https://dribbble.com/alexdndz">alexdndz</a>
        </div>
      </div>
      <div class="font-sublabel-bold color-blurple">
        $24.00
      </div>
    </div>
</li>

<li data-thumbnail-id="933248" data-pointguard-token="gAAAAABiHHGJwejNi4AW4fkhrxPIdXn7iUrkGXkn0ku9hw7I02YGQ-3HdO5PY2lNx5FvgCtEQAw7QqWgBm0mgJqH9jn6ZdhhxgqzM5sxH-KPxQleYvgiKFVN0tQ813wC0g4A4SYEYyFxLv3y2kU0uufTJr2cm1g8PzdI8Ya20C_bqQ-16sydwsY=" class="js-thumbnail thumbnail-container">
  <a class="js-good-link dribbble-link" href="https://dribbble.com/goods/933248-Instagram-Podcast-Series-CANVA?token=gAAAAABiHHGJwejNi4AW4fkhrxPIdXn7iUrkGXkn0ku9hw7I02YGQ-3HdO5PY2lNx5FvgCtEQAw7QqWgBm0mgJqH9jn6ZdhhxgqzM5sxH-KPxQleYvgiKFVN0tQ813wC0g4A4SYEYyFxLv3y2kU0uufTJr2cm1g8PzdI8Ya20C_bqQ-16sydwsY%3D">
  <div class="js-thumbnail-base thumbnail-base margin-b-16 ">
        <div class="loading-indicator good-type-indicators">

            <div class="good-type-icon multi"></div>
        </div>
      <figure class="js-thumbnail-placeholder thumbnail-placeholder">
        <noscript>
          <img src="https://cdn.dribbble.com/userupload/2229789/file/original-ad0961b91b6b2dc188df4a7aebaef933.png?compress=1&amp;resize=400x300&amp;vertical=top" alt="Instagram Podcast Series CANVA">
        </noscript>
        <img alt="Instagram Podcast Series CANVA" width="330" height="247" data-srcset="https://cdn.dribbble.com/userupload/2229789/file/original-ad0961b91b6b2dc188df4a7aebaef933.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/userupload/2229789/file/original-ad0961b91b6b2dc188df4a7aebaef933.png?compress=1&amp;resize=400x300&amp;vertical=top 400w" data-src="https://cdn.dribbble.com/userupload/2229789/file/original-ad0961b91b6b2dc188df4a7aebaef933.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" data-animated-url="https://cdn.dribbble.com/userupload/2229789/file/original-ad0961b91b6b2dc188df4a7aebaef933.png" class=" lazyload" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==">
      </figure>
  </div>
</a>    <div class="display-flex justify-space-between">
      <div class="good-details">
          <img class="good-avatar lazyload" alt="Ezalor Studio" width="32" height="32" data-src="https://cdn.dribbble.com/users/9492765/avatars/mini/e042ba888bdd39afbbc080d6bef0279d.jpeg?1634267458" src="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail">
        <div class="good-title margin-r-8">
          <a class="js-good-link dribbble-link font-sublabel-bold good-title-text" href="https://dribbble.com/goods/933248-Instagram-Podcast-Series-CANVA?token=gAAAAABiHHGJwejNi4AW4fkhrxPIdXn7iUrkGXkn0ku9hw7I02YGQ-3HdO5PY2lNx5FvgCtEQAw7QqWgBm0mgJqH9jn6ZdhhxgqzM5sxH-KPxQleYvgiKFVN0tQ813wC0g4A4SYEYyFxLv3y2kU0uufTJr2cm1g8PzdI8Ya20C_bqQ-16sydwsY%3D">Instagram Podcast Series CANVA</a>
          <a href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail"></a><a class="font-body-small good-title-text" rel="contact" href="https://dribbble.com/EzalorStudio">Ezalor Studio</a>
        </div>
      </div>
      <div class="font-sublabel-bold color-blurple">
        $44.00
      </div>
    </div>
</li>

<li data-thumbnail-id="427634" data-pointguard-token="gAAAAABiHHGJQ7uJUdk37Rkfsxjs9ybM6FGie58-gJYqO-QGq2d9vJ0iZqu3P0KN48_TCSwUHjGLKPJCxsaHGd_qUdkaEBAmHFatZ6RpcBf-QSrDTHkgQs2NsiGwr0tFsdCdbvxFWXZBZOpKagrDCxxCqcH3syd3cS18R2XpZrMiXPnsWt9H-CY=" class="js-thumbnail thumbnail-container">
  <a class="js-good-link dribbble-link" href="https://dribbble.com/goods/427634-BUNDLE-Instagram-Templates-CANVA?token=gAAAAABiHHGJQ7uJUdk37Rkfsxjs9ybM6FGie58-gJYqO-QGq2d9vJ0iZqu3P0KN48_TCSwUHjGLKPJCxsaHGd_qUdkaEBAmHFatZ6RpcBf-QSrDTHkgQs2NsiGwr0tFsdCdbvxFWXZBZOpKagrDCxxCqcH3syd3cS18R2XpZrMiXPnsWt9H-CY%3D">
  <div class="js-thumbnail-base thumbnail-base margin-b-16 ">
        <div class="loading-indicator good-type-indicators">

            <div class="good-type-icon multi"></div>
        </div>
      <figure class="js-thumbnail-placeholder thumbnail-placeholder">
        <noscript>
          <img src="https://cdn.dribbble.com/userupload/946124/file/original-7fcfa1888bbb55846ddeec1e1a1b7769.png?compress=1&amp;resize=400x300&amp;vertical=top" alt="BUNDLE Instagram Templates | CANVA">
        </noscript>
        <img alt="BUNDLE Instagram Templates | CANVA" width="330" height="247" data-srcset="https://cdn.dribbble.com/userupload/946124/file/original-7fcfa1888bbb55846ddeec1e1a1b7769.png?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/userupload/946124/file/original-7fcfa1888bbb55846ddeec1e1a1b7769.png?compress=1&amp;resize=400x300&amp;vertical=top 400w" data-src="https://cdn.dribbble.com/userupload/946124/file/original-7fcfa1888bbb55846ddeec1e1a1b7769.png?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" data-animated-url="https://cdn.dribbble.com/userupload/946124/file/original-7fcfa1888bbb55846ddeec1e1a1b7769.png" class=" lazyload" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==">
      </figure>
  </div>
</a>    <div class="display-flex justify-space-between">
      <div class="good-details">
          <img class="good-avatar lazyload" alt="Julia Orlova" width="32" height="32" data-src="https://cdn.dribbble.com/users/3276672/avatars/mini/97a91c3b76fcc9f697b4b0d702048afe.jpg?1568200633" src="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail">
        <div class="good-title margin-r-8">
          <a class="js-good-link dribbble-link font-sublabel-bold good-title-text" href="https://dribbble.com/goods/427634-BUNDLE-Instagram-Templates-CANVA?token=gAAAAABiHHGJQ7uJUdk37Rkfsxjs9ybM6FGie58-gJYqO-QGq2d9vJ0iZqu3P0KN48_TCSwUHjGLKPJCxsaHGd_qUdkaEBAmHFatZ6RpcBf-QSrDTHkgQs2NsiGwr0tFsdCdbvxFWXZBZOpKagrDCxxCqcH3syd3cS18R2XpZrMiXPnsWt9H-CY%3D">BUNDLE Instagram Templates | CANVA</a>
          <a href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail"></a><a class="font-body-small good-title-text" rel="contact" href="https://dribbble.com/Julia_Orlova">Julia Orlova</a>
        </div>
      </div>
      <div class="font-sublabel-bold color-blurple">
        $42.00
      </div>
    </div>
</li>

<li data-thumbnail-id="678066" data-pointguard-token="gAAAAABiHHGJZMiGYkrbQhT0QfRLaOmT6nGgpMVGR1bH4yjPED4iUkgoTfHduFW2xGv82xaJrFj0oz2PPxi7eqaPUUVgihq6Ks3x2IDJzrnkqLZLiKfmAsxhxvfO5rqP5JBWcIf2n4EQkdv-46StNmsQfRTYBuzzWbmVwq611XasAns1JQd2Y-I=" class="js-thumbnail thumbnail-container">
  <a class="js-good-link dribbble-link" href="https://dribbble.com/goods/678066-Atelier-Retro-Vintage-Social-media?token=gAAAAABiHHGJZMiGYkrbQhT0QfRLaOmT6nGgpMVGR1bH4yjPED4iUkgoTfHduFW2xGv82xaJrFj0oz2PPxi7eqaPUUVgihq6Ks3x2IDJzrnkqLZLiKfmAsxhxvfO5rqP5JBWcIf2n4EQkdv-46StNmsQfRTYBuzzWbmVwq611XasAns1JQd2Y-I%3D">
  <div class="js-thumbnail-base thumbnail-base margin-b-16 ">
        <div class="loading-indicator good-type-indicators">

            <div class="good-type-icon multi"></div>
        </div>
      <figure class="js-thumbnail-placeholder thumbnail-placeholder">
        <noscript>
          <img src="https://cdn.dribbble.com/userupload/1380123/file/original-4ccf1b05fff73c56cf35173c316c738c.jpg?compress=1&amp;resize=400x300&amp;vertical=top" alt="Atelier - Retro Vintage Social media">
        </noscript>
        <img alt="Atelier - Retro Vintage Social media" width="330" height="247" data-srcset="https://cdn.dribbble.com/userupload/1380123/file/original-4ccf1b05fff73c56cf35173c316c738c.jpg?compress=1&amp;resize=320x240&amp;vertical=top 320w, https://cdn.dribbble.com/userupload/1380123/file/original-4ccf1b05fff73c56cf35173c316c738c.jpg?compress=1&amp;resize=400x300&amp;vertical=top 400w" data-src="https://cdn.dribbble.com/userupload/1380123/file/original-4ccf1b05fff73c56cf35173c316c738c.jpg?compress=1&amp;resize=400x300&amp;vertical=top" data-sizes="auto" data-animated-url="https://cdn.dribbble.com/userupload/1380123/file/original-4ccf1b05fff73c56cf35173c316c738c.jpg" class=" lazyload" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==">
      </figure>
  </div>
</a>    <div class="display-flex justify-space-between">
      <div class="good-details">
          <img class="good-avatar lazyload" alt="Dirtytemp" width="32" height="32" data-src="https://cdn.dribbble.com/users/9842290/avatars/mini/data?1637140835" src="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail">
        <div class="good-title margin-r-8">
          <a class="js-good-link dribbble-link font-sublabel-bold good-title-text" href="https://dribbble.com/goods/678066-Atelier-Retro-Vintage-Social-media?token=gAAAAABiHHGJZMiGYkrbQhT0QfRLaOmT6nGgpMVGR1bH4yjPED4iUkgoTfHduFW2xGv82xaJrFj0oz2PPxi7eqaPUUVgihq6Ks3x2IDJzrnkqLZLiKfmAsxhxvfO5rqP5JBWcIf2n4EQkdv-46StNmsQfRTYBuzzWbmVwq611XasAns1JQd2Y-I%3D">Atelier - Retro Vintage Social media</a>
          <a href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail"></a><a class="font-body-small good-title-text" rel="contact" href="https://dribbble.com/Dirtytemp">Dirtytemp</a>
        </div>
      </div>
      <div class="font-sublabel-bold color-blurple">
        $25.00
      </div>
    </div>
</li>

    </ol>
  </div>
</div>


        <div class="shot-footer-container">
  <div class="keyboard-legend">
      <div class="keyboard-button-container">
        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="25" viewBox="0 0 22 25" fill="none" role="img" class="icon keyboard-button-icon">
<g filter="url(#filter0_d)">
<rect y="0.865234" width="22" height="22" rx="5" fill="white"></rect>
<path d="M12.2748 7.82637C12.539 7.5633 12.9673 7.5633 13.2315 7.82637C13.4957 8.08945 13.4957 8.51598 13.2315 8.77905L10.202 11.7958L13.2315 14.8124C13.4957 15.0755 13.4957 15.502 13.2315 15.7651C12.9673 16.0282 12.5389 16.0282 12.2747 15.7651L8.77674 12.2819C8.7734 12.2787 8.77008 12.2755 8.76679 12.2722C8.6801 12.1859 8.62186 12.082 8.59206 11.972C8.59133 11.9693 8.59061 11.9666 8.58992 11.9639C8.53234 11.7408 8.59127 11.494 8.76672 11.3193C8.77106 11.3149 8.77545 11.3107 8.77987 11.3065L12.2748 7.82637Z" fill="#9E9EA7"></path>
<rect x="0.5" y="1.36523" width="21" height="21" rx="4.5" stroke="#DBDADE"></rect>
</g>
<defs>
<filter id="filter0_d" x="0" y="0.865234" width="22" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
<fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"></fecolormatrix>
<feoffset dy="2"></feoffset>
<fecolormatrix type="matrix" values="0 0 0 0 0.858824 0 0 0 0 0.854902 0 0 0 0 0.870588 0 0 0 1 0"></fecolormatrix>
<feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"></feblend>
<feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"></feblend>
</filter>
</defs>
</svg>

        Previous shot
      </div>
      <div class="keyboard-button-container">
        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="25" viewBox="0 0 22 25" fill="none" role="img" class="icon keyboard-button-icon">
<g filter="url(#filter0_d)">
<rect y="0.865234" width="22" height="22" rx="5" fill="white"></rect>
<path d="M9.72519 7.82638C9.461 7.5633 9.03265 7.5633 8.76846 7.82638C8.50426 8.08945 8.50426 8.51598 8.76846 8.77905L11.798 11.7958L8.76853 14.8124C8.50434 15.0755 8.50434 15.502 8.76853 15.7651C9.03273 16.0282 9.46107 16.0282 9.72526 15.7651L13.2233 12.2819C13.2266 12.2787 13.2299 12.2755 13.2332 12.2722C13.3199 12.1859 13.3781 12.082 13.4079 11.972C13.4087 11.9693 13.4094 11.9666 13.4101 11.9639C13.4677 11.7408 13.4087 11.494 13.2333 11.3193C13.2289 11.3149 13.2246 11.3107 13.2201 11.3065L9.72519 7.82638Z" fill="#9E9EA7"></path>
<rect x="0.5" y="1.36523" width="21" height="21" rx="4.5" stroke="#DBDADE"></rect>
</g>
<defs>
<filter id="filter0_d" x="0" y="0.865234" width="22" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
<fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"></fecolormatrix>
<feoffset dy="2"></feoffset>
<fecolormatrix type="matrix" values="0 0 0 0 0.858824 0 0 0 0 0.854902 0 0 0 0 0.870588 0 0 0 1 0"></fecolormatrix>
<feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"></feblend>
<feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"></feblend>
</filter>
</defs>
</svg>

        Next shot
      </div>
    <div class="keyboard-button-container">
      <svg xmlns="http://www.w3.org/2000/svg" width="22" height="25" viewBox="0 0 22 25" fill="none" role="img" class="icon keyboard-button-icon">
<g filter="url(#filter0_d)">
<rect y="0.865234" width="22" height="22" rx="5" fill="white"></rect>
<path d="M8.27 14.8652H13.77V13.5342H9.788V6.68123H8.27V14.8652Z" fill="#9E9EA7"></path>
<rect x="0.5" y="1.36523" width="21" height="21" rx="4.5" stroke="#DBDADE"></rect>
</g>
<defs>
<filter id="filter0_d" x="0" y="0.865234" width="22" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
<fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"></fecolormatrix>
<feoffset dy="2"></feoffset>
<fecolormatrix type="matrix" values="0 0 0 0 0.858824 0 0 0 0 0.854902 0 0 0 0 0.870588 0 0 0 1 0"></fecolormatrix>
<feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"></feblend>
<feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"></feblend>
</filter>
</defs>
</svg>

      <svg xmlns="http://www.w3.org/2000/svg" width="22" height="25" viewBox="0 0 22 25" fill="none" role="img" class="icon keyboard-button-icon">
<g filter="url(#filter0_d)">
<rect y="0.865234" width="22" height="22" rx="5" fill="white"></rect>
<path d="M13.363 11.3562V10.0912H9.788V7.99023H14.078V6.68123H8.27V14.8652H9.788V11.3562H13.363Z" fill="#9E9EA7"></path>
<rect x="0.5" y="1.36523" width="21" height="21" rx="4.5" stroke="#DBDADE"></rect>
</g>
<defs>
<filter id="filter0_d" x="0" y="0.865234" width="22" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
<fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"></fecolormatrix>
<feoffset dy="2"></feoffset>
<fecolormatrix type="matrix" values="0 0 0 0 0.858824 0 0 0 0 0.854902 0 0 0 0 0.870588 0 0 0 1 0"></fecolormatrix>
<feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"></feblend>
<feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"></feblend>
</filter>
</defs>
</svg>

      Like
    </div>
      <div class="keyboard-button-container">
        <img class="keyboard-button-icon" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/keyboard-esc-774c88a8f50bb408cc315649b46aec3adb6a7ed573d28052b8219171c9441ab5.png" alt="Keyboard esc">
        Close
      </div>
  </div>

</div>

      </div>
    </div>

  <div data-v-4d07bdbe="" id="shot-sidebar-app" class="shot-sidebar-app"><div data-v-553ba33e="" data-v-4d07bdbe="" class="shot-actions-toolbar-wrapper"><div data-v-553ba33e="" class="shot-actions-toolbar"><div data-v-553ba33e="" name="shot-actions-toolbar-avatar" class="designer-hovercard" data-tippy=""> <span data-v-553ba33e="" class="profile-avatar"><a href="https://dribbble.com/aveef" title="" class="avatar-link"><div class="lazy-img-wrapper loaded avatar-image-wrapper avatar-medium"><img data-src="https://cdn.dribbble.com/users/124243/avatars/normal/8d25b91a17d31c2ab49566f80e6c2f72.jpg?1562051899&amp;compress=1&amp;resize=80x80" data-id="124243" alt="⭐️ Afif Bimantara&#39;s avatar" class="avatar-image lazyloaded" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/8d25b91a17d31c2ab49566f80e6c2f72(1).jpg"> <!----> <!----></div> <span class="accessibility-text">
      Link to ⭐️ Afif Bimantara's profile'
    </span></a></span></div> <div data-v-553ba33e="" class="shot-actions-toolbar-buttons"><button data-v-553ba33e="" class="btn-icon tertiary with-indicator comments-action" data-tippy="" data-original-title="Feedback"><svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" svg-inline="" role="presentation" focusable="false" tabindex="-1" class="icon-16"><path d="M10 0H6a5.998 5.998 0 00-1.333 11.846V16l4.666-4H10a6 6 0 100-12z" fill="currentColor"></path></svg> <span class="accessibility-text">
    View comments
  </span> <span class="count-indicator">
    33
  </span></button><button data-v-553ba33e="" class="btn-icon tertiary share-action" data-tippy="" data-original-title="Share"><svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" svg-inline="" role="presentation" focusable="false" tabindex="-1" class="icon-16"><path d="M0 12.782c0 .85.1 1.65.3 *********.35.45.5 0 1.05-2.65 2.75-5.15 5.55-5.65H8v2.2c0 1 .6 1.3 1.3.7l6.4-5.5c.35-.3.35-.8 0-1.15L9.3.332c-.7-.65-1.3-.3-1.3.65v2.35c-4.8.8-8 4.7-8 9.45z" fill="currentColor"></path></svg> <span class="accessibility-text">
    Share actions
  </span> <!----></button><button data-v-553ba33e="" class="btn-icon tertiary" data-tippy="" data-original-title="Shot details"><svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="24" height="24" svg-inline="" role="presentation" focusable="false" tabindex="-1" class="icon-16"><path d="M12 0C5.373 0 0 5.37 0 12c0 6.627 5.373 12 12 12s12-5.373 12-12c0-6.63-5.373-12-12-12zm1 18a1 1 0 01-2 0v-7a1 1 0 012 0zM12 8a1.5 1.5 0 11.001-3.001A1.5 1.5 0 0112 8z"></path></svg> <span class="accessibility-text">
    Detail actions
  </span> <!----></button></div> <!----></div></div> <div data-v-77407238="" data-v-4d07bdbe="" class="shot-sidebar" style="display: none;"><div data-v-77407238="" class="shot-sidebar-content"><!----> <div data-v-77407238="" infinite-scroll-distance="10" infinite-scroll-listen-for-event="infiniteScroll" class="sidebar-scrolling-container" style="display: none;"><!----></div></div></div></div>
</div>

<div id="boosted-shots-app"></div>



<script>
  // Initialize multi-shot
  Dribbble.MediaGallery.initialize();

  new Dribbble.VideoPlayer('.video-wrap video');


  // Move this logic into
  if (Dribbble.isMobile()) {
    $(".the-shot .single-img").swipe({
      fingers: 1,
      threshold: 200,
      swipeLeft: Screenshot.Page.next,
      swipeRight: Screenshot.Page.prev
    });
  }

  // Move this logic to gallery
    document.title = "Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble";

  // Move this logic to gallery
  Dribbble.Shots.add([{
    id: '14728277',
    ga: [["agensip","UA-120360070-3"],["aveef","UA-120360070-1"]]
  }])

    new Dribbble.Bucket({ target: "a[data-bucket-add]" });


  if (Dribbble.MediaSwap) {
    new Dribbble.MediaSwap('[data-video-teaser-small][data-video-teaser-large]');
  }
</script>



<div id="shot-header-contact-overlay" class="overlay contact-overlay message-overlay message-modal">
  <div class="lightbox">
    <a href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail#" class="close" aria-label="close">
          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25" fill="none" role="img" class="icon icon-24 lazyload">
<rect x="3" y="5" width="18" height="16" fill="white"></rect>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.5051 0.135254C14.9528 0.135152 17.3455 0.860879 19.3807 2.22066C21.416 3.58044 23.0022 5.51321 23.939 7.77453C24.8757 10.0359 25.1209 12.5242 24.6434 14.9248C24.166 17.3255 22.9873 19.5306 21.2566 21.2614C19.5259 22.9922 17.3207 24.1709 14.9201 24.6485C12.5195 25.126 10.0311 24.881 7.76979 23.9443C5.50843 23.0076 3.57561 21.4214 2.21576 19.3862C0.855913 17.3511 0.130107 14.9583 0.130127 12.5107C0.130141 9.22858 1.43392 6.08092 3.75467 3.76009C6.07542 1.43926 9.22304 0.135377 12.5051 0.135254V0.135254ZM7.13525 16.4156C6.9463 16.6101 6.84148 16.8711 6.84346 17.1423C6.84544 17.4135 6.95406 17.6729 7.14583 17.8647C7.3376 18.0564 7.59711 18.1649 7.86827 18.1668C8.13943 18.1687 8.40044 18.0638 8.59488 17.8748L12.4997 13.9699L16.4103 17.8805C16.6047 18.0698 16.8659 18.1749 17.1372 18.1731C17.4085 18.1712 17.6682 18.0626 17.8601 17.8707C18.0519 17.6788 18.1605 17.4191 18.1623 17.1478C18.164 16.8765 18.0589 16.6153 17.8695 16.421L13.9592 12.5106L17.8847 8.58516C18.072 8.39039 18.1754 8.12994 18.1727 7.85976C18.1701 7.58958 18.0616 7.33122 17.8705 7.14017C17.6795 6.94911 17.4211 6.8406 17.1509 6.83794C16.8808 6.83529 16.6203 6.93869 16.4255 7.12594L12.4997 11.051L8.57998 7.13125C8.38644 6.93769 8.12393 6.82894 7.8502 6.82892C7.57647 6.82891 7.31394 6.93763 7.12038 7.13118C6.92681 7.32472 6.81806 7.58724 6.81804 7.86097C6.81803 8.13469 6.92675 8.39722 7.1203 8.59078L11.0402 12.5106L7.13525 16.4156Z" fill="#979797"></path>
</svg>

    </a>
    <div class="display">
        <div class="processing">Loading…</div>
    </div>
  </div>
</div><div id="user-details-contact-overlay" class="overlay contact-overlay message-overlay message-modal">
  <div class="lightbox">
    <a href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail#" class="close" aria-label="close">
          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25" fill="none" role="img" class="icon icon-24 lazyload">
<rect x="3" y="5" width="18" height="16" fill="white"></rect>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.5051 0.135254C14.9528 0.135152 17.3455 0.860879 19.3807 2.22066C21.416 3.58044 23.0022 5.51321 23.939 7.77453C24.8757 10.0359 25.1209 12.5242 24.6434 14.9248C24.166 17.3255 22.9873 19.5306 21.2566 21.2614C19.5259 22.9922 17.3207 24.1709 14.9201 24.6485C12.5195 25.126 10.0311 24.881 7.76979 23.9443C5.50843 23.0076 3.57561 21.4214 2.21576 19.3862C0.855913 17.3511 0.130107 14.9583 0.130127 12.5107C0.130141 9.22858 1.43392 6.08092 3.75467 3.76009C6.07542 1.43926 9.22304 0.135377 12.5051 0.135254V0.135254ZM7.13525 16.4156C6.9463 16.6101 6.84148 16.8711 6.84346 17.1423C6.84544 17.4135 6.95406 17.6729 7.14583 17.8647C7.3376 18.0564 7.59711 18.1649 7.86827 18.1668C8.13943 18.1687 8.40044 18.0638 8.59488 17.8748L12.4997 13.9699L16.4103 17.8805C16.6047 18.0698 16.8659 18.1749 17.1372 18.1731C17.4085 18.1712 17.6682 18.0626 17.8601 17.8707C18.0519 17.6788 18.1605 17.4191 18.1623 17.1478C18.164 16.8765 18.0589 16.6153 17.8695 16.421L13.9592 12.5106L17.8847 8.58516C18.072 8.39039 18.1754 8.12994 18.1727 7.85976C18.1701 7.58958 18.0616 7.33122 17.8705 7.14017C17.6795 6.94911 17.4211 6.8406 17.1509 6.83794C16.8808 6.83529 16.6203 6.93869 16.4255 7.12594L12.4997 11.051L8.57998 7.13125C8.38644 6.93769 8.12393 6.82894 7.8502 6.82892C7.57647 6.82891 7.31394 6.93763 7.12038 7.13118C6.92681 7.32472 6.81806 7.58724 6.81804 7.86097C6.81803 8.13469 6.92675 8.39722 7.1203 8.59078L11.0402 12.5106L7.13525 16.4156Z" fill="#979797"></path>
</svg>

    </a>
    <div class="display">
        <div class="processing">Loading…</div>
    </div>
  </div>
</div>
</div>
    <div class="shot-nav-prev" style="display: none;">
      <div class="shot-nav-button-container">
        <a href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail#">
          <div class="shot-nav-button">
            <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current"><path d="m13.532 4.585-7.532 7.415 7.532 7.415c.792.779 2.081.779 2.873 0s.792-2.049 0-2.829l-4.659-4.586 4.659-4.587c.384-.378.595-.88.595-1.414s-.211-1.036-.595-1.414c-.792-.78-2.082-.78-2.873 0z"></path></svg>

          </div>
        </a>
        <span>Previous</span>
      </div>
    </div>
    <div class="shot-nav-next" style="">
      <div class="shot-nav-button-container">
        <a href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail#">
          <div class="shot-nav-button">
            <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" role="img" class="icon fill-current"><path d="m10.468 19.415 7.532-7.415-7.532-7.415c-.792-.779-2.081-.779-2.873 0s-.792 2.049 0 2.829l4.659 4.586-4.659 4.587c-.384.378-.595.88-.595 1.414s.211 1.036.595 1.414c.792.78 2.082.78 2.873 0z"></path></svg>

          </div>
        </a>
        <span>Next</span>
      </div>
    </div>
  <a href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail#" class="js-close-overlay close-overlay" aria-label="close">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 26 26" fill="none" role="img" class="icon fill-current">
<path d="M8.28596 6.51819C7.7978 6.03003 7.00634 6.03003 6.51819 6.51819C6.03003 7.00634 6.03003 7.7978 6.51819 8.28596L11.2322 13L6.51819 17.714C6.03003 18.2022 6.03003 18.9937 6.51819 19.4818C7.00634 19.97 7.7978 19.97 8.28596 19.4818L13 14.7678L17.714 19.4818C18.2022 19.97 18.9937 19.97 19.4818 19.4818C19.97 18.9937 19.97 18.2022 19.4818 17.714L14.7678 13L19.4818 8.28596C19.97 7.7978 19.97 7.00634 19.4818 6.51819C18.9937 6.03003 18.2022 6.03003 17.714 6.51819L13 11.2322L8.28596 6.51819Z" fill="currentColor"></path>
</svg>

  </a>
</div>

<div id="shot-modals-app" class="js-shot-modal-content"><!----> <!----> <!----></div>
<div id="good-modals-app"></div>


  <div class="attachment-overlay fill-screen lazyloading-hidden lazyload" style="display:none;" tabindex="-1" data-include="css:https://cdn.dribbble.com/assets/attachment-overlay-d331bf7ef824f4e1f1dca744b77052b5fa3bd4388b9725bfddf1f5d4703f934e.css">
  </div>

<div id="signup-overlay" class="overlay ">
  <div class="lightbox">
    <a href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail#" class="close" aria-label="close">
          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25" fill="none" role="img" class="icon icon-24 lazyloaded">
<rect x="3" y="5" width="18" height="16" fill="white"></rect>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.5051 0.135254C14.9528 0.135152 17.3455 0.860879 19.3807 2.22066C21.416 3.58044 23.0022 5.51321 23.939 7.77453C24.8757 10.0359 25.1209 12.5242 24.6434 14.9248C24.166 17.3255 22.9873 19.5306 21.2566 21.2614C19.5259 22.9922 17.3207 24.1709 14.9201 24.6485C12.5195 25.126 10.0311 24.881 7.76979 23.9443C5.50843 23.0076 3.57561 21.4214 2.21576 19.3862C0.855913 17.3511 0.130107 14.9583 0.130127 12.5107C0.130141 9.22858 1.43392 6.08092 3.75467 3.76009C6.07542 1.43926 9.22304 0.135377 12.5051 0.135254V0.135254ZM7.13525 16.4156C6.9463 16.6101 6.84148 16.8711 6.84346 17.1423C6.84544 17.4135 6.95406 17.6729 7.14583 17.8647C7.3376 18.0564 7.59711 18.1649 7.86827 18.1668C8.13943 18.1687 8.40044 18.0638 8.59488 17.8748L12.4997 13.9699L16.4103 17.8805C16.6047 18.0698 16.8659 18.1749 17.1372 18.1731C17.4085 18.1712 17.6682 18.0626 17.8601 17.8707C18.0519 17.6788 18.1605 17.4191 18.1623 17.1478C18.164 16.8765 18.0589 16.6153 17.8695 16.421L13.9592 12.5106L17.8847 8.58516C18.072 8.39039 18.1754 8.12994 18.1727 7.85976C18.1701 7.58958 18.0616 7.33122 17.8705 7.14017C17.6795 6.94911 17.4211 6.8406 17.1509 6.83794C16.8808 6.83529 16.6203 6.93869 16.4255 7.12594L12.4997 11.051L8.57998 7.13125C8.38644 6.93769 8.12393 6.82894 7.8502 6.82892C7.57647 6.82891 7.31394 6.93763 7.12038 7.13118C6.92681 7.32472 6.81806 7.58724 6.81804 7.86097C6.81803 8.13469 6.92675 8.39722 7.1203 8.59078L11.0402 12.5106L7.13525 16.4156Z" fill="#979797"></path>
</svg>

    </a>
    <div class="display">
        <div class="processing">Loading…</div>
    </div>
  </div>
</div>
  <div id="work-availability-modal-app"></div>
<div id="introductions-upload-modal-app"></div>

<div id="site-notifications"></div>

<div id="designer-application-modal-app"></div>


<script src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/jquery.min.js"></script>
<!-- jquery failover -->
<script>
  if (typeof jQuery == 'undefined') {
    document.write(unescape('%3Cscript src="https://cdn.dribbble.com/assets/jquery-3.2.1.min-fe436dfd579c3153d6ecfe2c7e893127c27ff1fe44f276d9f149535ba5e6f2c0.js">%3C/script>'))
  }
</script>




<script>
  // ensure namespace is available
  Dribbble = window.Dribbble || {};
  Dribbble.JsConfig = window.Dribbble.JsConfig || {};
  Dribbble.isHistorySupported = () => (window.history && 'pushState' in window.history);

  // add global constants
  DEVICE_WIDTH_BREAKPOINT = '800px';
  HIDPI_BREAKPOINT = '(-webkit-min-device-pixel-ratio: 1.5),(min--moz-device-pixel-ratio: 1.5),(-o-min-device-pixel-ratio: 3/2),(min-device-pixel-ratio: 1.5),(min-resolution: 1.5dppx)';

  User = {
    loggedIn: function() {
      return !!document.querySelector('body.logged-in')
    },
    loggedOut: function() {
      return !this.loggedIn()
    },
  };
</script>


  <script src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/global-ac421558d261844eedb5d9b3e2f952e1f12887c9d025b15c128093361f37857d.js"></script>



<script>
  var config = Dribbble.JsConfig || {}
  // the assign is so this partial can be called in other partials, e.g. modals
  // without overriding parent instantiations of JsConfig
  Dribbble.JsConfig = Object.assign(config, {
      user: {"isLoggedIn":false,"reCaptchaSiteKey":"6LdmBTIUAAAAAM4NIokaWu8p3BBuYEw3CxuDdyg_","canPurchasePro":true},
      hiringProfile: {},
      features: {"applePay":true,"braintreeHiring":false,"braintreePaymentButtons":true,"proSale2021":true,"courses50DiscountLink":true,"caseStudies":false,"proDiscountToasty":false,"caseStudiesAlpha":false,"marketplaceUploads":false,"directBillingCheckout":false,"clientSidePaymentMethods":false},
      isRobot: null,
      remoteIp: "**************",
      isProduction: true,
      permissions: {"userPermissions":["canSeeThirdPartyPaymentMethods"],"marketplacePermissions":[]},
      screenshot_boost: {"buttonText":[{"label":"Buy Now","value":"buy-now"},{"label":"Download","value":"download"},{"label":"Learn More","value":"learn-more"},{"label":"Shop Now","value":"shop-now"},{"label":"Apply Now","value":"apply-now"},{"label":"Try Now","value":"try-now"},{"label":"Get Offer","value":"get-offer"},{"label":"Contact Us","value":"contact-us"}],"tiers":{"lowTier":{"daysToServe":7,"range":{"lowerLimit":0,"upperLimit":10000}},"midTier":{"daysToServe":14,"range":{"lowerLimit":10001,"upperLimit":100000}},"highTier":{"daysToServe":28,"range":{"lowerLimit":100001,"upperLimit":null}}},"pricePerImpression":"0.006","purchase":{"stripePublicKey":"pk_live_9EfFSEE6iTCgHghKqBqnixxR","savedCreditCardInformation":null},"discount":null,"minimumImpressions":2000,"targetPlacements":{"following":"Following Feed","popular":"Popular Feed","search":"Search Feed","goods":"Goods Feed","recent":"New \u0026 Noteworthy Feed","shot_modal":"Shot Modal","similar_work":"Similar Work","tags":"Tag Feed","popular_first":"Popular Feed First"},"priorities":["self_serve","sales","remnant","sales_priority"]},
  })
</script>

<script src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/application-7dabc46252be529f4385.js"></script>
<script src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/shots-manifest-a12d5cfb43f75e81ddc69935a102c35aeab31e6aa06ddd33305fcd6c8cb0405b.js"></script>  <script>
    $(function() {

      Dribbble.Thumbnails.initialize({"enable_shot_overlay":true,"cursor":false,"deferredContent":null,"staticParams":null,"showBoostedAds":true,"showSimilarShots":true,"boostedAdPlacement":"Tag Feed","boostedAdParams":{"q":"find nearby places"},"numberOfAdsOnFirstPage":2,"numberOfAdsOnSubsequentPages":2,"boostedAdGroupPositions":[2,6]});


      if(Dribbble.Bucket){
        new Dribbble.Bucket({ target: "a[data-bucket-add]" });
      }


      $(document).on(
        'click',
        'a[data-fav-toggle=shot]',
        Screenshot.Like.Shot.toggle
      )
    });
  </script>
  <!-- Global site tag (gtag.js) -->
<script async="" src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/js(3)"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

    gtag('config', "UA-24041469-1"); // google analytics

    gtag('config', "AW-787470371"); // google ads
</script>

<script>
  Dribbble.Analytics.googleAnalyticsKey = "UA-24041469-1"
  if (window.location.pathname == "/") {
    Dribbble.Analytics.namedRoot = "/shots";
    Dribbble.Analytics.logDribbbleGAPageView("/")
  } else {
    Dribbble.Analytics.logDribbbleGAPageView("/tags/find_nearby_places")
  }

  Dribbble.Itly.pageViewed({ controller: "tags", action: "show"})
</script>





<div id="bucket-overlay" class="overlay"><div id="bucket-add" class="lightbox"><a href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail#" class="close"><img src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/close-circle-****************************************4a41ab9050aa700e77c367e9.svg" class=" ls-is-cached lazyloaded" style="width: 24px; height: 24px;"></a><div class="display"><div class="processing">Loading…</div></div></div></div><div id="credential_picker_container" style="position: fixed; z-index: 9999; height: 205px;"><iframe src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/select.html" title="Sign in with Google Dialog" style="height: 205px; width: 391px; overflow: hidden;"></iframe></div><div id="bucket-overlay" class="overlay"><div id="bucket-add" class="lightbox"><a href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail#" class="close"><img src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/close-circle-****************************************4a41ab9050aa700e77c367e9.svg" class=" ls-is-cached lazyloaded" style="width: 24px; height: 24px;"></a><div class="display"><div class="processing">Loading…</div></div></div></div><!----><div id="bucket-overlay" class="overlay"><div id="bucket-add" class="lightbox"><a href="https://dribbble.com/shots/14728277-Holeswing-Golf-Courses-List-and-Detail#" class="close"><img src="./Holeswing - Golf Courses List and Detail by ⭐️ Afif Bimantara for ⚡️Agensip UI UX Agency on Dribbble_files/close-circle-****************************************4a41ab9050aa700e77c367e9.svg" class=" ls-is-cached lazyloaded" style="width: 24px; height: 24px;"></a><div class="display"><div class="processing">Loading…</div></div></div></div></body></html>