var amplitude=function(){"use strict";function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function v(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},i=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),i.forEach(function(e){r(t,e,n[e])})}return t}var p="$default_instance",c=2,n=4096,a=1e3,g="$identify",m="$groupidentify",o="amp_cookie_test",l="amp",s="revenue_amount",u="$productId",d="$quantity",h="$price",f="$revenueType",y="amp_device_id",_="referrer",w="utm_source",b="utm_medium",I="utm_campaign",S="utm_term",k="utm_content",E="[Amplitude] Attribution Captured",O=function(e){for(var t="",n=0;n<e.length;n++){var i=e.charCodeAt(n);i<128?t+=String.fromCharCode(i):(127<i&&i<2048?t+=String.fromCharCode(i>>6|192):(t+=String.fromCharCode(i>>12|224),t+=String.fromCharCode(i>>6&63|128)),t+=String.fromCharCode(63&i|128))}return t},x=function(e){for(var t="",n=0,i=0,r=0,o=0;n<e.length;)(i=e.charCodeAt(n))<128?(t+=String.fromCharCode(i),n++):191<i&&i<224?(r=e.charCodeAt(n+1),t+=String.fromCharCode((31&i)<<6|63&r),n+=2):(r=e.charCodeAt(n+1),o=e.charCodeAt(n+2),t+=String.fromCharCode((15&i)<<12|(63&r)<<6|63&o),n+=3);return t},N={_keyStr:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",encode:function(e){try{if(window.btoa&&window.atob)return window.btoa(unescape(encodeURIComponent(e)))}catch(e){}return N._encode(e)},_encode:function(e){var t,n,i,r,o,s,a,u="",c=0;for(e=O(e);c<e.length;)r=(t=e.charCodeAt(c++))>>2,o=(3&t)<<4|(n=e.charCodeAt(c++))>>4,s=(15&n)<<2|(i=e.charCodeAt(c++))>>6,a=63&i,isNaN(n)?s=a=64:isNaN(i)&&(a=64),u=u+N._keyStr.charAt(r)+N._keyStr.charAt(o)+N._keyStr.charAt(s)+N._keyStr.charAt(a);return u},decode:function(e){try{if(window.btoa&&window.atob)return decodeURIComponent(escape(window.atob(e)))}catch(e){}return N._decode(e)},_decode:function(e){var t,n,i,r,o,s,a="",u=0;for(e=e.replace(/[^A-Za-z0-9\+\/\=]/g,"");u<e.length;)t=N._keyStr.indexOf(e.charAt(u++))<<2|(r=N._keyStr.indexOf(e.charAt(u++)))>>4,n=(15&r)<<4|(o=N._keyStr.indexOf(e.charAt(u++)))>>2,i=(3&o)<<6|(s=N._keyStr.indexOf(e.charAt(u++))),a+=String.fromCharCode(t),64!==o&&(a+=String.fromCharCode(n)),64!==s&&(a+=String.fromCharCode(i));return a=x(a)}},A=Object.prototype.toString;function C(e){switch(A.call(e)){case"[object Date]":return"date";case"[object RegExp]":return"regexp";case"[object Arguments]":return"arguments";case"[object Array]":return"array";case"[object Error]":return"error"}return null===e?"null":void 0===e?"undefined":e!=e?"nan":e&&1===e.nodeType?"element":"undefined"!=typeof Buffer&&"function"==typeof Buffer.isBuffer&&Buffer.isBuffer(e)?"buffer":t(e=e.valueOf?e.valueOf():Object.prototype.valueOf.apply(e))}var e,T={DISABLE:0,ERROR:1,WARN:2,INFO:3},P=T.WARN,R={error:function(e){P>=T.ERROR&&q(e)},warn:function(e){P>=T.WARN&&q(e)},info:function(e){P>=T.INFO&&q(e)}},q=function(e){try{console.log("[Amplitude] "+e)}catch(e){}},D=function(e){return"string"===C(e)&&e.length>n?e.substring(0,n):e},j=function(e){var t=C(e);if("object"!==t)return R.error("Error: invalid properties format. Expecting Javascript object, received "+t+", ignoring"),{};if(Object.keys(e).length>a)return R.error("Error: too many properties (more than 1000), ignoring"),{};var n={};for(var i in e)if(e.hasOwnProperty(i)){var r=i,o=C(r);"string"!==o&&(r=String(r),R.warn("WARNING: Non-string property key, received type "+o+', coercing to string "'+r+'"'));var s=K(r,e[i]);null!==s&&(n[r]=s)}return n},U=["nan","function","arguments","regexp","element"],K=function e(t,n){var i=C(n);if(-1!==U.indexOf(i))R.warn('WARNING: Property key "'+t+'" with invalid value type '+i+", ignoring"),n=null;else if("undefined"===i)n=null;else if("error"===i)n=String(n),R.warn('WARNING: Property key "'+t+'" with value type error, coercing to '+n);else if("array"===i){for(var r=[],o=0;o<n.length;o++){var s=n[o],a=C(s);"array"!==a?"object"===a?r.push(j(s)):r.push(e(t,s)):R.warn("WARNING: Cannot have "+a+" nested in an array property value, skipping")}n=r}else"object"===i&&(n=j(n));return n},M=function(e,t){var n=C(t);if("string"===n)return t;if("date"===n||"number"===n||"boolean"===n)return t=String(t),R.warn("WARNING: Non-string groupName, received type "+n+', coercing to string "'+t+'"'),t;if("array"===n){for(var i=[],r=0;r<t.length;r++){var o=t[r],s=C(o);"array"!==s&&"object"!==s?"string"===s?i.push(o):"date"!==s&&"number"!==s&&"boolean"!==s||(o=String(o),R.warn("WARNING: Non-string groupName, received type "+s+', coercing to string "'+o+'"'),i.push(o)):R.warn("WARNING: Skipping nested "+s+" in array groupName")}return i}R.warn("WARNING: Non-string groupName, received type "+n+". Please use strings or array of strings for groupName")},z=function(e){T.hasOwnProperty(e)&&(P=T[e])},B=R,G=function(e){return!e||0===e.length},V=function(e,t){e=e.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");var n=new RegExp("[\\?&]"+e+"=([^&#]*)").exec(t);return null===n?void 0:decodeURIComponent(n[1].replace(/\+/g," "))},F=function e(t){if("array"===C(t))for(var n=0;n<t.length;n++)t[n]=e(t[n]);else if("object"===C(t))for(var i in t)t.hasOwnProperty(i)&&(t[i]=e(t[i]));else t=D(t);return t},L=function(e){var t=C(e);if("object"!==t)return R.error("Error: invalid groups format. Expecting Javascript object, received "+t+", ignoring"),{};var n={};for(var i in e)if(e.hasOwnProperty(i)){var r=i,o=C(r);"string"!==o&&(r=String(r),R.warn("WARNING: Non-string groupType, received type "+o+', coercing to string "'+r+'"'));var s=M(r,e[i]);null!==s&&(n[r]=s)}return n},W=function(e,t,n){return C(e)===n||(R.error("Invalid "+t+" input type. Expected "+n+" but received "+C(e)),!1)},$=j,J=function(){return window.location},Q=function(){for(var e="",t=0;t<22;++t)e+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".charAt(Math.floor(64*Math.random()));return e},X=function(e){try{for(var t=document.cookie.split(";"),n=null,i=0;i<t.length;i++){for(var r=t[i];" "===r.charAt(0);)r=r.substring(1,r.length);if(0===r.indexOf(e)){n=r.substring(e.length,r.length);break}}return n}catch(e){return null}},H=function(e,t,n){var i=null!==t?n.expirationDays:-1;if(i){var r=new Date;r.setTime(r.getTime()+24*i*60*60*1e3),i=r}var o=e+"="+t;i&&(o+="; expires="+i.toUTCString()),o+="; path=/",n.domain&&(o+="; domain="+n.domain),n.secure&&(o+="; Secure"),n.sameSite&&(o+="; SameSite="+n.sameSite),document.cookie=o},Z=H,Y=X,ee=function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=String(new Date);try{var n=o+Q();H(n,t,e);var i=X(n+"=")===t;return H(n,null,e),i}catch(e){}return!1},te=function(e){var t=document.createElement("a");return t.href=e,t.hostname||location.hostname},ne=function(e){for(var t=te(e).split("."),n=[],i="_tldtest_"+Q(),r=t.length-2;0<=r;--r)n.push(t.slice(r).join("."));for(var o=0;o<n.length;++o){var s=n[o],a={domain:"."+s};if(Z(i,1,a),Y(i))return Z(i,null,a),s}return""},ie={expirationDays:void 0,domain:void 0},re=function(e){var t="";return ie.domain&&(t="."===ie.domain.charAt(0)?ie.domain.substring(1):ie.domain),e+t},oe=function(e){var t=re(e)+"=",n=Y(t);try{if(n)return JSON.parse(N.decode(n))}catch(e){return null}return null},se=function(e,t){try{return Z(re(e),N.encode(JSON.stringify(t)),ie),!0}catch(e){return!1}},ae=function(e){try{return Z(re(e),null,ie),!0}catch(e){return!1}},ue={reset:function(){ie={expirationDays:void 0,domain:void 0}},options:function(e){if(0===arguments.length)return ie;e=e||{},ie.expirationDays=e.expirationDays,ie.secure=e.secure,ie.sameSite=e.sameSite;var t=G(e.domain)?"."+ne(J().href):e.domain,n=Math.random();ie.domain=t,se("amplitude_test",n);var i=oe("amplitude_test");return i&&i===n||(t=null),ae("amplitude_test"),ie.domain=t,ie},get:oe,set:se,remove:ae,setRaw:function(e,t){try{return Z(re(e),t,ie),!0}catch(e){return!1}},getRaw:function(e){var t=re(e)+"=";return Y(t)}};if(function(){var e,t=new Date;try{return window.localStorage.setItem(t,t),e=window.localStorage.getItem(t)===String(t),window.localStorage.removeItem(t),e}catch(e){}return!1}())e=window.localStorage;else if(window.globalStorage)try{e=window.globalStorage[window.location.hostname]}catch(e){}else if("undefined"!=typeof document){var ce=document.createElement("div"),pe="localStorage";ce.style.display="none",document.getElementsByTagName("head")[0].appendChild(ce),ce.addBehavior&&(ce.addBehavior("#default#userdata"),e={length:0,setItem:function(e,t){ce.load(pe),ce.getAttribute(e)||this.length++,ce.setAttribute(e,t),ce.save(pe)},getItem:function(e){return ce.load(pe),ce.getAttribute(e)},removeItem:function(e){ce.load(pe),ce.getAttribute(e)&&this.length--,ce.removeAttribute(e),ce.save(pe)},clear:function(){ce.load(pe);for(var e,t=0;e=ce.XMLDocument.documentElement.attributes[t++];)ce.removeAttribute(e.name);ce.save(pe),this.length=0},key:function(e){return ce.load(pe),ce.XMLDocument.documentElement.attributes[e]}},ce.load(pe),e.length=ce.XMLDocument.documentElement.attributes.length)}e||(e={length:0,setItem:function(e,t){},getItem:function(e){},removeItem:function(e){},clear:function(){},key:function(e){}});var le=e,de=function(){this.storage=null};de.prototype.getStorage=function(){if(null!==this.storage)return this.storage;if(ee())this.storage=ue;else{var n="amp_cookiestore_";this.storage={_options:{expirationDays:void 0,domain:void 0,secure:!1},reset:function(){this._options={expirationDays:void 0,domain:void 0,secure:!1}},options:function(e){return 0===arguments.length?this._options:(e=e||{},this._options.expirationDays=e.expirationDays||this._options.expirationDays,this._options.domain=e.domain||this._options.domain||window&&window.location&&window.location.hostname,this._options.secure=e.secure||!1)},get:function(e){try{return JSON.parse(le.getItem(n+e))}catch(e){}return null},set:function(e,t){try{return le.setItem(n+e,JSON.stringify(t)),!0}catch(e){}return!1},remove:function(e){try{le.removeItem(n+e)}catch(e){return!1}}}}return this.storage};var he=function(){function u(e){var t=e.storageKey,n=e.disableCookies,i=e.domain,r=e.secure,o=e.sameSite,s=e.expirationDays;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),this.storageKey=t,this.domain=i,this.secure=r,this.sameSite=o,this.expirationDays=s,this.cookieDomain="";var a=ne(J().href);this.cookieDomain=i||(a?"."+a:null),this.disableCookieStorage=n||!ee({domain:this.cookieDomain,secure:this.secure,sameSite:this.sameSite,expirationDays:this.expirationDays})}var e,t,n;return e=u,(t=[{key:"getCookieStorageKey",value:function(){if(!this.domain)return this.storageKey;var e="."===this.domain.charAt(0)?this.domain.substring(1):this.domain;return"".concat(this.storageKey).concat(e?"_".concat(e):"")}},{key:"save",value:function(e){var t=e.deviceId,n=e.userId,i=e.optOut,r=e.sessionId,o=e.lastEventTime,s=e.eventId,a=e.identifyId,u=e.sequenceNumber,c=[t,N.encode(n||""),i?"1":"",r?r.toString(32):"0",o?o.toString(32):"0",s?s.toString(32):"0",a?a.toString(32):"0",u?u.toString(32):"0"].join(".");this.disableCookieStorage?le.setItem(this.storageKey,c):Z(this.getCookieStorageKey(),c,{domain:this.cookieDomain,secure:this.secure,sameSite:this.sameSite,expirationDays:this.expirationDays})}},{key:"load",value:function(){var e;if(this.disableCookieStorage||(e=Y(this.getCookieStorageKey()+"=")),e||(e=le.getItem(this.storageKey)),!e)return null;var t=e.split("."),n=null;if(t[1])try{n=N.decode(t[1])}catch(e){n=null}return{deviceId:t[0],userId:n,optOut:"1"===t[2],sessionId:parseInt(t[3],32),lastEventTime:parseInt(t[4],32),eventId:parseInt(t[5],32),identifyId:parseInt(t[6],32),sequenceNumber:parseInt(t[7],32)}}}])&&i(e.prototype,t),n&&i(e,n),u}(),fe="$clearAll",ve=function(){this.userPropertiesOperations={},this.properties=[]};ve.prototype.add=function(e,t){return"number"===C(t)||"string"===C(t)?this._addOperation("$add",e,t):B.error("Unsupported type for value: "+C(t)+", expecting number or string"),this},ve.prototype.append=function(e,t){return this._addOperation("$append",e,t),this},ve.prototype.clearAll=function(){return 0<Object.keys(this.userPropertiesOperations).length?this.userPropertiesOperations.hasOwnProperty(fe)||B.error("Need to send $clearAll on its own Identify object without any other operations, skipping $clearAll"):this.userPropertiesOperations[fe]="-",this},ve.prototype.prepend=function(e,t){return this._addOperation("$prepend",e,t),this},ve.prototype.set=function(e,t){return this._addOperation("$set",e,t),this},ve.prototype.setOnce=function(e,t){return this._addOperation("$setOnce",e,t),this},ve.prototype.unset=function(e){return this._addOperation("$unset",e,"-"),this},ve.prototype._addOperation=function(e,t,n){this.userPropertiesOperations.hasOwnProperty(fe)?B.error("This identify already contains a $clearAll operation, skipping operation "+e):-1===this.properties.indexOf(t)?(this.userPropertiesOperations.hasOwnProperty(e)||(this.userPropertiesOperations[e]={}),this.userPropertiesOperations[e][t]=n,this.properties.push(t)):B.error('User property "'+t+'" already used in this identify, skipping operation '+e)};var ge="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function me(e,t){return e(t={exports:{}},t.exports),t.exports}var ye=me(function(s){!function(e){function l(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function a(e,t,n,i,r,o){return l((s=l(l(t,e),l(i,o)))<<(a=r)|s>>>32-a,n);var s,a}function d(e,t,n,i,r,o,s){return a(t&n|~t&i,e,t,r,o,s)}function h(e,t,n,i,r,o,s){return a(t&i|n&~i,e,t,r,o,s)}function f(e,t,n,i,r,o,s){return a(t^n^i,e,t,r,o,s)}function v(e,t,n,i,r,o,s){return a(n^(t|~i),e,t,r,o,s)}function u(e,t){var n,i,r,o,s;e[t>>5]|=128<<t%32,e[14+(t+64>>>9<<4)]=t;var a=1732584193,u=-271733879,c=-1732584194,p=271733878;for(n=0;n<e.length;n+=16)u=v(u=v(u=v(u=v(u=f(u=f(u=f(u=f(u=h(u=h(u=h(u=h(u=d(u=d(u=d(u=d(r=u,c=d(o=c,p=d(s=p,a=d(i=a,u,c,p,e[n],7,-680876936),u,c,e[n+1],12,-389564586),a,u,e[n+2],17,606105819),p,a,e[n+3],22,-1044525330),c=d(c,p=d(p,a=d(a,u,c,p,e[n+4],7,-176418897),u,c,e[n+5],12,1200080426),a,u,e[n+6],17,-1473231341),p,a,e[n+7],22,-45705983),c=d(c,p=d(p,a=d(a,u,c,p,e[n+8],7,1770035416),u,c,e[n+9],12,-1958414417),a,u,e[n+10],17,-42063),p,a,e[n+11],22,-1990404162),c=d(c,p=d(p,a=d(a,u,c,p,e[n+12],7,1804603682),u,c,e[n+13],12,-40341101),a,u,e[n+14],17,-1502002290),p,a,e[n+15],22,1236535329),c=h(c,p=h(p,a=h(a,u,c,p,e[n+1],5,-165796510),u,c,e[n+6],9,-1069501632),a,u,e[n+11],14,643717713),p,a,e[n],20,-373897302),c=h(c,p=h(p,a=h(a,u,c,p,e[n+5],5,-701558691),u,c,e[n+10],9,38016083),a,u,e[n+15],14,-660478335),p,a,e[n+4],20,-405537848),c=h(c,p=h(p,a=h(a,u,c,p,e[n+9],5,568446438),u,c,e[n+14],9,-1019803690),a,u,e[n+3],14,-187363961),p,a,e[n+8],20,1163531501),c=h(c,p=h(p,a=h(a,u,c,p,e[n+13],5,-1444681467),u,c,e[n+2],9,-51403784),a,u,e[n+7],14,1735328473),p,a,e[n+12],20,-1926607734),c=f(c,p=f(p,a=f(a,u,c,p,e[n+5],4,-378558),u,c,e[n+8],11,-2022574463),a,u,e[n+11],16,1839030562),p,a,e[n+14],23,-35309556),c=f(c,p=f(p,a=f(a,u,c,p,e[n+1],4,-1530992060),u,c,e[n+4],11,1272893353),a,u,e[n+7],16,-155497632),p,a,e[n+10],23,-1094730640),c=f(c,p=f(p,a=f(a,u,c,p,e[n+13],4,681279174),u,c,e[n],11,-358537222),a,u,e[n+3],16,-722521979),p,a,e[n+6],23,76029189),c=f(c,p=f(p,a=f(a,u,c,p,e[n+9],4,-640364487),u,c,e[n+12],11,-421815835),a,u,e[n+15],16,530742520),p,a,e[n+2],23,-995338651),c=v(c,p=v(p,a=v(a,u,c,p,e[n],6,-198630844),u,c,e[n+7],10,1126891415),a,u,e[n+14],15,-1416354905),p,a,e[n+5],21,-57434055),c=v(c,p=v(p,a=v(a,u,c,p,e[n+12],6,1700485571),u,c,e[n+3],10,-1894986606),a,u,e[n+10],15,-1051523),p,a,e[n+1],21,-2054922799),c=v(c,p=v(p,a=v(a,u,c,p,e[n+8],6,1873313359),u,c,e[n+15],10,-30611744),a,u,e[n+6],15,-1560198380),p,a,e[n+13],21,1309151649),c=v(c,p=v(p,a=v(a,u,c,p,e[n+4],6,-145523070),u,c,e[n+11],10,-1120210379),a,u,e[n+2],15,718787259),p,a,e[n+9],21,-343485551),a=l(a,i),u=l(u,r),c=l(c,o),p=l(p,s);return[a,u,c,p]}function c(e){var t,n="",i=32*e.length;for(t=0;t<i;t+=8)n+=String.fromCharCode(e[t>>5]>>>t%32&255);return n}function p(e){var t,n=[];for(n[(e.length>>2)-1]=void 0,t=0;t<n.length;t+=1)n[t]=0;var i=8*e.length;for(t=0;t<i;t+=8)n[t>>5]|=(255&e.charCodeAt(t/8))<<t%32;return n}function i(e){var t,n,i="0123456789abcdef",r="";for(n=0;n<e.length;n+=1)t=e.charCodeAt(n),r+=i.charAt(t>>>4&15)+i.charAt(15&t);return r}function n(e){return unescape(encodeURIComponent(e))}function r(e){return c(u(p(t=n(e)),8*t.length));var t}function o(e,t){return function(e,t){var n,i,r=p(e),o=[],s=[];for(o[15]=s[15]=void 0,16<r.length&&(r=u(r,8*e.length)),n=0;n<16;n+=1)o[n]=909522486^r[n],s[n]=1549556828^r[n];return i=u(o.concat(p(t)),512+8*t.length),c(u(s.concat(i),640))}(n(e),n(t))}function t(e,t,n){return t?n?o(t,e):i(o(t,e)):n?r(e):i(r(e))}s.exports?s.exports=t:e.md5=t}(ge)}),_e=function(e){return encodeURIComponent(e).replace(/[!'()*]/g,function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()})},we=Object.getOwnPropertySymbols,be=Object.prototype.hasOwnProperty,Ie=Object.prototype.propertyIsEnumerable;var Se=function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},n=0;n<10;n++)t["_"+String.fromCharCode(n)]=n;if("0123456789"!==Object.getOwnPropertyNames(t).map(function(e){return t[e]}).join(""))return!1;var i={};return"abcdefghijklmnopqrst".split("").forEach(function(e){i[e]=e}),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},i)).join("")}catch(e){return!1}}()?Object.assign:function(e,t){for(var n,i,r=function(e){if(null==e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}(e),o=1;o<arguments.length;o++){for(var s in n=Object(arguments[o]))be.call(n,s)&&(r[s]=n[s]);if(we){i=we(n);for(var a=0;a<i.length;a++)Ie.call(n,i[a])&&(r[i[a]]=n[i[a]])}}return r},ke="%[a-f0-9]{2}";new RegExp(ke,"gi"),new RegExp("("+ke+")+","gi");function Ee(e,t){return t.encode?t.strict?_e(e):encodeURIComponent(e):e}var Oe=function(i,r){!1===(r=Se({encode:!0,strict:!0,arrayFormat:"none"},r)).sort&&(r.sort=function(){});var o=function(i){switch(i.arrayFormat){case"index":return function(e,t,n){return null===t?[Ee(e,i),"[",n,"]"].join(""):[Ee(e,i),"[",Ee(n,i),"]=",Ee(t,i)].join("")};case"bracket":return function(e,t){return null===t?Ee(e,i):[Ee(e,i),"[]=",Ee(t,i)].join("")};default:return function(e,t){return null===t?Ee(e,i):[Ee(e,i),"=",Ee(t,i)].join("")}}}(r);return i?Object.keys(i).sort(r.sort).map(function(t){var e=i[t];if(void 0===e)return"";if(null===e)return Ee(t,r);if(Array.isArray(e)){var n=[];return e.slice().forEach(function(e){void 0!==e&&n.push(o(t,e,n.length))}),n.join("&")}return Ee(t,r)+"="+Ee(e,r)}).filter(function(e){return 0<e.length}).join("&"):""},xe=function(e,t){this.url=e,this.data=t||{}};xe.prototype.send=function(e){if(!!window.XDomainRequest){var t=new window.XDomainRequest;t.open("POST",this.url,!0),t.onload=function(){e(200,t.responseText)},t.onerror=function(){"Request Entity Too Large"===t.responseText?e(413,t.responseText):e(500,t.responseText)},t.ontimeout=function(){},t.onprogress=function(){},t.send(Oe(this.data))}else{var n=new XMLHttpRequest;n.open("POST",this.url,!0),n.onreadystatechange=function(){4===n.readyState&&e(n.status,n.responseText)},n.setRequestHeader("Content-Type","application/x-www-form-urlencoded; charset=UTF-8"),n.send(Oe(this.data))}};var Ne=function(){this._price=null,this._productId=null,this._quantity=1,this._revenueType=null,this._properties=null};Ne.prototype.setProductId=function(e){return"string"!==C(e)?B.error("Unsupported type for productId: "+C(e)+", expecting string"):G(e)?B.error("Invalid empty productId"):this._productId=e,this},Ne.prototype.setQuantity=function(e){return"number"!==C(e)?B.error("Unsupported type for quantity: "+C(e)+", expecting number"):this._quantity=parseInt(e),this},Ne.prototype.setPrice=function(e){return"number"!==C(e)?B.error("Unsupported type for price: "+C(e)+", expecting number"):this._price=e,this},Ne.prototype.setRevenueType=function(e){return"string"!==C(e)?B.error("Unsupported type for revenueType: "+C(e)+", expecting string"):this._revenueType=e,this},Ne.prototype.setEventProperties=function(e){return"object"!==C(e)?B.error("Unsupported type for eventProperties: "+C(e)+", expecting object"):this._properties=$(e),this},Ne.prototype._isValidRevenue=function(){return"number"===C(this._price)||(B.error("Invalid revenue, need to set price field"),!1)},Ne.prototype._toJSONObject=function(){var e="object"===C(this._properties)?this._properties:{};return null!==this._productId&&(e[u]=this._productId),null!==this._quantity&&(e[d]=this._quantity),null!==this._price&&(e[h]=this._price),null!==this._revenueType&&(e[f]=this._revenueType),e};var Ae,Ce=me(function(b,I){!function(r,l){var d="function",e="model",t="name",n="type",i="vendor",o="version",s="architecture",a="console",u="mobile",c="tablet",p="smarttv",h="wearable",f={extend:function(e,t){var n={};for(var i in e)t[i]&&t[i].length%2==0?n[i]=t[i].concat(e[i]):n[i]=e[i];return n},has:function(e,t){return"string"==typeof e&&-1!==t.toLowerCase().indexOf(e.toLowerCase())},lowerize:function(e){return e.toLowerCase()},major:function(e){return"string"==typeof e?e.replace(/[^\d\.]/g,"").split(".")[0]:l},trim:function(e){return e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}},v={rgx:function(e,t){for(var n,i,r,o,s,a,u=0;u<t.length&&!s;){var c=t[u],p=t[u+1];for(n=i=0;n<c.length&&!s;)if(s=c[n++].exec(e))for(r=0;r<p.length;r++)a=s[++i],"object"==typeof(o=p[r])&&0<o.length?2==o.length?typeof o[1]==d?this[o[0]]=o[1].call(this,a):this[o[0]]=o[1]:3==o.length?typeof o[1]!==d||o[1].exec&&o[1].test?this[o[0]]=a?a.replace(o[1],o[2]):l:this[o[0]]=a?o[1].call(this,a,o[2]):l:4==o.length&&(this[o[0]]=a?o[3].call(this,a.replace(o[1],o[2])):l):this[o]=a||l;u+=2}},str:function(e,t){for(var n in t)if("object"==typeof t[n]&&0<t[n].length){for(var i=0;i<t[n].length;i++)if(f.has(t[n][i],e))return"?"===n?l:n}else if(f.has(t[n],e))return"?"===n?l:n;return e}},g={browser:{oldsafari:{version:{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}}},device:{amazon:{model:{"Fire Phone":["SD","KF"]}},sprint:{model:{"Evo Shift 4G":"7373KT"},vendor:{HTC:"APA",Sprint:"Sprint"}}},os:{windows:{version:{ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"}}}},m={browser:[[/(opera\smini)\/([\w\.-]+)/i,/(opera\s[mobiletab]+).+version\/([\w\.-]+)/i,/(opera).+version\/([\w\.]+)/i,/(opera)[\/\s]+([\w\.]+)/i],[t,o],[/(opios)[\/\s]+([\w\.]+)/i],[[t,"Opera Mini"],o],[/\s(opr)\/([\w\.]+)/i],[[t,"Opera"],o],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/\s]?([\w\.]*)/i,/(avant\s|iemobile|slim)(?:browser)?[\/\s]?([\w\.]*)/i,/(bidubrowser|baidubrowser)[\/\s]?([\w\.]+)/i,/(?:ms|\()(ie)\s([\w\.]+)/i,/(rekonq)\/([\w\.]*)/i,/(chromium|flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon)\/([\w\.-]+)/i],[t,o],[/(konqueror)\/([\w\.]+)/i],[[t,"Konqueror"],o],[/(trident).+rv[:\s]([\w\.]+).+like\sgecko/i],[[t,"IE"],o],[/(edge|edgios|edga|edg)\/((\d+)?[\w\.]+)/i],[[t,"Edge"],o],[/(yabrowser)\/([\w\.]+)/i],[[t,"Yandex"],o],[/(Avast)\/([\w\.]+)/i],[[t,"Avast Secure Browser"],o],[/(AVG)\/([\w\.]+)/i],[[t,"AVG Secure Browser"],o],[/(puffin)\/([\w\.]+)/i],[[t,"Puffin"],o],[/(focus)\/([\w\.]+)/i],[[t,"Firefox Focus"],o],[/(opt)\/([\w\.]+)/i],[[t,"Opera Touch"],o],[/((?:[\s\/])uc?\s?browser|(?:juc.+)ucweb)[\/\s]?([\w\.]+)/i],[[t,"UCBrowser"],o],[/(comodo_dragon)\/([\w\.]+)/i],[[t,/_/g," "],o],[/(windowswechat qbcore)\/([\w\.]+)/i],[[t,"WeChat(Win) Desktop"],o],[/(micromessenger)\/([\w\.]+)/i],[[t,"WeChat"],o],[/(brave)\/([\w\.]+)/i],[[t,"Brave"],o],[/(qqbrowserlite)\/([\w\.]+)/i],[t,o],[/(QQ)\/([\d\.]+)/i],[t,o],[/m?(qqbrowser)[\/\s]?([\w\.]+)/i],[t,o],[/(baiduboxapp)[\/\s]?([\w\.]+)/i],[t,o],[/(2345Explorer)[\/\s]?([\w\.]+)/i],[t,o],[/(MetaSr)[\/\s]?([\w\.]+)/i],[t],[/(LBBROWSER)/i],[t],[/xiaomi\/miuibrowser\/([\w\.]+)/i],[o,[t,"MIUI Browser"]],[/;fbav\/([\w\.]+);/i],[o,[t,"Facebook"]],[/safari\s(line)\/([\w\.]+)/i,/android.+(line)\/([\w\.]+)\/iab/i],[t,o],[/headlesschrome(?:\/([\w\.]+)|\s)/i],[o,[t,"Chrome Headless"]],[/\swv\).+(chrome)\/([\w\.]+)/i],[[t,/(.+)/,"$1 WebView"],o],[/((?:oculus|samsung)browser)\/([\w\.]+)/i],[[t,/(.+(?:g|us))(.+)/,"$1 $2"],o],[/((?:android.+)crmo|crios)\/([\w\.]+)/i,/android.+(chrome)\/([\w\.]+)\s+(?:mobile\s?safari)/i],[[t,"Chrome Mobile"],o],[/android.+version\/([\w\.]+)\s+(?:mobile\s?safari|safari)*/i],[o,[t,"Android Browser"]],[/(sailfishbrowser)\/([\w\.]+)/i],[[t,"Sailfish Browser"],o],[/(chrome|omniweb|arora|[tizenoka]{5}\s?browser)\/v?([\w\.]+)/i],[t,o],[/(dolfin)\/([\w\.]+)/i],[[t,"Dolphin"],o],[/(qihu|qhbrowser|qihoobrowser|360browser)/i],[[t,"360 Browser"]],[/(coast)\/([\w\.]+)/i],[[t,"Opera Coast"],o],[/fxios\/([\w\.-]+)/i],[o,[t,"Firefox"]],[/version\/([\w\.]+).+?mobile\/\w+\s(safari)/i],[o,[t,"Mobile Safari"]],[/version\/([\w\.]+).+?(mobile\s?safari|safari)/i],[o,t],[/webkit.+?(gsa)\/([\w\.]+).+?(mobile\s?safari|safari)(\/[\w\.]+)/i],[[t,"GSA"],o],[/webkit.+?(mobile\s?safari|safari)(\/[\w\.]+)/i],[t,[o,v.str,g.browser.oldsafari.version]],[/(webkit|khtml)\/([\w\.]+)/i],[t,o],[/(navigator|netscape)\/([\w\.-]+)/i],[[t,"Netscape"],o],[/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo\sbrowser|minimo|conkeror)[\/\s]?([\w\.\+]+)/i,/(firefox|seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([\w\.-]+)/i,/(mozilla)\/([\w\.]+).+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir)[\/\s]?([\w\.]+)/i,/(links)\s\(([\w\.]+)/i,/(gobrowser)\/?([\w\.]*)/i,/(ice\s?browser)\/v?([\w\._]+)/i,/(mosaic)[\/\s]([\w\.]+)/i],[t,o]],cpu:[[/(?:(amd|x(?:(?:86|64)[_-])?|wow|win)64)[;\)]/i],[[s,"amd64"]],[/(ia32(?=;))/i],[[s,f.lowerize]],[/((?:i[346]|x)86)[;\)]/i],[[s,"ia32"]],[/windows\s(ce|mobile);\sppc;/i],[[s,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?:\smac|;|\))/i],[[s,/ower/,"",f.lowerize]],[/(sun4\w)[;\)]/i],[[s,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|arm(?:64|(?=v\d+[;l]))|(?=atmel\s)avr|(?:irix|mips|sparc)(?:64)?(?=;)|pa-risc)/i],[[s,f.lowerize]]],device:[[/\((ipad|playbook);[\w\s\),;-]+(rim|apple)/i],[e,i,[n,c]],[/applecoremedia\/[\w\.]+ \((ipad)/],[e,[i,"Apple"],[n,c]],[/(apple\s{0,1}tv)/i],[[e,"Apple TV"],[i,"Apple"],[n,p]],[/(archos)\s(gamepad2?)/i,/(hp).+(touchpad)/i,/(hp).+(tablet)/i,/(kindle)\/([\w\.]+)/i,/\s(nook)[\w\s]+build\/(\w+)/i,/(dell)\s(strea[kpr\s\d]*[\dko])/i],[i,e,[n,c]],[/(kf[A-z]+)\sbuild\/.+silk\//i],[e,[i,"Amazon"],[n,c]],[/(sd|kf)[0349hijorstuw]+\sbuild\/.+silk\//i],[[e,v.str,g.device.amazon.model],[i,"Amazon"],[n,u]],[/android.+aft([bms])\sbuild/i],[e,[i,"Amazon"],[n,p]],[/\((ip[honed|\s\w*]+);.+(apple)/i],[e,i,[n,u]],[/\((ip[honed|\s\w*]+);/i],[e,[i,"Apple"],[n,u]],[/(blackberry)[\s-]?(\w+)/i,/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[\s_-]?([\w-]*)/i,/(hp)\s([\w\s]+\w)/i,/(asus)-?(\w+)/i],[i,e,[n,u]],[/\(bb10;\s(\w+)/i],[e,[i,"BlackBerry"],[n,u]],[/android.+(transfo[prime\s]{4,10}\s\w+|eeepc|slider\s\w+|nexus 7|padfone|p00c)/i],[e,[i,"Asus"],[n,c]],[/(sony)\s(tablet\s[ps])\sbuild\//i,/(sony)?(?:sgp.+)\sbuild\//i],[[i,"Sony"],[e,"Xperia Tablet"],[n,c]],[/android.+\s([c-g]\d{4}|so[-l]\w+)(?=\sbuild\/|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[e,[i,"Sony"],[n,u]],[/\s(ouya)\s/i,/(nintendo)\s([wids3u]+)/i],[i,e,[n,a]],[/android.+;\s(shield)\sbuild/i],[e,[i,"Nvidia"],[n,a]],[/(playstation\s[34portablevi]+)/i],[e,[i,"Sony"],[n,a]],[/(sprint\s(\w+))/i],[[i,v.str,g.device.sprint.vendor],[e,v.str,g.device.sprint.model],[n,u]],[/(htc)[;_\s-]+([\w\s]+(?=\)|\sbuild)|\w+)/i,/(zte)-(\w*)/i,/(alcatel|geeksphone|nexian|panasonic|(?=;\s)sony)[_\s-]?([\w-]*)/i],[i,[e,/_/g," "],[n,u]],[/(nexus\s9)/i],[e,[i,"HTC"],[n,c]],[/d\/huawei([\w\s-]+)[;\)]/i,/(nexus\s6p|vog-l29|ane-lx1|eml-l29)/i],[e,[i,"Huawei"],[n,u]],[/android.+(bah2?-a?[lw]\d{2})/i],[e,[i,"Huawei"],[n,c]],[/(microsoft);\s(lumia[\s\w]+)/i],[i,e,[n,u]],[/[\s\(;](xbox(?:\sone)?)[\s\);]/i],[e,[i,"Microsoft"],[n,a]],[/(kin\.[onetw]{3})/i],[[e,/\./g," "],[i,"Microsoft"],[n,u]],[/\s(milestone|droid(?:[2-4x]|\s(?:bionic|x2|pro|razr))?:?(\s4g)?)[\w\s]+build\//i,/mot[\s-]?(\w*)/i,/(XT\d{3,4}) build\//i,/(nexus\s6)/i],[e,[i,"Motorola"],[n,u]],[/android.+\s(mz60\d|xoom[\s2]{0,2})\sbuild\//i],[e,[i,"Motorola"],[n,c]],[/hbbtv\/\d+\.\d+\.\d+\s+\([\w\s]*;\s*(\w[^;]*);([^;]*)/i],[[i,f.trim],[e,f.trim],[n,p]],[/hbbtv.+maple;(\d+)/i],[[e,/^/,"SmartTV"],[i,"Samsung"],[n,p]],[/\(dtv[\);].+(aquos)/i],[e,[i,"Sharp"],[n,p]],[/android.+((sch-i[89]0\d|shw-m380s|gt-p\d{4}|gt-n\d+|sgh-t8[56]9|nexus 10))/i,/((SM-T\w+))/i],[[i,"Samsung"],e,[n,c]],[/smart-tv.+(samsung)/i],[i,[n,p],e],[/((s[cgp]h-\w+|gt-\w+|galaxy\snexus|sm-\w[\w\d]+))/i,/(sam[sung]*)[\s-]*(\w+-?[\w-]*)/i,/sec-((sgh\w+))/i],[[i,"Samsung"],e,[n,u]],[/sie-(\w*)/i],[e,[i,"Siemens"],[n,u]],[/(maemo|nokia).*(n900|lumia\s\d+)/i,/(nokia)[\s_-]?([\w-]*)/i],[[i,"Nokia"],e,[n,u]],[/android[x\d\.\s;]+\s([ab][1-7]\-?[0178a]\d\d?)/i],[e,[i,"Acer"],[n,c]],[/android.+([vl]k\-?\d{3})\s+build/i],[e,[i,"LG"],[n,c]],[/android\s3\.[\s\w;-]{10}(lg?)-([06cv9]{3,4})/i],[[i,"LG"],e,[n,c]],[/(lg) netcast\.tv/i],[i,e,[n,p]],[/(nexus\s[45])/i,/lg[e;\s\/-]+(\w*)/i,/android.+lg(\-?[\d\w]+)\s+build/i],[e,[i,"LG"],[n,u]],[/(lenovo)\s?(s(?:5000|6000)(?:[\w-]+)|tab(?:[\s\w]+))/i],[i,e,[n,c]],[/android.+(ideatab[a-z0-9\-\s]+)/i],[e,[i,"Lenovo"],[n,c]],[/(lenovo)[_\s-]?([\w-]+)/i],[i,e,[n,u]],[/linux;.+((jolla));/i],[i,e,[n,u]],[/((pebble))app\/[\d\.]+\s/i],[i,e,[n,h]],[/android.+;\s(oppo)\s?([\w\s]+)\sbuild/i],[i,e,[n,u]],[/crkey/i],[[e,"Chromecast"],[i,"Google"],[n,p]],[/android.+;\s(glass)\s\d/i],[e,[i,"Google"],[n,h]],[/android.+;\s(pixel c)[\s)]/i],[e,[i,"Google"],[n,c]],[/android.+;\s(pixel( [23])?( xl)?)[\s)]/i],[e,[i,"Google"],[n,u]],[/android.+;\s(\w+)\s+build\/hm\1/i,/android.+(hm[\s\-_]*note?[\s_]*(?:\d\w)?)\s+build/i,/android.+(mi[\s\-_]*(?:a\d|one|one[\s_]plus|note lte)?[\s_]*(?:\d?\w?)[\s_]*(?:plus)?)\s+build/i,/android.+(redmi[\s\-_]*(?:note)?(?:[\s_]*[\w\s]+))\s+build/i],[[e,/_/g," "],[i,"Xiaomi"],[n,u]],[/android.+(mi[\s\-_]*(?:pad)(?:[\s_]*[\w\s]+))\s+build/i],[[e,/_/g," "],[i,"Xiaomi"],[n,c]],[/android.+;\s(m[1-5]\snote)\sbuild/i],[e,[i,"Meizu"],[n,u]],[/(mz)-([\w-]{2,})/i],[[i,"Meizu"],e,[n,u]],[/android.+a000(1)\s+build/i,/android.+oneplus\s(a\d{4})[\s)]/i],[e,[i,"OnePlus"],[n,u]],[/android.+[;\/]\s*(RCT[\d\w]+)\s+build/i],[e,[i,"RCA"],[n,c]],[/android.+[;\/\s]+(Venue[\d\s]{2,7})\s+build/i],[e,[i,"Dell"],[n,c]],[/android.+[;\/]\s*(Q[T|M][\d\w]+)\s+build/i],[e,[i,"Verizon"],[n,c]],[/android.+[;\/]\s+(Barnes[&\s]+Noble\s+|BN[RT])(V?.*)\s+build/i],[[i,"Barnes & Noble"],e,[n,c]],[/android.+[;\/]\s+(TM\d{3}.*\b)\s+build/i],[e,[i,"NuVision"],[n,c]],[/android.+;\s(k88)\sbuild/i],[e,[i,"ZTE"],[n,c]],[/android.+[;\/]\s*(gen\d{3})\s+build.*49h/i],[e,[i,"Swiss"],[n,u]],[/android.+[;\/]\s*(zur\d{3})\s+build/i],[e,[i,"Swiss"],[n,c]],[/android.+[;\/]\s*((Zeki)?TB.*\b)\s+build/i],[e,[i,"Zeki"],[n,c]],[/(android).+[;\/]\s+([YR]\d{2})\s+build/i,/android.+[;\/]\s+(Dragon[\-\s]+Touch\s+|DT)(\w{5})\sbuild/i],[[i,"Dragon Touch"],e,[n,c]],[/android.+[;\/]\s*(NS-?\w{0,9})\sbuild/i],[e,[i,"Insignia"],[n,c]],[/android.+[;\/]\s*((NX|Next)-?\w{0,9})\s+build/i],[e,[i,"NextBook"],[n,c]],[/android.+[;\/]\s*(Xtreme\_)?(V(1[045]|2[015]|30|40|60|7[05]|90))\s+build/i],[[i,"Voice"],e,[n,u]],[/android.+[;\/]\s*(LVTEL\-)?(V1[12])\s+build/i],[[i,"LvTel"],e,[n,u]],[/android.+;\s(PH-1)\s/i],[e,[i,"Essential"],[n,u]],[/android.+[;\/]\s*(V(100MD|700NA|7011|917G).*\b)\s+build/i],[e,[i,"Envizen"],[n,c]],[/android.+[;\/]\s*(Le[\s\-]+Pan)[\s\-]+(\w{1,9})\s+build/i],[i,e,[n,c]],[/android.+[;\/]\s*(Trio[\s\-]*.*)\s+build/i],[e,[i,"MachSpeed"],[n,c]],[/android.+[;\/]\s*(Trinity)[\-\s]*(T\d{3})\s+build/i],[i,e,[n,c]],[/android.+[;\/]\s*TU_(1491)\s+build/i],[e,[i,"Rotor"],[n,c]],[/android.+(KS(.+))\s+build/i],[e,[i,"Amazon"],[n,c]],[/android.+(Gigaset)[\s\-]+(Q\w{1,9})\s+build/i],[i,e,[n,c]],[/\s(tablet|tab)[;\/]/i,/\s(mobile)(?:[;\/]|\ssafari)/i],[[n,f.lowerize],i,e],[/[\s\/\(](smart-?tv)[;\)]/i],[[n,p]],[/(android[\w\.\s\-]{0,9});.+build/i],[e,[i,"Generic"]]],engine:[[/windows.+\sedge\/([\w\.]+)/i],[o,[t,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[o,[t,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/(khtml|tasman|links)[\/\s]\(?([\w\.]+)/i,/(icab)[\/\s]([23]\.[\d\.]+)/i],[t,o],[/rv\:([\w\.]{1,9}).+(gecko)/i],[o,t]],os:[[/microsoft\s(windows)\s(vista|xp)/i],[t,o],[/(windows)\snt\s6\.2;\s(arm)/i,/(windows\sphone(?:\sos)*)[\s\/]?([\d\.\s\w]*)/i,/(windows\smobile|windows)[\s\/]?([ntce\d\.\s]+\w)/i],[[t,v.str,g.os.windows.name],[o,v.str,g.os.windows.version]],[/(win(?=3|9|n)|win\s9x\s)([nt\d\.]+)/i],[[t,"Windows"],[o,v.str,g.os.windows.version]],[/\((bb)(10);/i],[[t,"BlackBerry"],o],[/(blackberry)\w*\/?([\w\.]*)/i,/(tizen|kaios)[\/\s]([\w\.]+)/i,/(android|webos|palm\sos|qnx|bada|rim\stablet\sos|meego|sailfish|contiki)[\/\s-]?([\w\.]*)/i],[t,o],[/(symbian\s?os|symbos|s60(?=;))[\/\s-]?([\w\.]*)/i],[[t,"Symbian"],o],[/\((series40);/i],[t],[/mozilla.+\(mobile;.+gecko.+firefox/i],[[t,"Firefox OS"],o],[/(nintendo|playstation)\s([wids34portablevu]+)/i,/(mint)[\/\s\(]?(\w*)/i,/(mageia|vectorlinux)[;\s]/i,/(joli|[kxln]?ubuntu|debian|suse|opensuse|gentoo|(?=\s)arch|slackware|fedora|mandriva|centos|pclinuxos|redhat|zenwalk|linpus)[\/\s-]?(?!chrom)([\w\.-]*)/i,/(hurd|linux)\s?([\w\.]*)/i,/(gnu)\s?([\w\.]*)/i],[[t,"Linux"],o],[/(cros)\s[\w]+\s([\w\.]+\w)/i],[[t,"Chromium OS"],o],[/(sunos)\s?([\w\.\d]*)/i],[[t,"Solaris"],o],[/\s([frentopc-]{0,4}bsd|dragonfly)\s?([\w\.]*)/i],[[t,"Linux"],o],[/(iphone)(?:.*os\s*([\w]*)\slike\smac|;\sopera)/i],[[t,"iPhone"],[o,/_/g,"."]],[/(ipad)(?:.*os\s*([\w]*)\slike\smac|;\sopera)/i],[[t,"iPad"],[o,/_/g,"."]],[/(haiku)\s(\w+)/i],[t,o],[/cfnetwork\/.+darwin/i,/ip[honead]{2,4}(?:.*os\s([\w]+)\slike\smac|;\sopera)/i],[[o,/_/g,"."],[t,"iOS"]],[/(mac\sos\sx)\s?([\w\s\.]*)/i,/(macintosh|mac(?=_powerpc)\s)/i],[[t,"Mac"],[o,/_/g,"."]],[/((?:open)?solaris)[\/\s-]?([\w\.]*)/i,/(aix)\s((\d)(?=\.|\)|\s)[\w\.])*/i,/(plan\s9|minix|beos|os\/2|amigaos|morphos|risc\sos|openvms|fuchsia)/i,/(unix)\s?([\w\.]*)/i],[t,o]]},y=function(e,t){if("object"==typeof e&&(t=e,e=l),!(this instanceof y))return new y(e,t).getResult();var n=e||(r&&r.navigator&&r.navigator.userAgent?r.navigator.userAgent:""),i=t?f.extend(m,t):m;return this.getBrowser=function(){var e={name:l,version:l};return v.rgx.call(e,n,i.browser),e.major=f.major(e.version),e},this.getCPU=function(){var e={architecture:l};return v.rgx.call(e,n,i.cpu),e},this.getDevice=function(){var e={vendor:l,model:l,type:l};return v.rgx.call(e,n,i.device),e},this.getEngine=function(){var e={name:l,version:l};return v.rgx.call(e,n,i.engine),e},this.getOS=function(){var e={name:l,version:l};return v.rgx.call(e,n,i.os),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=e,this},this};y.VERSION="0.7.21",y.BROWSER={NAME:t,MAJOR:"major",VERSION:o},y.CPU={ARCHITECTURE:s},y.DEVICE={MODEL:e,VENDOR:i,TYPE:n,CONSOLE:a,MOBILE:u,SMARTTV:p,TABLET:c,WEARABLE:h,EMBEDDED:"embedded"},y.ENGINE={NAME:t,VERSION:o},y.OS={NAME:t,VERSION:o},b.exports&&(I=b.exports=y),I.UAParser=y;var _=r&&(r.jQuery||r.Zepto);if(_&&!_.ua){var w=new y;_.ua=w.getResult(),_.ua.get=function(){return w.getUA()},_.ua.set=function(e){w.setUA(e);var t=w.getResult();for(var n in t)_.ua[n]=t[n]}}}("object"==typeof window?window:ge)}),Te=(Ce.UAParser,"7.2.1"),Pe={apiEndpoint:"api.amplitude.com",batchEvents:!1,cookieExpiration:3650,cookieName:"amplitude_id",sameSiteCookie:"Lax",cookieForceUpgrade:!1,deferInitialization:!1,disableCookies:!1,deviceIdFromUrlParam:!1,domain:"",eventUploadPeriodMillis:3e4,eventUploadThreshold:30,forceHttps:!0,includeGclid:!1,includeReferrer:!1,includeUtm:!1,language:function(){return navigator&&(navigator.languages&&navigator.languages[0]||navigator.language||navigator.userLanguage)||""}(),logLevel:"WARN",logAttributionCapturedEvent:!1,optOut:!1,onError:function(){},platform:"Web",savedMaxCount:1e3,saveEvents:!0,saveParamsReferrerOncePerSession:!0,secureCookie:!1,sessionTimeout:18e5,trackingOptions:{city:!0,country:!0,carrier:!0,device_manufacturer:!0,device_model:!0,dma:!0,ip_address:!0,language:!0,os_name:!0,os_version:!0,platform:!0,region:!0,version_name:!0},unsetParamsReferrerOnNewSession:!1,unsentKey:"amplitude_unsent",unsentIdentifyKey:"amplitude_unsent_identify",uploadBatchSize:100},Re=function(e){this._instanceName=G(e)?p:e.toLowerCase(),this._unsentEvents=[],this._unsentIdentifys=[],this._ua=new Ce(navigator.userAgent).getResult(),this.options=v({},Pe,{trackingOptions:v({},Pe.trackingOptions)}),this.cookieStorage=(new de).getStorage(),this._q=[],this._sending=!1,this._updateScheduled=!1,this._onInit=[],this._eventId=0,this._identifyId=0,this._lastEventTime=null,this._newSession=!1,this._sequenceNumber=0,this._sessionId=null,this._isInitialized=!1,this._userAgent=navigator&&navigator.userAgent||null};Re.prototype.Identify=ve,Re.prototype.Revenue=Ne,Re.prototype.init=function(e,i,r,t){var o=this;if("string"!==C(e)||G(e))B.error("Invalid apiKey. Please re-initialize with a valid apiKey");else try{De(this.options,r),this.options.cookieName!==Pe.cookieName&&B.warn("The cookieName option is deprecated. We will be ignoring it for newer cookies"),this.options.apiKey=e,this._storageSuffix="_"+e+(this._instanceName===p?"":"_"+this._instanceName),this._storageSuffixV5=e.slice(0,6),this._oldCookiename=this.options.cookieName+this._storageSuffix,this._unsentKey=this.options.unsentKey+this._storageSuffix,this._unsentIdentifyKey=this.options.unsentIdentifyKey+this._storageSuffix,this._cookieName=l+"_"+this._storageSuffixV5,this.cookieStorage.options({expirationDays:this.options.cookieExpiration,domain:this.options.domain,secure:this.options.secureCookie,sameSite:this.options.sameSiteCookie}),this._metadataStorage=new he({storageKey:this._cookieName,disableCookies:this.options.disableCookies,expirationDays:this.options.cookieExpiration,domain:this.options.domain,secure:this.options.secureCookie,sameSite:this.options.sameSiteCookie});var n=!!this.cookieStorage.get(this._oldCookiename),s=!!this._metadataStorage.load();this._useOldCookie=!s&&n&&!this.options.cookieForceUpgrade;var a=s||n;if(this.options.domain=this.cookieStorage.options().domain,this.options.deferInitialization&&!a)return void this._deferInitialization(e,i,r,t);"string"===C(this.options.logLevel)&&z(this.options.logLevel);var u=Ve(this);this._apiPropertiesTrackingOptions=0<Object.keys(u).length?{tracking_options:u}:{},this.options.cookieForceUpgrade&&n&&(s||Ue(this),this.cookieStorage.remove(this._oldCookiename)),je(this),this._pendingReadStorage=!0;var c=function(e){o.options.deviceId=o._getInitialDeviceId(r&&r.deviceId,e),o.options.userId="string"===C(i)&&!G(i)&&i||"number"===C(i)&&i.toString()||o.options.userId||null;var t=(new Date).getTime();(!o._sessionId||!o._lastEventTime||t-o._lastEventTime>o.options.sessionTimeout)&&(o.options.unsetParamsReferrerOnNewSession&&o._unsetUTMParams(),o._newSession=!0,o._sessionId=t,o.options.saveParamsReferrerOncePerSession&&o._trackParamsAndReferrer()),o.options.saveParamsReferrerOncePerSession||o._trackParamsAndReferrer(),o.options.saveEvents&&(qe(o._unsentEvents),qe(o._unsentIdentifys)),o._lastEventTime=t,Me(o),o._pendingReadStorage=!1,o._sendEventsIfReady();for(var n=0;n<o._onInit.length;n++)o._onInit[n](o);o._onInit=[],o._isInitialized=!0};this.options.saveEvents&&(this._unsentEvents=this._loadSavedUnsentEvents(this.options.unsentKey).map(function(e){return{event:e}}).concat(this._unsentEvents),this._unsentIdentifys=this._loadSavedUnsentEvents(this.options.unsentIdentifyKey).map(function(e){return{event:e}}).concat(this._unsentIdentifys)),c(),this.runQueuedFunctions(),"function"===C(t)&&t(this)}catch(e){B.error(e),this.options.onError(e)}},Re.prototype.deleteLowerLevelDomainCookies=function(){var e=te(),t=this.options.domain&&"."===this.options.domain[0]?this.options.domain.slice(1):this.options.domain;if(t&&e!==t&&new RegExp(t+"$").test(e)){for(var n=e.split("."),i=t.split("."),r=n.length;r>i.length;--r){var o=n.slice(n.length-r).join(".");Z(this._cookieName,null,{domain:"."+o})}Z(this._cookieName,null,{})}},Re.prototype._getInitialDeviceId=function(e,t){return e||(this.options.deviceIdFromUrlParam?this._getDeviceIdFromUrlParam(this._getUrlParams()):this.options.deviceId?this.options.deviceId:t||Q())};var qe=function(e){for(var t=0;t<e.length;t++){var n=e[t].event.user_properties,i=e[t].event.event_properties,r=e[t].event.groups;e[t].event.user_properties=$(n),e[t].event.event_properties=$(i),e[t].event.groups=L(r)}};Re.prototype._migrateUnsentEvents=function(e){var o=this;Promise.all([Ae.getItem(this.options.unsentKey),Ae.getItem(this.options.unsentIdentifyKey)]).then(function(e){if(o.options.saveEvents){var t=e[0],n=e[1],i=[],r=[];t&&(i.push(Ae.setItem(o.options.unsentKey+o._storageSuffix,JSON.stringify(t))),r.push(Ae.removeItem(o.options.unsentKey))),n&&(i.push(Ae.setItem(o.options.unsentIdentifyKey+o._storageSuffix,JSON.stringify(n))),r.push(Ae.removeItem(o.options.unsentIdentifyKey))),0<i.length&&Promise.all(i).then(function(){}).catch(function(e){o.options.onError(e)})}}).then(e).catch(function(e){o.options.onError(e)})},Re.prototype._trackParamsAndReferrer=function(){var e,t,n;if(this.options.includeUtm&&(e=this._initUtmData()),this.options.includeReferrer&&(t=this._saveReferrer(this._getReferrer())),this.options.includeGclid&&(n=this._saveGclid(this._getUrlParams())),this.options.logAttributionCapturedEvent){var i=v({},e,t,n);0<Object.keys(i).length&&this.logEvent(E,i)}};var De=function i(r,o){if("object"===C(o)){var e=function(e){if(r.hasOwnProperty(e)){var t=o[e],n=C(r[e]);W(t,e+" option",n)&&("boolean"===n?r[e]=!!t:"string"===n&&!G(t)||"number"===n&&0<t?r[e]=t:"object"===n&&i(r[e],t))}};for(var t in o)o.hasOwnProperty(t)&&e(t)}};Re.prototype.runQueuedFunctions=function(){var e=this._q;this._q=[];for(var t=0;t<e.length;t++){var n=this[e[t][0]];"function"===C(n)&&n.apply(this,e[t].slice(1))}},Re.prototype._apiKeySet=function(e){return!G(this.options.apiKey)||(B.error("Invalid apiKey. Please set a valid apiKey with init() before calling "+e),!1)},Re.prototype._loadSavedUnsentEvents=function(e){var t=this._getFromStorage(le,e),n=this._parseSavedUnsentEventsString(t,e);return this._setInStorage(le,e,JSON.stringify(n)),n},Re.prototype._parseSavedUnsentEventsString=function(e,t){if(G(e))return[];if("string"===C(e))try{var n=JSON.parse(e);if("array"===C(n))return n}catch(e){}return B.error("Unable to load "+t+" events. Restart with a new empty queue."),[]},Re.prototype.isNewSession=function(){return this._newSession},Re.prototype.onInit=function(e){this._isInitialized?e(this):this._onInit.push(e)},Re.prototype.getSessionId=function(){return this._sessionId},Re.prototype.nextEventId=function(){return this._eventId++,this._eventId},Re.prototype.nextIdentifyId=function(){return this._identifyId++,this._identifyId},Re.prototype.nextSequenceNumber=function(){return this._sequenceNumber++,this._sequenceNumber},Re.prototype._unsentCount=function(){return this._unsentEvents.length+this._unsentIdentifys.length},Re.prototype._sendEventsIfReady=function(){return 0!==this._unsentCount()&&(this.options.batchEvents?this._unsentCount()>=this.options.eventUploadThreshold?(this.sendEvents(),!0):(this._updateScheduled||(this._updateScheduled=!0,setTimeout(function(){this._updateScheduled=!1,this.sendEvents()}.bind(this),this.options.eventUploadPeriodMillis)),!1):(this.sendEvents(),!0))},Re.prototype._getFromStorage=function(e,t){return e.getItem(t+this._storageSuffix)},Re.prototype._setInStorage=function(e,t,n){e.setItem(t+this._storageSuffix,n)};var je=function(e){if(e._useOldCookie){var t=e.cookieStorage.get(e._oldCookiename);"object"!==C(t)||Ke(e,t)}else{var n=e._metadataStorage.load();"object"===C(n)&&Ke(e,n)}},Ue=function(e){var t=e.cookieStorage.get(e._oldCookiename);"object"===C(t)&&(Ke(e,t),Me(e))},Ke=function(e,t){t.deviceId&&(e.options.deviceId=t.deviceId),t.userId&&(e.options.userId=t.userId),null!==t.optOut&&void 0!==t.optOut&&!1!==t.optOut&&(e.options.optOut=t.optOut),t.sessionId&&(e._sessionId=parseInt(t.sessionId,10)),t.lastEventTime&&(e._lastEventTime=parseInt(t.lastEventTime,10)),t.eventId&&(e._eventId=parseInt(t.eventId,10)),t.identifyId&&(e._identifyId=parseInt(t.identifyId,10)),t.sequenceNumber&&(e._sequenceNumber=parseInt(t.sequenceNumber,10))},Me=function(e){var t={deviceId:e.options.deviceId,userId:e.options.userId,optOut:e.options.optOut,sessionId:e._sessionId,lastEventTime:e._lastEventTime,eventId:e._eventId,identifyId:e._identifyId,sequenceNumber:e._sequenceNumber};e._useOldCookie?e.cookieStorage.set(e.options.cookieName+e._storageSuffix,t):e._metadataStorage.save(t)};Re.prototype._initUtmData=function(e,t){e=e||this._getUrlParams(),t=t||this.cookieStorage.get("__utmz");var n,i,r,o,s,a,u,c,p,l,d,h=(i=e,r=(n=t)?"?"+n.split(".").slice(-1)[0].replace(/\|/g,"&"):"",s=(o=function(e,t,n,i){return V(e,t)||V(n,i)})(w,i,"utmcsr",r),a=o(b,i,"utmcmd",r),u=o(I,i,"utmccn",r),c=o(S,i,"utmctr",r),p=o(k,i,"utmcct",r),l={},(d=function(e,t){G(t)||(l[e]=t)})(w,s),d(b,a),d(I,u),d(S,c),d(k,p),l);return ze(this,h),h},Re.prototype._unsetUTMParams=function(){var e=new ve;e.unset(_),e.unset(w),e.unset(b),e.unset(I),e.unset(S),e.unset(k),this.identify(e)};var ze=function(e,t){if("object"===C(t)&&0!==Object.keys(t).length){var n=new ve;for(var i in t)t.hasOwnProperty(i)&&(n.setOnce("initial_"+i,t[i]),n.set(i,t[i]));e.identify(n)}};Re.prototype._getReferrer=function(){return document.referrer},Re.prototype._getUrlParams=function(){return location.search},Re.prototype._saveGclid=function(e){var t=V("gclid",e);if(!G(t)){var n={gclid:t};return ze(this,n),n}},Re.prototype._getDeviceIdFromUrlParam=function(e){return V(y,e)},Re.prototype._getReferringDomain=function(e){if(G(e))return null;var t=e.split("/");return 3<=t.length?t[2]:null},Re.prototype._saveReferrer=function(e){if(!G(e)){var t={referrer:e,referring_domain:this._getReferringDomain(e)};return ze(this,t),t}},Re.prototype.saveEvents=function(){try{var e=JSON.stringify(this._unsentEvents.map(function(e){return e.event}));this._setInStorage(le,this.options.unsentKey,e)}catch(e){}try{var t=JSON.stringify(this._unsentIdentifys.map(function(e){return e.event}));this._setInStorage(le,this.options.unsentIdentifyKey,t)}catch(e){}},Re.prototype.setDomain=function(e){if(this._shouldDeferCall())return this._q.push(["setDomain"].concat(Array.prototype.slice.call(arguments,0)));if(W(e,"domain","string"))try{this.cookieStorage.options({expirationDays:this.options.cookieExpiration,secure:this.options.secureCookie,domain:e,sameSite:this.options.sameSiteCookie}),this.options.domain=this.cookieStorage.options().domain,je(this),Me(this)}catch(e){B.error(e)}},Re.prototype.setUserId=function(e){if(this._shouldDeferCall())return this._q.push(["setUserId"].concat(Array.prototype.slice.call(arguments,0)));try{this.options.userId=null!=e&&""+e||null,Me(this)}catch(e){B.error(e)}},Re.prototype.setGroup=function(e,t){if(this._shouldDeferCall())return this._q.push(["setGroup"].concat(Array.prototype.slice.call(arguments,0)));if(this._apiKeySet("setGroup()")&&W(e,"groupType","string")&&!G(e)){var n={};n[e]=t;var i=(new ve).set(e,t);this._logEvent(g,null,null,i.userPropertiesOperations,n,null,null,null)}},Re.prototype.setOptOut=function(e){if(this._shouldDeferCall())return this._q.push(["setOptOut"].concat(Array.prototype.slice.call(arguments,0)));if(W(e,"enable","boolean"))try{this.options.optOut=e,Me(this)}catch(e){B.error(e)}},Re.prototype.setSessionId=function(e){if(W(e,"sessionId","number"))try{this._sessionId=e,Me(this)}catch(e){B.error(e)}},Re.prototype.resetSessionId=function(){this.setSessionId((new Date).getTime())},Re.prototype.regenerateDeviceId=function(){if(this._shouldDeferCall())return this._q.push(["regenerateDeviceId"].concat(Array.prototype.slice.call(arguments,0)));this.setDeviceId(Q())},Re.prototype.setDeviceId=function(e){if(this._shouldDeferCall())return this._q.push(["setDeviceId"].concat(Array.prototype.slice.call(arguments,0)));if(W(e,"deviceId","string"))try{G(e)||(this.options.deviceId=""+e,Me(this))}catch(e){B.error(e)}},Re.prototype.setUserProperties=function(e){if(this._shouldDeferCall())return this._q.push(["setUserProperties"].concat(Array.prototype.slice.call(arguments,0)));if(this._apiKeySet("setUserProperties()")&&W(e,"userProperties","object")){var t=F($(e));if(0!==Object.keys(t).length){var n=new ve;for(var i in t)t.hasOwnProperty(i)&&n.set(i,t[i]);this.identify(n)}}},Re.prototype.clearUserProperties=function(){if(this._shouldDeferCall())return this._q.push(["clearUserProperties"].concat(Array.prototype.slice.call(arguments,0)));if(this._apiKeySet("clearUserProperties()")){var e=new ve;e.clearAll(),this.identify(e)}};var Be=function(e,t){for(var n=0;n<t._q.length;n++){var i=e[t._q[n][0]];"function"===C(i)&&i.apply(e,t._q[n].slice(1))}return e};Re.prototype.identify=function(e,t){if(this._shouldDeferCall())return this._q.push(["identify"].concat(Array.prototype.slice.call(arguments,0)));if(this._apiKeySet("identify()"))if("object"===C(e)&&e.hasOwnProperty("_q")&&(e=Be(new ve,e)),e instanceof ve){if(0<Object.keys(e.userPropertiesOperations).length)return this._logEvent(g,null,null,e.userPropertiesOperations,null,null,null,t);"function"===C(t)&&t(0,"No request sent",{reason:"No user property operations"})}else B.error("Invalid identify input type. Expected Identify object but saw "+C(e)),"function"===C(t)&&t(0,"No request sent",{reason:"Invalid identify input type"});else"function"===C(t)&&t(0,"No request sent",{reason:"API key is not set"})},Re.prototype.groupIdentify=function(e,t,n,i){if(this._shouldDeferCall())return this._q.push(["groupIdentify"].concat(Array.prototype.slice.call(arguments,0)));if(this._apiKeySet("groupIdentify()"))if(W(e,"group_type","string")&&!G(e))if(null!=t)if("object"===C(n)&&n.hasOwnProperty("_q")&&(n=Be(new ve,n)),n instanceof ve){if(0<Object.keys(n.userPropertiesOperations).length)return this._logEvent(m,null,null,null,r({},e,t),n.userPropertiesOperations,null,i);"function"===C(i)&&i(0,"No request sent",{reason:"No group property operations"})}else B.error("Invalid identify input type. Expected Identify object but saw "+C(n)),"function"===C(i)&&i(0,"No request sent",{reason:"Invalid identify input type"});else"function"===C(i)&&i(0,"No request sent",{reason:"Invalid group name"});else"function"===C(i)&&i(0,"No request sent",{reason:"Invalid group type"});else"function"===C(i)&&i(0,"No request sent",{reason:"API key is not set"})},Re.prototype.setVersionName=function(e){if(this._shouldDeferCall())return this._q.push(["setVersionName"].concat(Array.prototype.slice.call(arguments,0)));W(e,"versionName","string")&&(this.options.versionName=e)},Re.prototype._logEvent=function(e,t,n,i,r,o,s,a){if(je(this),e)if(this.options.optOut)"function"===C(a)&&a(0,"No request sent",{reason:"optOut is set to true"});else try{var u;u=e===g||e===m?this.nextIdentifyId():this.nextEventId();var c=this.nextSequenceNumber(),p="number"===C(s)?s:(new Date).getTime();(!this._sessionId||!this._lastEventTime||p-this._lastEventTime>this.options.sessionTimeout)&&(this._sessionId=p),this._lastEventTime=p,Me(this);var l=this._ua.browser.name,d=this._ua.browser.major,h=this._ua.os.name;i=i||{},n=v({},n||{},v({},this._apiPropertiesTrackingOptions)),t=t||{},r=r||{},o=o||{};var f={device_id:this.options.deviceId,user_id:this.options.userId,timestamp:p,event_id:u,session_id:this._sessionId||-1,event_type:e,version_name:Ge(this,"version_name")&&this.options.versionName||null,platform:Ge(this,"platform")?this.options.platform:null,os_name:Ge(this,"os_name")&&l||null,os_version:Ge(this,"os_version")&&d||null,device_model:Ge(this,"device_model")&&h||null,device_manufacturer:(Ge(this,"device_manufacturer"),null),language:Ge(this,"language")?this.options.language:null,carrier:(Ge(this,"carrier"),null),api_properties:n,event_properties:F($(t)),user_properties:F($(i)),uuid:function e(t){return t?(t^16*Math.random()>>t/4).toString(16):([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,e)}(),library:{name:"amplitude-js",version:Te},sequence_number:c,groups:F(L(r)),group_properties:F($(o)),user_agent:this._userAgent};return e===g||e===m?(this._unsentIdentifys.push({event:f,callback:a}),this._limitEventsQueued(this._unsentIdentifys)):(this._unsentEvents.push({event:f,callback:a}),this._limitEventsQueued(this._unsentEvents)),this.options.saveEvents&&this.saveEvents(),this._sendEventsIfReady(a),u}catch(e){B.error(e)}else"function"===C(a)&&a(0,"No request sent",{reason:"Missing eventType"})};var Ge=function(e,t){return!!e.options.trackingOptions[t]},Ve=function(e){for(var t=["city","country","dma","ip_address","region"],n={},i=0;i<t.length;i++){var r=t[i];Ge(e,r)||(n[r]=!1)}return n};Re.prototype._limitEventsQueued=function(e){e.length>this.options.savedMaxCount&&e.splice(0,e.length-this.options.savedMaxCount)},Re.prototype.logEvent=function(e,t,n){return this._shouldDeferCall()?this._q.push(["logEvent"].concat(Array.prototype.slice.call(arguments,0))):this.logEventWithTimestamp(e,t,null,n)},Re.prototype.logEventWithTimestamp=function(e,t,n,i){return this._shouldDeferCall()?this._q.push(["logEventWithTimestamp"].concat(Array.prototype.slice.call(arguments,0))):this._apiKeySet("logEvent()")?W(e,"eventType","string")?G(e)?("function"===C(i)&&i(0,"No request sent",{reason:"Missing eventType"}),-1):this._logEvent(e,t,null,null,null,null,n,i):("function"===C(i)&&i(0,"No request sent",{reason:"Invalid type for eventType"}),-1):("function"===C(i)&&i(0,"No request sent",{reason:"API key not set"}),-1)},Re.prototype.logEventWithGroups=function(e,t,n,i){return this._shouldDeferCall()?this._q.push(["logEventWithGroups"].concat(Array.prototype.slice.call(arguments,0))):this._apiKeySet("logEventWithGroups()")?W(e,"eventType","string")?this._logEvent(e,t,null,null,n,null,null,i):("function"===C(i)&&i(0,"No request sent",{reason:"Invalid type for eventType"}),-1):("function"===C(i)&&i(0,"No request sent",{reason:"API key not set"}),-1)};var Fe=function(e){return!isNaN(parseFloat(e))&&isFinite(e)};Re.prototype.logRevenueV2=function(e){if(this._shouldDeferCall())return this._q.push(["logRevenueV2"].concat(Array.prototype.slice.call(arguments,0)));if(this._apiKeySet("logRevenueV2()"))if("object"===C(e)&&e.hasOwnProperty("_q")&&(e=Be(new Ne,e)),e instanceof Ne){if(e&&e._isValidRevenue())return this.logEvent(s,e._toJSONObject())}else B.error("Invalid revenue input type. Expected Revenue object but saw "+C(e))},Re.prototype.logRevenue=function(e,t,n){return this._shouldDeferCall()?this._q.push(["logRevenue"].concat(Array.prototype.slice.call(arguments,0))):this._apiKeySet("logRevenue()")&&Fe(e)&&(void 0===t||Fe(t))?this._logEvent(s,{},{productId:n,special:"revenue_amount",quantity:t||1,price:e},null,null,null,null,null):-1},Re.prototype.removeEvents=function(e,t,n,i){Le(this,"_unsentEvents",e,n,i),Le(this,"_unsentIdentifys",t,n,i)};var Le=function(e,t,n,i,r){if(!(n<0)){for(var o=[],s=0;s<e[t].length;s++){var a=e[t][s];a.event.event_id>n?o.push(a):a.callback&&a.callback(i,r)}e[t]=o}};Re.prototype.sendEvents=function(){if(this._apiKeySet("sendEvents()")){if(this.options.optOut)this.removeEvents(1/0,1/0,0,"No request sent",{reason:"Opt out is set to true"});else if(0!==this._unsentCount()&&!this._sending){this._sending=!0;var e=(this.options.forceHttps?"https":"https:"===window.location.protocol?"https":"http")+"://"+this.options.apiEndpoint,n=Math.min(this._unsentCount(),this.options.uploadBatchSize),t=this._mergeEventsAndIdentifys(n),i=t.maxEventId,r=t.maxIdentifyId,o=JSON.stringify(t.eventsToSend.map(function(e){return e.event})),s=(new Date).getTime(),a={client:this.options.apiKey,e:o,v:c,upload_time:s,checksum:ye(c+this.options.apiKey+o+s)},u=this;new xe(e,a).send(function(e,t){u._sending=!1;try{200===e&&"success"===t?(u.removeEvents(i,r,e,t),u.options.saveEvents&&u.saveEvents(),u._sendEventsIfReady()):413===e&&(1===u.options.uploadBatchSize&&u.removeEvents(i,r,e,t),u.options.uploadBatchSize=Math.ceil(n/2),u.sendEvents())}catch(e){}})}}else this.removeEvents(1/0,1/0,0,"No request sent",{reason:"API key not set"})},Re.prototype._mergeEventsAndIdentifys=function(e){for(var t=[],n=0,i=-1,r=0,o=-1;t.length<e;){var s=void 0,a=r>=this._unsentIdentifys.length,u=n>=this._unsentEvents.length;if(u&&a){B.error("Merging Events and Identifys, less events and identifys than expected");break}a?i=(s=this._unsentEvents[n++]).event.event_id:u?o=(s=this._unsentIdentifys[r++]).event.event_id:!("sequence_number"in this._unsentEvents[n].event)||this._unsentEvents[n].event.sequence_number<this._unsentIdentifys[r].event.sequence_number?i=(s=this._unsentEvents[n++]).event.event_id:o=(s=this._unsentIdentifys[r++]).event.event_id,t.push(s)}return{eventsToSend:t,maxEventId:i,maxIdentifyId:o}},Re.prototype.setGlobalUserProperties=function(e){this.setUserProperties(e)},Re.prototype.__VERSION__=Te,Re.prototype._shouldDeferCall=function(){return this._pendingReadStorage||this._initializationDeferred},Re.prototype._deferInitialization=function(){this._initializationDeferred=!0,this._q.push(["init"].concat(Array.prototype.slice.call(arguments,0)))},Re.prototype.enableTracking=function(){this._initializationDeferred=!1,Me(this),this.runQueuedFunctions()};var We=function(){this.options=v({},Pe),this._q=[],this._instances={}};We.prototype.Identify=ve,We.prototype.Revenue=Ne,We.prototype.getInstance=function(e){e=G(e)?p:e.toLowerCase();var t=this._instances[e];return void 0===t&&(t=new Re(e),this._instances[e]=t),t},We.prototype.runQueuedFunctions=function(){for(var e=0;e<this._q.length;e++){var t=this[this._q[e][0]];"function"===C(t)&&t.apply(this,this._q[e].slice(1))}for(var n in this._q=[],this._instances)this._instances.hasOwnProperty(n)&&this._instances[n].runQueuedFunctions()},We.prototype.init=function(e,t,n,i){this.getInstance().init(e,t,n,function(e){this.options=e.options,"function"===C(i)&&i(e)}.bind(this))},We.prototype.isNewSession=function(){return this.getInstance().isNewSession()},We.prototype.getSessionId=function(){return this.getInstance().getSessionId()},We.prototype.nextEventId=function(){return this.getInstance().nextEventId()},We.prototype.nextIdentifyId=function(){return this.getInstance().nextIdentifyId()},We.prototype.nextSequenceNumber=function(){return this.getInstance().nextSequenceNumber()},We.prototype.saveEvents=function(){this.getInstance().saveEvents()},We.prototype.setDomain=function(e){this.getInstance().setDomain(e)},We.prototype.setUserId=function(e){this.getInstance().setUserId(e)},We.prototype.setGroup=function(e,t){this.getInstance().setGroup(e,t)},We.prototype.setOptOut=function(e){this.getInstance().setOptOut(e)},We.prototype.regenerateDeviceId=function(){this.getInstance().regenerateDeviceId()},We.prototype.setDeviceId=function(e){this.getInstance().setDeviceId(e)},We.prototype.setUserProperties=function(e){this.getInstance().setUserProperties(e)},We.prototype.clearUserProperties=function(){this.getInstance().clearUserProperties()},We.prototype.identify=function(e,t){this.getInstance().identify(e,t)},We.prototype.setVersionName=function(e){this.getInstance().setVersionName(e)},We.prototype.logEvent=function(e,t,n){return this.getInstance().logEvent(e,t,n)},We.prototype.logEventWithGroups=function(e,t,n,i){return this.getInstance().logEventWithGroups(e,t,n,i)},We.prototype.logRevenueV2=function(e){return this.getInstance().logRevenueV2(e)},We.prototype.logRevenue=function(e,t,n){return this.getInstance().logRevenue(e,t,n)},We.prototype.removeEvents=function(e,t){this.getInstance().removeEvents(e,t)},We.prototype.sendEvents=function(e){this.getInstance().sendEvents(e)},We.prototype.setGlobalUserProperties=function(e){this.getInstance().setUserProperties(e)},We.prototype.__VERSION__=Te;var $e=window.amplitude||{},Je=new We;for(var Qe in Je._q=$e._q||[],$e._iq)$e._iq.hasOwnProperty(Qe)&&(Je.getInstance(Qe)._q=$e._iq[Qe]._q||[]);return Je.runQueuedFunctions(),Je}();
