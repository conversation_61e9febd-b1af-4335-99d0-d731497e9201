function memberContainer(e){var t=$(e).closest("li.player, .floating-sidebar-container, .shot-byline .attribution, .js-hover-card .js-hover-card-follow, .hover-card .profile-head, .screenshot-meta, .overlay, .user-card .profile-head, .user-card-container .buttons-container, .profile-masthead, js-resume-card, .js-designer-card, .js-shot-header-action-links, .js-follow-prompt-container"),i=$("."+t.attr("class").match(/user-row-\d+/));return i.length?i:t}function numberWithDelimiter(e){return e.toString().replace(/(\d)(?=(\d\d\d)+(?!\d))/g,"$1,")}$D={addClass:function(e,t){"function"==typeof e.addClass?e.addClass(t):e.classList.add(t)},removeClass:function(e,t){"function"==typeof e.removeClass?e.removeClass(t):e.classList.remove(t)},hide:function(e){$D.addClass(e,"d-none")},show:function(e){$D.removeClass(e,"d-none")},hasClass:function(e,t){e.classList.contains(t)},toggleClass:function(e,t){e.classList.toggle(t)},setValue:function(e,t){"string"==typeof e?els=Array.prototype.slice.call(document.querySelectorAll(e)):Array.isArray(e)?els=e:els=Array.of(e),els.forEach(function(e){e.value=t})},createElement:function(e,t){t=t||{};var i=document.createElement(e);return t.text&&(i.textContent=t.text,delete t.text),t["class"]&&(t["class"].split(" ").forEach(function(e){i.classList.add(e)}),delete t["class"]),Object.keys(t).forEach(function(e){i.setAttribute(e,t[e])}),i}},function(){"use strict";var e=function(d,l){var c;d.rails!==l&&d.error("jquery-ujs has already been loaded!");var e=d(document);d.rails=c={linkClickSelector:"a[data-confirm], a[data-method], a[data-remote]:not([disabled]), a[data-disable-with], a[data-disable]",buttonClickSelector:"button[data-remote]:not([form]):not(form button), button[data-confirm]:not([form]):not(form button)",inputChangeSelector:"select[data-remote], input[data-remote], textarea[data-remote]",formSubmitSelector:"form",formInputClickSelector:"form input[type=submit], form input[type=image], form button[type=submit], form button:not([type]), input[type=submit][form], input[type=image][form], button[type=submit][form], button[form]:not([type])",disableSelector:"input[data-disable-with]:enabled, button[data-disable-with]:enabled, textarea[data-disable-with]:enabled, input[data-disable]:enabled, button[data-disable]:enabled, textarea[data-disable]:enabled",enableSelector:"input[data-disable-with]:disabled, button[data-disable-with]:disabled, textarea[data-disable-with]:disabled, input[data-disable]:disabled, button[data-disable]:disabled, textarea[data-disable]:disabled",requiredInputSelector:"input[name][required]:not([disabled]), textarea[name][required]:not([disabled])",fileInputSelector:"input[name][type=file]:not([disabled])",linkDisableSelector:"a[data-disable-with], a[data-disable]",buttonDisableSelector:"button[data-remote][data-disable-with], button[data-remote][data-disable]",csrfToken:function(){return d("meta[name=csrf-token]").attr("content")},csrfParam:function(){return d("meta[name=csrf-param]").attr("content")},CSRFProtection:function(e){var t=c.csrfToken();t&&e.setRequestHeader("X-CSRF-Token",t)},refreshCSRFTokens:function(){d('form input[name="'+c.csrfParam()+'"]').val(c.csrfToken())},fire:function(e,t,i){var n=d.Event(t);return e.trigger(n,i),!1!==n.result},confirm:function(e){return confirm(e)},ajax:function(e){return d.ajax(e)},href:function(e){return e[0].href},isRemote:function(e){return e.data("remote")!==l&&!1!==e.data("remote")},handleRemote:function(n){var e,t,i,r,a,o;if(c.fire(n,"ajax:before")){if(r=n.data("with-credentials")||null,a=n.data("type")||d.ajaxSettings&&d.ajaxSettings.dataType,n.is("form")){e=n.data("ujs:submit-button-formmethod")||n.attr("method"),t=n.data("ujs:submit-button-formaction")||n.attr("action"),i=d(n[0]).serializeArray();var s=n.data("ujs:submit-button");s&&(i.push(s),n.data("ujs:submit-button",null)),n.data("ujs:submit-button-formmethod",null),n.data("ujs:submit-button-formaction",null)}else n.is(c.inputChangeSelector)?(e=n.data("method"),t=n.data("url"),i=n.serialize(),n.data("params")&&(i=i+"&"+n.data("params"))):n.is(c.buttonClickSelector)?(e=n.data("method")||"get",t=n.data("url"),i=n.serialize(),n.data("params")&&(i=i+"&"+n.data("params"))):(e=n.data("method"),t=c.href(n),i=n.data("params")||null);return o={type:e||"GET",data:i,dataType:a,beforeSend:function(e,t){if(t.dataType===l&&e.setRequestHeader("accept","*/*;q=0.5, "+t.accepts.script),!c.fire(n,"ajax:beforeSend",[e,t]))return!1;n.trigger("ajax:send",e)},success:function(e,t,i){n.trigger("ajax:success",[e,t,i])},complete:function(e,t){n.trigger("ajax:complete",[e,t])},error:function(e,t,i){n.trigger("ajax:error",[e,t,i])},crossDomain:c.isCrossDomain(t)},r&&(o.xhrFields={withCredentials:r}),t&&(o.url=t),c.ajax(o)}return!1},isCrossDomain:function(e){var t=document.createElement("a");t.href=location.href;var i=document.createElement("a");try{return i.href=e,i.href=i.href,!((!i.protocol||":"===i.protocol)&&!i.host||t.protocol+"//"+t.host==i.protocol+"//"+i.host)}catch(n){return!0}},handleMethod:function(e){var t=c.href(e),i=e.data("method"),n=e.attr("target"),r=c.csrfToken(),a=c.csrfParam(),o=d('<form method="post" action="'+t+'"></form>'),s='<input name="_method" value="'+i+'" type="hidden" />';a===l||r===l||c.isCrossDomain(t)||(s+='<input name="'+a+'" value="'+r+'" type="hidden" />'),n&&o.attr("target",n),o.hide().append(s).appendTo("body"),o.trigger("submit")},formElements:function(e,t){return e.is("form")?d(e[0].elements).filter(t):e.find(t)},disableFormElements:function(e){c.formElements(e,c.disableSelector).each(function(){c.disableFormElement(d(this))})},disableFormElement:function(e){var t,i;t=e.is("button")?"html":"val",(i=e.data("disable-with"))!==l&&(e.data("ujs:enable-with",e[t]()),e[t](i)),e.prop("disabled",!0),e.data("ujs:disabled",!0)},enableFormElements:function(e){c.formElements(e,c.enableSelector).each(function(){c.enableFormElement(d(this))})},enableFormElement:function(e){var t=e.is("button")?"html":"val";e.data("ujs:enable-with")!==l&&(e[t](e.data("ujs:enable-with")),e.removeData("ujs:enable-with")),e.prop("disabled",!1),e.removeData("ujs:disabled")},allowAction:function(e){var t,i=e.data("confirm"),n=!1;if(!i)return!0;if(c.fire(e,"confirm")){try{n=c.confirm(i)}catch(r){(console.error||console.log).call(console,r.stack||r)}t=c.fire(e,"confirm:complete",[n])}return n&&t},blankInputs:function(e,t,i){var n,r,a,o=d(),s=t||"input,textarea",l=e.find(s),c={};return l.each(function(){(n=d(this)).is("input[type=radio]")?(a=n.attr("name"),c[a]||(0===e.find('input[type=radio]:checked[name="'+a+'"]').length&&(r=e.find('input[type=radio][name="'+a+'"]'),o=o.add(r)),c[a]=a)):(n.is("input[type=checkbox],input[type=radio]")?n.is(":checked"):!!n.val())===i&&(o=o.add(n))}),!!o.length&&o},nonBlankInputs:function(e,t){return c.blankInputs(e,t,!0)},stopEverything:function(e){return d(e.target).trigger("ujs:everythingStopped"),e.stopImmediatePropagation(),!1},disableElement:function(e){var t=e.data("disable-with");t!==l&&(e.data("ujs:enable-with",e.html()),e.html(t)),e.bind("click.railsDisable",function(e){return c.stopEverything(e)}),e.data("ujs:disabled",!0)},enableElement:function(e){e.data("ujs:enable-with")!==l&&(e.html(e.data("ujs:enable-with")),e.removeData("ujs:enable-with")),e.unbind("click.railsDisable"),e.removeData("ujs:disabled")}},c.fire(e,"rails:attachBindings")&&(d.ajaxPrefilter(function(e,t,i){e.crossDomain||c.CSRFProtection(i)}),d(window).on("pageshow.rails",function(){d(d.rails.enableSelector).each(function(){var e=d(this);e.data("ujs:disabled")&&d.rails.enableFormElement(e)}),d(d.rails.linkDisableSelector).each(function(){var e=d(this);e.data("ujs:disabled")&&d.rails.enableElement(e)})}),e.on("ajax:complete",c.linkDisableSelector,function(){c.enableElement(d(this))}),e.on("ajax:complete",c.buttonDisableSelector,function(){c.enableFormElement(d(this))}),e.on("click.rails",c.linkClickSelector,function(e){var t=d(this),i=t.data("method"),n=t.data("params"),r=e.metaKey||e.ctrlKey;if(!c.allowAction(t))return c.stopEverything(e);if(!r&&t.is(c.linkDisableSelector)&&c.disableElement(t),c.isRemote(t)){if(r&&(!i||"GET"===i)&&!n)return!0;var a=c.handleRemote(t);return!1===a?c.enableElement(t):a.fail(function(){c.enableElement(t)}),!1}return i?(c.handleMethod(t),!1):void 0}),e.on("click.rails",c.buttonClickSelector,function(e){var t=d(this);if(!c.allowAction(t)||!c.isRemote(t))return c.stopEverything(e);t.is(c.buttonDisableSelector)&&c.disableFormElement(t);var i=c.handleRemote(t);return!1===i?c.enableFormElement(t):i.fail(function(){c.enableFormElement(t)}),!1}),e.on("change.rails",c.inputChangeSelector,function(e){var t=d(this);return c.allowAction(t)&&c.isRemote(t)?(c.handleRemote(t),!1):c.stopEverything(e)}),e.on("submit.rails",c.formSubmitSelector,function(e){var t,i,n=d(this),r=c.isRemote(n);if(!c.allowAction(n))return c.stopEverything(e);if(n.attr("novalidate")===l)if(n.data("ujs:formnovalidate-button")===l){if((t=c.blankInputs(n,c.requiredInputSelector,!1))&&c.fire(n,"ajax:aborted:required",[t]))return c.stopEverything(e)}else n.data("ujs:formnovalidate-button",l);if(r){if(i=c.nonBlankInputs(n,c.fileInputSelector)){setTimeout(function(){c.disableFormElements(n)},13);var a=c.fire(n,"ajax:aborted:file",[i]);return a||setTimeout(function(){c.enableFormElements(n)},13),a}return c.handleRemote(n),!1}setTimeout(function(){c.disableFormElements(n)},13)}),e.on("click.rails",c.formInputClickSelector,function(e){var t=d(this);if(!c.allowAction(t))return c.stopEverything(e);var i=t.attr("name"),n=i?{name:i,value:t.val()}:null,r=t.closest("form");0===r.length&&(r=d("#"+t.attr("form"))),r.data("ujs:submit-button",n),r.data("ujs:formnovalidate-button",t.attr("formnovalidate")),r.data("ujs:submit-button-formaction",t.attr("formaction")),r.data("ujs:submit-button-formmethod",t.attr("formmethod"))}),e.on("ajax:send.rails",c.formSubmitSelector,function(e){this===e.target&&c.disableFormElements(d(this))}),e.on("ajax:complete.rails",c.formSubmitSelector,function(e){this===e.target&&c.enableFormElements(d(this))}),d(function(){c.refreshCSRFTokens()}))};window.jQuery?e(jQuery):"object"==typeof exports&&"object"==typeof module&&(module.exports=e)}(),function(i){"function"==typeof define&&define.amd?define(["jquery"],i):"object"==typeof module&&module.exports?module.exports=function(e,t){return void 0===t&&(t="undefined"!=typeof window?require("jquery"):require("jquery")(e)),i(t),t}:i(jQuery)}(function(F){"use strict";function a(e){var t=e.data;e.isDefaultPrevented()||(e.preventDefault(),F(e.target).closest("form").ajaxSubmit(t))}function o(e){var t=e.target,i=F(t);if(!i.is("[type=submit],[type=image]")){var n=i.closest("[type=submit]");if(0===n.length)return;t=n[0]}var r=t.form;if("image"===(r.clk=t).type)if("undefined"!=typeof e.offsetX)r.clk_x=e.offsetX,r.clk_y=e.offsetY;else if("function"==typeof F.fn.offset){var a=i.offset();r.clk_x=e.pageX-a.left,r.clk_y=e.pageY-a.top}else r.clk_x=e.pageX-t.offsetLeft,r.clk_y=e.pageY-t.offsetTop;setTimeout(function(){r.clk=r.clk_x=r.clk_y=null},100)}function H(){if(F.fn.ajaxSubmit.debug){var e="[jquery.form] "+Array.prototype.join.call(arguments,"");window.console&&window.console.log?window.console.log(e):window.opera&&window.opera.postError&&window.opera.postError(e)}}var f=/\r?\n/g,C={};C.fileapi=F('<input type="file">').get(0).files!==undefined,C.formdata="undefined"!=typeof window.FormData;var M=!!F.fn.prop;F.fn.attr2=function(){if(!M)return this.attr.apply(this,arguments);var e=this.prop.apply(this,arguments);return e&&e.jquery||"string"==typeof e?e:this.attr.apply(this,arguments)},F.fn.ajaxSubmit=function(O,e,t,i){function o(e){var t,i,n=F.param(e,O.traditional).split("&"),r=n.length,a=[];for(t=0;t<r;t++)n[t]=n[t].replace(/\+/g," "),i=n[t].split("="),a.push([decodeURIComponent(i[0]),decodeURIComponent(i[1])]);return a}function n(e){for(var i=new FormData,t=0;t<e.length;t++)i.append(e[t].name,e[t].value);if(O.extraData){var n=o(O.extraData);for(t=0;t<n.length;t++)n[t]&&i.append(n[t][0],n[t][1])}O.data=null;var r=F.extend(!0,{},F.ajaxSettings,O,{contentType:!1,processData:!1,cache:!1,type:L||"POST"});O.uploadProgress&&(r.xhr=function(){var e=F.ajaxSettings.xhr();return e.upload&&e.upload.addEventListener("progress",function(e){var t=0,i=e.loaded||e.position,n=e.total;e.lengthComputable&&(t=Math.ceil(i/n*100)),O.uploadProgress(e,i,n,t)},!1),e}),r.data=null;var a=r.beforeSend;return r.beforeSend=function(e,t){O.formData?t.data=O.formData:t.data=i,a&&a.call(this,e,t)},F.ajax(r)}function r(e){function u(e){var t=null;try{e.contentWindow&&(t=e.contentWindow.document)}catch(i){H("cannot get iframe.contentWindow document: "+i)}if(t)return t;try{t=e.contentDocument?e.contentDocument:e.document}catch(i){H("cannot get iframe.contentDocument: "+i),t=e.document}return t}function t(){function i(){try{var e=u(m).readyState;H("state = "+e),e&&"uninitialized"===e.toLowerCase()&&setTimeout(i,50)}catch(t){H("Server abort: ",t," (",t.name,")"),h(x),y&&clearTimeout(y),y=undefined}}var e=j.attr2("target"),t=j.attr2("action"),n="multipart/form-data",r=j.attr("enctype")||j.attr("encoding")||n;d.setAttribute("target",c),L&&!/post/i.test(L)||d.setAttribute("method","POST"),t!==f.url&&d.setAttribute("action",f.url),f.skipEncodingOverride||L&&!/post/i.test(L)||j.attr({encoding:"multipart/form-data",enctype:"multipart/form-data"}),f.timeout&&(y=setTimeout(function(){v=!0,h(D)},f.timeout));var a=[];try{if(f.extraData)for(var o in f.extraData)f.extraData.hasOwnProperty(o)&&(F.isPlainObject(f.extraData[o])&&f.extraData[o].hasOwnProperty("name")&&f.extraData[o].hasOwnProperty("value")?a.push(F('<input type="hidden" name="'+f.extraData[o].name+'">',S).val(f.extraData[o].value).appendTo(d)[0]):a.push(F('<input type="hidden" name="'+o+'">',S).val(f.extraData[o]).appendTo(d)[0]));f.iframeTarget||b.appendTo(T),m.attachEvent?m.attachEvent("onload",h):m.addEventListener("load",h,!1),setTimeout(i,15);try{d.submit()}catch(l){var s=document.createElement("form").submit;s.apply(d)}}finally{d.setAttribute("action",t),d.setAttribute("enctype",r),e?d.setAttribute("target",e):j.removeAttr("target"),F(a).remove()}}function h(e){if(!g.aborted&&!k){if((E=u(m))||(H("cannot access response document"),e=x),e===D&&g)return g.abort("timeout"),void w.reject(g,"timeout");if(e===x&&g)return g.abort("server abort"),void w.reject(g,"error","server abort");if(E&&E.location.href!==f.iframeSrc||v){m.detachEvent?m.detachEvent("onload",h):m.removeEventListener("load",h,!1);var t,i="success";try{if(v)throw"timeout";var n="xml"===f.dataType||E.XMLDocument||F.isXMLDoc(E);if(H("isXml="+n),!n&&window.opera&&(null===E.body||!E.body.innerHTML)&&--$)return H("requeing onLoad callback, DOM not available"),void setTimeout(h,250);var r=E.body?E.body:E.documentElement;g.responseText=r?r.innerHTML:null,g.responseXML=E.XMLDocument?E.XMLDocument:E,n&&(f.dataType="xml"),g.getResponseHeader=function(e){return{"content-type":f.dataType}[e.toLowerCase()]},r&&(g.status=Number(r.getAttribute("status"))||g.status,g.statusText=r.getAttribute("statusText")||g.statusText);var a=(f.dataType||"").toLowerCase(),o=/(json|script|text)/.test(a);if(o||f.textarea){var s=E.getElementsByTagName("textarea")[0];if(s)g.responseText=s.value,g.status=Number(s.getAttribute("status"))||g.status,g.statusText=s.getAttribute("statusText")||g.statusText;else if(o){var l=E.getElementsByTagName("pre")[0],c=E.getElementsByTagName("body")[0];l?g.responseText=l.textContent?l.textContent:l.innerText:c&&(g.responseText=c.textContent?c.textContent:c.innerText)}}else"xml"===a&&!g.responseXML&&g.responseText&&(g.responseXML=I(g.responseText));try{C=A(g,a,f)}catch(d){i="parsererror",g.error=t=d||i}}catch(d){H("error caught: ",d),i="error",g.error=t=d||i}g.aborted&&(H("upload aborted"),i=null),g.status&&(i=200<=g.status&&g.status<300||304===g.status?"success":"error"),"success"===i?(f.success&&f.success.call(f.context,C,"success",g),w.resolve(g.responseText,"success",g),p&&F.event.trigger("ajaxSuccess",[g,f])):i&&(void 0===t&&(t=g.statusText),f.error&&f.error.call(f.context,g,i,t),w.reject(g,"error",t),p&&F.event.trigger("ajaxError",[g,f,t])),p&&F.event.trigger("ajaxComplete",[g,f]),p&&!--F.active&&F.event.trigger("ajaxStop"),f.complete&&f.complete.call(f.context,g,i),k=!0,f.timeout&&clearTimeout(y),setTimeout(function(){f.iframeTarget?b.attr("src",f.iframeSrc):b.remove(),g.responseXML=null},100)}}}var i,n,f,p,c,b,m,g,r,a,v,y,d=j[0],w=F.Deferred();if(w.abort=function(e){g.abort(e)},e)for(n=0;n<P.length;n++)i=F(P[n]),M?i.prop("disabled",!1):i.removeAttr("disabled");(f=F.extend(!0,{},F.ajaxSettings,O)).context=f.context||f,c="jqFormIO"+(new Date).getTime();var S=d.ownerDocument,T=j.closest("body");if(f.iframeTarget?(a=(b=F(f.iframeTarget,S)).attr2("name"))?c=a:b.attr2("name",c):(b=F('<iframe name="'+c+'" src="'+f.iframeSrc+'" />',S)).css({position:"absolute",top:"-1000px",left:"-1000px"}),m=b[0],g={aborted:0,responseText:null,responseXML:null,status:0,statusText:"n/a",getAllResponseHeaders:function(){},getResponseHeader:function(){},setRequestHeader:function(){},abort:function(e){var t="timeout"===e?"timeout":"aborted";H("aborting upload... "+t),this.aborted=1;try{m.contentWindow.document.execCommand&&m.contentWindow.document.execCommand("Stop")}catch(i){}b.attr("src",f.iframeSrc),g.error=t,f.error&&f.error.call(f.context,g,t,e),p&&F.event.trigger("ajaxError",[g,f,t]),f.complete&&f.complete.call(f.context,g,t)}},(p=f.global)&&0==F.active++&&F.event.trigger("ajaxStart"),p&&F.event.trigger("ajaxSend",[g,f]),f.beforeSend&&!1===f.beforeSend.call(f.context,g,f))return f.global&&F.active--,w.reject(),w;if(g.aborted)return w.reject(),w;(r=d.clk)&&(a=r.name)&&!r.disabled&&(f.extraData=f.extraData||{},f.extraData[a]=r.value,"image"===r.type&&(f.extraData[a+".x"]=d.clk_x,f.extraData[a+".y"]=d.clk_y));var D=1,x=2,o=F("meta[name=csrf-token]").attr("content"),s=F("meta[name=csrf-param]").attr("content");s&&o&&(f.extraData=f.extraData||{},f.extraData[s]=o),f.forceSync?t():setTimeout(t,10);var C,E,k,$=50,I=F.parseXML||function(e,t){return window.ActiveXObject?((t=new ActiveXObject("Microsoft.XMLDOM")).async="false",t.loadXML(e)):t=(new DOMParser).parseFromString(e,"text/xml"),t&&t.documentElement&&"parsererror"!==t.documentElement.nodeName?t:null},l=F.parseJSON||function(e){return window.eval("("+e+")")},A=function(e,t,i){var n=e.getResponseHeader("content-type")||"",r=("xml"===t||!t)&&0<=n.indexOf("xml"),a=r?e.responseXML:e.responseText;return r&&"parsererror"===a.documentElement.nodeName&&F.error&&F.error("parsererror"),i&&i.dataFilter&&(a=i.dataFilter(a,t)),"string"==typeof a&&(("json"===t||!t)&&0<=n.indexOf("json")?a=l(a):("script"===t||!t)&&0<=n.indexOf("javascript")&&F.globalEval(a)),a};return w}if(!this.length)return H("ajaxSubmit: skipping submit process - no element selected"),this;var L,a,s,j=this;"function"==typeof O?O={success:O}:"string"==typeof O||!1===O&&0<arguments.length?(O={url:O,data:e,dataType:t},"function"==typeof i&&(O.success=i)):void 0===O&&(O={}),L=O.method||O.type||this.attr2("method"),(s=(s="string"==typeof(a=O.url||this.attr2("action"))?F.trim(a):"")||window.location.href||"")&&(s=(s.match(/^([^#]+)/)||[])[1]),O=F.extend(!0,{url:s,success:F.ajaxSettings.success,type:L||F.ajaxSettings.type,iframeSrc:/^https/i.test(window.location.href||"")?"javascript:false":"about:blank"},O);var l={};if(this.trigger("form-pre-serialize",[this,O,l]),l.veto)return H("ajaxSubmit: submit vetoed via form-pre-serialize trigger"),this;if(O.beforeSerialize&&!1===O.beforeSerialize(this,O))return H("ajaxSubmit: submit aborted via beforeSerialize callback"),this;var c=O.traditional;void 0===c&&(c=F.ajaxSettings.traditional);var d,P=[],u=this.formToArray(O.semantic,P,O.filtering);if(O.data){var h=F.isFunction(O.data)?O.data(u):O.data;O.extraData=h,d=F.param(h,c)}if(O.beforeSubmit&&!1===O.beforeSubmit(u,this,O))return H("ajaxSubmit: submit aborted via beforeSubmit callback"),this;if(this.trigger("form-submit-validate",[u,this,O,l]),l.veto)return H("ajaxSubmit: submit vetoed via form-submit-validate trigger"),this;var f=F.param(u,c);d&&(f=f?f+"&"+d:d),"GET"===O.type.toUpperCase()?(O.url+=(0<=O.url.indexOf("?")?"&":"?")+f,O.data=null):O.data=f;var p=[];if(O.resetForm&&p.push(function(){j.resetForm()}),O.clearForm&&p.push(function(){j.clearForm(O.includeHidden)}),!O.dataType&&O.target){var b=O.success||function(){};p.push(function(e,t,i){var n=arguments,r=O.replaceTarget?"replaceWith":"html";F(O.target)[r](e).each(function(){b.apply(this,n)})})}else O.success&&(F.isArray(O.success)?F.merge(p,O.success):p.push(O.success));if(O.success=function(e,t,i){for(var n=O.context||this,r=0,a=p.length;r<a;r++)p[r].apply(n,[e,t,i||j,j])},O.error){var m=O.error;O.error=function(e,t,i){var n=O.context||this;m.apply(n,[e,t,i,j])}}if(O.complete){var g=O.complete;O.complete=function(e,t){var i=O.context||this;g.apply(i,[e,t,j])}}var v=0<F("input[type=file]:enabled",this).filter(function(){return""!==F(this).val()}).length,y="multipart/form-data",w=j.attr("enctype")===y||j.attr("encoding")===y,S=C.fileapi&&C.formdata;H("fileAPI :"+S);var T,D=(v||w)&&!S;!1!==O.iframe&&(O.iframe||D)?O.closeKeepAlive?F.get(O.closeKeepAlive,function(){T=r(u)}):T=r(u):T=(v||w)&&S?n(u):F.ajax(O),j.removeData("jqxhr").data("jqxhr",T);for(var x=0;x<P.length;x++)P[x]=null;return this.trigger("form-submit-notify",[this,O]),this},F.fn.ajaxForm=function(e,t,i,n){if(("string"==typeof e||!1===e&&0<arguments.length)&&(e={url:e,data:t,dataType:i},"function"==typeof n&&(e.success=n)),(e=e||{}).delegation=e.delegation&&F.isFunction(F.fn.on),e.delegation||0!==this.length)return e.delegation?(F(document).off("submit.form-plugin",this.selector,a).off("click.form-plugin",this.selector,o).on("submit.form-plugin",this.selector,e,a).on("click.form-plugin",this.selector,e,o),this):this.ajaxFormUnbind().on("submit.form-plugin",e,a).on("click.form-plugin",e,o);var r={s:this.selector,c:this.context};return!F.isReady&&r.s?(H("DOM not ready, queuing ajaxForm"),F(function(){F(r.s,r.c).ajaxForm(e)})):H("terminating; zero elements found by selector"+(F.isReady?"":" (DOM not ready)")),this},F.fn.ajaxFormUnbind=function(){return this.off("submit.form-plugin click.form-plugin")},F.fn.formToArray=function(e,t,i){var n=[];if(0===this.length)return n;var r,a,o,s,l,c,d,u,h=this[0],f=this.attr("id"),p=e||"undefined"==typeof h.elements?h.getElementsByTagName("*"):h.elements;if(p&&(p=F.makeArray(p)),f&&(e||/(Edge|Trident)\//.test(navigator.userAgent))&&(r=F(':input[form="'+f+'"]').get()).length&&(p=(p||[]).concat(r)),!p||!p.length)return n;for(F.isFunction(i)&&(p=F.map(p,i)),a=0,d=p.length;a<d;a++)if((s=(c=p[a]).name)&&!c.disabled)if(e&&h.clk&&"image"===c.type)h.clk===c&&(n.push({name:s,value:F(c).val(),type:c.type}),n.push({name:s+".x",value:h.clk_x},{name:s+".y",value:h.clk_y}));else if((l=F.fieldValue(c,!0))&&l.constructor===Array)for(t&&t.push(c),o=0,u=l.length;o<u;o++)n.push({name:s,value:l[o]});else if(C.fileapi&&"file"===c.type){t&&t.push(c);var b=c.files;if(b.length)for(o=0;o<b.length;o++)n.push({name:s,value:b[o],type:c.type});else n.push({name:s,value:"",type:c.type})}else null!=l&&(t&&t.push(c),n.push({name:s,value:l,type:c.type,required:c.required}));if(!e&&h.clk){var m=F(h.clk),g=m[0];(s=g.name)&&!g.disabled&&"image"===g.type&&(n.push({name:s,value:m.val()}),n.push({name:s+".x",value:h.clk_x},{name:s+".y",value:h.clk_y}))}return n},F.fn.formSerialize=function(e){return F.param(this.formToArray(e))},F.fn.fieldSerialize=function(r){var a=[];return this.each(function(){var e=this.name;if(e){var t=F.fieldValue(this,r);if(t&&t.constructor===Array)for(var i=0,n=t.length;i<n;i++)a.push({name:e,value:t[i]});else null!=t&&a.push({name:this.name,value:t})}}),F.param(a)},F.fn.fieldValue=function(e){for(var t=[],i=0,n=this.length;i<n;i++){var r=this[i],a=F.fieldValue(r,e);null==a||a.constructor===Array&&!a.length||(a.constructor===Array?F.merge(t,a):t.push(a))}return t},F.fieldValue=function(e,t){var i=e.name,n=e.type,r=e.tagName.toLowerCase();if(void 0===t&&(t=!0),t&&(!i||e.disabled||"reset"===n||"button"===n||("checkbox"===n||"radio"===n)&&!e.checked||("submit"===n||"image"===n)&&e.form&&e.form.clk!==e||"select"===r&&-1===e.selectedIndex))return null;if("select"!==r)return F(e).val().replace(f,"\r\n");var a=e.selectedIndex;if(a<0)return null;for(var o=[],s=e.options,l="select-one"===n,c=l?a+1:s.length,d=l?a:0;d<c;d++){var u=s[d];if(u.selected&&!u.disabled){var h=u.value;if(h||(h=u.attributes&&u.attributes.value&&!u.attributes.value.specified?u.text:u.value),l)return h;o.push(h)}}return o},F.fn.clearForm=function(e){return this.each(function(){F("input,select,textarea",this).clearFields(e)})},F.fn.clearFields=F.fn.clearInputs=function(i){var n=/^(?:color|date|datetime|email|month|number|password|range|search|tel|text|time|url|week)$/i;return this.each(function(){var e=this.type,t=this.tagName.toLowerCase();n.test(e)||"textarea"===t?this.value="":"checkbox"===e||"radio"===e?this.checked=!1:"select"===t?this.selectedIndex=-1:"file"===e?/MSIE/.test(navigator.userAgent)?F(this).replaceWith(F(this).clone(!0)):F(this).val(""):i&&(!0===i&&/hidden/.test(e)||"string"==typeof i&&F(this).is(i))&&(this.value="")})},F.fn.resetForm=function(){return this.each(function(){var t=F(this),e=this.tagName.toLowerCase();switch(e){case"input":this.checked=this.defaultChecked;case"textarea":return this.value=this.defaultValue,!0;case"option":case"optgroup":var i=t.parents("select");return i.length&&i[0].multiple?"option"===e?this.selected=this.defaultSelected:t.find("option").resetForm():i.resetForm(),!0;case"select":return t.find("option").each(function(e){if(this.selected=this.defaultSelected,this.defaultSelected&&!t[0].multiple)return t[0].selectedIndex=e,!1}),!0;case"label":var n=F(t.attr("for")),r=t.find("input,select,textarea");return n[0]&&r.unshift(n[0]),r.resetForm(),!0;case"form":return("function"==typeof this.reset||"object"==typeof this.reset&&!this.reset.nodeType)&&this.reset(),!0;default:return t.find("form,input,label,select,textarea").resetForm(),!0}})},F.fn.enable=function(e){return void 0===e&&(e=!0),this.each(function(){this.disabled=!e})},F.fn.selected=function(i){return void 0===i&&(i=!0),this.each(function(){var e=this.type;if("checkbox"===e||"radio"===e)this.checked=i;else if("option"===this.tagName.toLowerCase()){var t=F(this).parent("select");i&&t[0]&&"select-one"===t[0].type&&t.find("option").selected(!1),this.selected=i}})},F.fn.ajaxSubmit.debug=!1}),function(s){function l(e,t){return"function"==typeof e?e.call(t):e}function t(e){for(;e=e.parentNode;)if(e==document)return!0;return!1}function c(e,t){this.$element=s(e),this.options=t,this.enabled=!0,this.fixTitle()}c.prototype={show:function(){var e=this.getTitle();if(e&&this.enabled){var t=this.tip();t.find(".tipsy-inner")[this.options.html?"html":"text"](e),t[0].className="tipsy",t.remove().css({top:0,left:0,visibility:"hidden",display:"block"}).prependTo(document.body);var i,n=s.extend({},this.$element.offset(),{width:this.$element[0].offsetWidth,height:this.$element[0].offsetHeight}),r=t[0].offsetWidth,a=t[0].offsetHeight,o=l(this.options.gravity,this.$element[0]);switch(o.charAt(0)){case"n":i={top:n.top+n.height+this.options.offset,left:n.left+n.width/2-r/2};break;case"s":i={top:n.top-a-this.options.offset,left:n.left+n.width/2-r/2};break;case"e":i={top:n.top+n.height/2-a/2,left:n.left-r-this.options.offset};break;case"w":i={top:n.top+n.height/2-a/2,left:n.left+n.width+this.options.offset}}2==o.length&&("w"==o.charAt(1)?i.left=n.left+n.width/2-15:i.left=n.left+n.width/2-r+15),t.css(i).addClass("tipsy-"+o),t.find(".tipsy-arrow")[0].className="tipsy-arrow tipsy-arrow-"+o.charAt(0),this.options.className&&t.addClass(l(this.options.className,this.$element[0])),this.options.fade?t.stop().css({opacity:0,display:"block",visibility:"visible"}).animate({opacity:this.options.opacity}):t.css({visibility:"visible",opacity:this.options.opacity})}},hide:function(){this.options.fade?this.tip().stop().fadeOut(function(){s(this).remove()}):this.tip().remove()},fixTitle:function(){var e=this.$element;(e.attr("title")||"string"!=typeof e.attr("original-title"))&&e.attr("original-title",e.attr("title")||"").removeAttr("title")},getTitle:function(){if(this.enabled){var e,t=this.$element,i=this.options;return this.fixTitle(),"string"==typeof(i=this.options).title?e=t.attr("title"==i.title?"original-title":i.title):"function"==typeof i.title&&(e=i.title.call(t[0])),(e=(""+e).replace(/(^\s*|\s*$)/,""))||i.fallback}},tip:function(){return this.$tip||(this.$tip=s('<div class="tipsy"></div>').html('<div class="tipsy-arrow"></div><div class="tipsy-inner"></div>'),this.$tip.data("tipsy-pointee",this.$element[0])),this.$tip},validate:function(){this.$element[0].parentNode||(this.hide(),this.$element=null,this.options=null)},enable:function(){this.enabled=!0},disable:function(){this.enabled=!1},toggleEnabled:function(){this.enabled=!this.enabled}},s.fn.tipsy=function(i){function t(e){var t=s.data(e,"tipsy");return t||(t=new c(e,s.fn.tipsy.elementOptions(e,i)),s.data(e,"tipsy",t)),t}function e(){var e=t(this);e.hoverState="in",0==i.delayIn?e.show():(e.fixTitle(),setTimeout(function(){"in"==e.hoverState&&e.show()},i.delayIn))}function n(){var e=t(this);e.hoverState="out",0==i.delayOut?e.hide():setTimeout(function(){"out"==e.hoverState&&e.hide()},i.delayOut)}if(!0===i)return this.data("tipsy");if("string"==typeof i){var r=this.data("tipsy");return r&&r[i](),this}if((i=s.extend({},s.fn.tipsy.defaults,i)).live||this.each(function(){t(this)}),"manual"!=i.trigger){var a="hover"==i.trigger?"mouseenter":"focus",o="hover"==i.trigger?"mouseleave":"blur";this.on(a,e).on(o,n)}return this},s.fn.tipsy.defaults={className:null,delayIn:0,delayOut:0,fade:!1,fallback:"",gravity:"n",html:!1,live:!1,offset:0,opacity:.8,title:"title",trigger:"hover"},s.fn.tipsy.revalidate=function(){s(".tipsy").each(function(){var e=s.data(this,"tipsy-pointee");e&&t(e)||s(this).remove()})},s.fn.tipsy.elementOptions=function(e,t){return s.metadata?s.extend({},t,s(e).metadata()):t},s.fn.tipsy.autoNS=function(){return s(this).offset().top>s(document).scrollTop()+s(window).height()/2?"s":"n"},s.fn.tipsy.autoWE=function(){return s(this).offset().left>s(document).scrollLeft()+s(window).width()/2?"e":"w"},s.fn.tipsy.autoBounds=function(r,a){return function(){var e={ns:a[0],ew:1<a.length&&a[1]},t=s(document).scrollTop()+r,i=s(document).scrollLeft()+r,n=s(this);return n.offset().top<t&&(e.ns="n"),n.offset().left<i&&(e.ew="w"),s(window).width()+s(document).scrollLeft()-n.offset().left<r&&(e.ew="e"),s(window).height()+s(document).scrollTop()-n.offset().top<r&&(e.ns="s"),e.ns+(e.ew?e.ew:"")}}}(jQuery),function(e){"function"==typeof define&&define.amd&&define.amd.jQuery?define(["jquery"],e):e(jQuery)}(function(Ae){"use strict";function n(i){return!i||i.allowPageScroll!==undefined||i.swipe===undefined&&i.swipeStatus===undefined||(i.allowPageScroll=Me),i.click!==undefined&&i.tap===undefined&&(i.tap=i.click),i||(i={}),i=Ae.extend({},Ae.fn.swipe.defaults,i),this.each(function(){var e=Ae(this),t=e.data(tt);t||(t=new r(this,i),e.data(tt,t))})}function r(e,s){function t(e){if(!(M()||0<Ae(e.target).closest(s.excludedElements,ve).length)){var t,i=e.originalEvent?e.originalEvent:e,n=i.touches,r=n?n[0]:i;return ye=Ge,n?we=n.length:e.preventDefault(),me=de=null,pe=1,be=fe=he=ue=ce=0,Se=U(),ge=X(),F(),!n||we===s.fingers||s.fingers===We||g()?(_(0,r),Te=ee(),2==we&&(_(1,n[1]),he=fe=G(Se[0].start,Se[1].start)),(s.swipeStatus||s.pinchStatus)&&(t=c(i,ye))):t=!1,!1===t?(c(i,ye=Ye),t):(s.hold&&($e=setTimeout(Ae.proxy(function(){ve.trigger("hold",[i.target]),s.hold&&(t=s.hold.call(ve,i,i.target))},this),s.longTapThreshold)),B(!0),null)}}function i(e){var t=e.originalEvent?e.originalEvent:e;if(ye!==Qe&&ye!==Ye&&!H()){var i,n=t.touches,r=N(n?n[0]:t);if(De=ee(),n&&(we=n.length),s.hold&&clearTimeout($e),ye=Ke,2==we&&(0==he?(_(1,n[1]),he=fe=G(Se[0].start,Se[1].start)):(N(n[1]),fe=G(Se[0].end,Se[1].end),me=Q(Se[0].end,Se[1].end)),pe=K(he,fe),be=Math.abs(he-fe)),we===s.fingers||s.fingers===We||!n||g()){if(b(e,de=Z(r.start,r.end)),ce=Y(r.start,r.end),ue=V(),z(de,ce),(s.swipeStatus||s.pinchStatus)&&(i=c(t,ye)),!s.triggerOnTouchEnd||s.triggerOnTouchLeave){var a=!0;if(s.triggerOnTouchLeave){var o=te(this);a=ie(r.end,o)}!s.triggerOnTouchEnd&&a?ye=l(Ke):s.triggerOnTouchLeave&&!a&&(ye=l(Qe)),ye!=Ye&&ye!=Qe||c(t,ye)}}else c(t,ye=Ye);!1===i&&c(t,ye=Ye)}}function n(e){var t=e.originalEvent?e.originalEvent:e,i=t.touches;return i&&i.length?(P(),!0):(H()&&(we=Ce),De=ee(),ue=V(),h()||!u()?c(t,ye=Ye):s.triggerOnTouchEnd||0==s.triggerOnTouchEnd&&ye===Ke?(e.preventDefault(),c(t,ye=Qe)):!s.triggerOnTouchEnd&&x()?d(t,ye=Qe,Re):ye===Ke&&c(t,ye=Ye),B(!1),null)}function r(){fe=he=Te=De=we=0,pe=1,F(),B(!1)}function a(e){var t=e.originalEvent?e.originalEvent:e
;s.triggerOnTouchLeave&&c(t,ye=l(Qe))}function o(){ve.unbind(re,t),ve.unbind(le,r),ve.unbind(ae,i),ve.unbind(oe,n),se&&ve.unbind(se,a),B(!1)}function l(e){var t=e,i=p(),n=u(),r=h();return!i||r?t=Ye:!n||e!=Ke||s.triggerOnTouchEnd&&!s.triggerOnTouchLeave?!n&&e==Qe&&s.triggerOnTouchLeave&&(t=Ye):t=Qe,t}function c(e,t){var i,n=e.touches;return S()||w()||v()||g()?((S()||w())&&(i=d(e,t,_e)),(v()||g())&&!1!==i&&(i=d(e,t,Ne))):L()&&!1!==i?i=d(e,t,Ue):j()&&!1!==i?i=d(e,t,ze):O()&&!1!==i&&(i=d(e,t,Re)),t===Ye&&r(e),t===Qe&&(n&&n.length||r(e)),i}function d(e,t,i){var n;if(i==_e){if(ve.trigger("swipeStatus",[t,de||null,ce||0,ue||0,we,Se]),s.swipeStatus&&!1===(n=s.swipeStatus.call(ve,e,t,de||null,ce||0,ue||0,we,Se)))return!1;if(t==Qe&&y()){if(ve.trigger("swipe",[de,ce,ue,we,Se]),s.swipe&&!1===(n=s.swipe.call(ve,e,de,ce,ue,we,Se)))return!1;switch(de){case Oe:ve.trigger("swipeLeft",[de,ce,ue,we,Se]),s.swipeLeft&&(n=s.swipeLeft.call(ve,e,de,ce,ue,we,Se));break;case Le:ve.trigger("swipeRight",[de,ce,ue,we,Se]),s.swipeRight&&(n=s.swipeRight.call(ve,e,de,ce,ue,we,Se));break;case je:ve.trigger("swipeUp",[de,ce,ue,we,Se]),s.swipeUp&&(n=s.swipeUp.call(ve,e,de,ce,ue,we,Se));break;case Pe:ve.trigger("swipeDown",[de,ce,ue,we,Se]),s.swipeDown&&(n=s.swipeDown.call(ve,e,de,ce,ue,we,Se))}}}if(i==Ne){if(ve.trigger("pinchStatus",[t,me||null,be||0,ue||0,we,pe,Se]),s.pinchStatus&&!1===(n=s.pinchStatus.call(ve,e,t,me||null,be||0,ue||0,we,pe,Se)))return!1;if(t==Qe&&m())switch(me){case Fe:ve.trigger("pinchIn",[me||null,be||0,ue||0,we,pe,Se]),s.pinchIn&&(n=s.pinchIn.call(ve,e,me||null,be||0,ue||0,we,pe,Se));break;case He:ve.trigger("pinchOut",[me||null,be||0,ue||0,we,pe,Se]),s.pinchOut&&(n=s.pinchOut.call(ve,e,me||null,be||0,ue||0,we,pe,Se))}}return i==Re?t!==Ye&&t!==Qe||(clearTimeout(ke),clearTimeout($e),C()&&!$()?(Ee=ee(),ke=setTimeout(Ae.proxy(function(){Ee=null,ve.trigger("tap",[e.target]),s.tap&&(n=s.tap.call(ve,e,e.target))},this),s.doubleTapThreshold)):(Ee=null,ve.trigger("tap",[e.target]),s.tap&&(n=s.tap.call(ve,e,e.target)))):i==Ue?t!==Ye&&t!==Qe||(clearTimeout(ke),Ee=null,ve.trigger("doubletap",[e.target]),s.doubleTap&&(n=s.doubleTap.call(ve,e,e.target))):i==ze&&(t!==Ye&&t!==Qe||(clearTimeout(ke),Ee=null,ve.trigger("longtap",[e.target]),s.longTap&&(n=s.longTap.call(ve,e,e.target)))),n}function u(){var e=!0;return null!==s.threshold&&(e=ce>=s.threshold),e}function h(){var e=!1;return null!==s.cancelThreshold&&null!==de&&(e=q(de)-ce>=s.cancelThreshold),e}function f(){return null===s.pinchThreshold||be>=s.pinchThreshold}function p(){return!s.maxTimeThreshold||!(ue>=s.maxTimeThreshold)}function b(e,t){if(!1!==s.preventDefaultEvents)if(s.allowPageScroll===Me)e.preventDefault();else{var i=s.allowPageScroll===Be;switch(t){case Oe:(s.swipeLeft&&i||!i&&s.allowPageScroll!=qe)&&e.preventDefault();break;case Le:(s.swipeRight&&i||!i&&s.allowPageScroll!=qe)&&e.preventDefault();break;case je:(s.swipeUp&&i||!i&&s.allowPageScroll!=Xe)&&e.preventDefault();break;case Pe:(s.swipeDown&&i||!i&&s.allowPageScroll!=Xe)&&e.preventDefault()}}}function m(){var e=T(),t=D(),i=f();return e&&t&&i}function g(){return!!(s.pinchStatus||s.pinchIn||s.pinchOut)}function v(){return!(!m()||!g())}function y(){var e=p(),t=u(),i=T(),n=D();return!h()&&n&&i&&t&&e}function w(){return!!(s.swipe||s.swipeStatus||s.swipeLeft||s.swipeRight||s.swipeUp||s.swipeDown)}function S(){return!(!y()||!w())}function T(){return we===s.fingers||s.fingers===We||!Je}function D(){return 0!==Se[0].end.x}function x(){return!!s.tap}function C(){return!!s.doubleTap}function E(){return!!s.longTap}function k(){if(null==Ee)return!1;var e=ee();return C()&&e-Ee<=s.doubleTapThreshold}function $(){return k()}function I(){return(1===we||!Je)&&(isNaN(ce)||ce<s.threshold)}function A(){return ue>s.longTapThreshold&&ce<Ve}function O(){return!(!I()||!x())}function L(){return!(!k()||!C())}function j(){return!(!A()||!E())}function P(){xe=ee(),Ce=event.touches.length+1}function F(){Ce=xe=0}function H(){var e=!1;xe&&(ee()-xe<=s.fingerReleaseThreshold&&(e=!0));return e}function M(){return!(!0!==ve.data(tt+"_intouch"))}function B(e){!0===e?(ve.bind(ae,i),ve.bind(oe,n),se&&ve.bind(se,a)):(ve.unbind(ae,i,!1),ve.unbind(oe,n,!1),se&&ve.unbind(se,a,!1)),ve.data(tt+"_intouch",!0===e)}function _(e,t){var i=t.identifier!==undefined?t.identifier:0;return Se[e].identifier=i,Se[e].start.x=Se[e].end.x=t.pageX||t.clientX,Se[e].start.y=Se[e].end.y=t.pageY||t.clientY,Se[e]}function N(e){var t=R(e.identifier!==undefined?e.identifier:0);return t.end.x=e.pageX||e.clientX,t.end.y=e.pageY||e.clientY,t}function R(e){for(var t=0;t<Se.length;t++)if(Se[t].identifier==e)return Se[t]}function U(){for(var e=[],t=0;t<=5;t++)e.push({start:{x:0,y:0},end:{x:0,y:0},identifier:0});return e}function z(e,t){t=Math.max(t,q(e)),ge[e].distance=t}function q(e){return ge[e]?ge[e].distance:undefined}function X(){var e={};return e[Oe]=W(Oe),e[Le]=W(Le),e[je]=W(je),e[Pe]=W(Pe),e}function W(e){return{direction:e,distance:0}}function V(){return De-Te}function G(e,t){var i=Math.abs(e.x-t.x),n=Math.abs(e.y-t.y);return Math.round(Math.sqrt(i*i+n*n))}function K(e,t){return(t/e*1).toFixed(2)}function Q(){return pe<1?He:Fe}function Y(e,t){return Math.round(Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2)))}function J(e,t){var i=e.x-t.x,n=t.y-e.y,r=Math.atan2(n,i),a=Math.round(180*r/Math.PI);return a<0&&(a=360-Math.abs(a)),a}function Z(e,t){var i=J(e,t);return i<=45&&0<=i?Oe:i<=360&&315<=i?Oe:135<=i&&i<=225?Le:45<i&&i<135?Pe:je}function ee(){return(new Date).getTime()}function te(e){var t=(e=Ae(e)).offset();return{left:t.left,right:t.left+e.outerWidth(),top:t.top,bottom:t.top+e.outerHeight()}}function ie(e,t){return e.x>t.left&&e.x<t.right&&e.y>t.top&&e.y<t.bottom}var ne=Je||et||!s.fallbackToMouseEvents,re=ne?et?Ze?"MSPointerDown":"pointerdown":"touchstart":"mousedown",ae=ne?et?Ze?"MSPointerMove":"pointermove":"touchmove":"mousemove",oe=ne?et?Ze?"MSPointerUp":"pointerup":"touchend":"mouseup",se=ne?null:"mouseleave",le=et?Ze?"MSPointerCancel":"pointercancel":"touchcancel",ce=0,de=null,ue=0,he=0,fe=0,pe=1,be=0,me=0,ge=null,ve=Ae(e),ye="start",we=0,Se=null,Te=0,De=0,xe=0,Ce=0,Ee=0,ke=null,$e=null;try{ve.bind(re,t),ve.bind(le,r)}catch(Ie){Ae.error("events not supported "+re+","+le+" on jQuery.swipe")}this.enable=function(){return ve.bind(re,t),ve.bind(le,r),ve},this.disable=function(){return o(),ve},this.destroy=function(){o(),ve.data(tt,null),ve=null},this.option=function(e,t){if(s[e]!==undefined){if(t===undefined)return s[e];s[e]=t}else Ae.error("Option "+e+" does not exist on jQuery.swipe.options");return null}}var e="1.6.9",Oe="left",Le="right",je="up",Pe="down",Fe="in",He="out",Me="none",Be="auto",_e="swipe",Ne="pinch",Re="tap",Ue="doubletap",ze="longtap",qe="horizontal",Xe="vertical",We="all",Ve=10,Ge="start",Ke="move",Qe="end",Ye="cancel",Je="ontouchstart"in window,Ze=window.navigator.msPointerEnabled&&!window.navigator.pointerEnabled,et=window.navigator.pointerEnabled||window.navigator.msPointerEnabled,tt="TouchSwipe",t={fingers:1,threshold:75,cancelThreshold:null,pinchThreshold:20,maxTimeThreshold:null,fingerReleaseThreshold:250,longTapThreshold:500,doubleTapThreshold:200,swipe:null,swipeLeft:null,swipeRight:null,swipeUp:null,swipeDown:null,swipeStatus:null,pinchIn:null,pinchOut:null,pinchStatus:null,click:null,tap:null,doubleTap:null,longTap:null,hold:null,triggerOnTouchEnd:!0,triggerOnTouchLeave:!1,allowPageScroll:"auto",fallbackToMouseEvents:!0,excludedElements:"label, button, input, select, textarea, a, .noSwipe",preventDefaultEvents:!0};Ae.fn.swipe=function(e){var t=Ae(this),i=t.data(tt);if(i&&"string"==typeof e){if(i[e])return i[e].apply(this,Array.prototype.slice.call(arguments,1));Ae.error("Method "+e+" does not exist on jQuery.swipe")}else if(!(i||"object"!=typeof e&&e))return n.apply(this,arguments);return t},Ae.fn.swipe.version=e,Ae.fn.swipe.defaults=t,Ae.fn.swipe.phases={PHASE_START:Ge,PHASE_MOVE:Ke,PHASE_END:Qe,PHASE_CANCEL:Ye},Ae.fn.swipe.directions={LEFT:Oe,RIGHT:Le,UP:je,DOWN:Pe,IN:Fe,OUT:He},Ae.fn.swipe.pageScroll={NONE:Me,HORIZONTAL:qe,VERTICAL:Xe,AUTO:Be},Ae.fn.swipe.fingers={ONE:1,TWO:2,THREE:3,ALL:We}}),function(e,n){"use strict";var t=e.History=e.History||{},r=e.jQuery;if("undefined"!=typeof t.Adapter)throw new Error("History.js Adapter has already been loaded...");t.Adapter={bind:function(e,t,i){r(e).on(t,i)},trigger:function(e,t,i){r(e).trigger(t,i)},extractEventData:function(e,t,i){return t&&t.originalEvent&&t.originalEvent[e]||i&&i[e]||n},onDomLoad:function(e){r(e)}},"undefined"!=typeof t.init&&t.init()}(window),function(o,i){"use strict";var l=o.console||i,c=o.document,n=o.navigator,r=!1,a=o.setTimeout,s=o.clearTimeout,d=o.setInterval,u=o.clearInterval,h=o.JSON,f=o.alert,p=o.History=o.History||{},b=o.history;try{(r=o.sessionStorage).setItem("TEST","1"),r.removeItem("TEST")}catch(e){r=!1}if(h.stringify=h.stringify||h.encode,h.parse=h.parse||h.decode,"undefined"!=typeof p.init)throw new Error("History.js Core has already been loaded...");p.init=function(){return"undefined"!=typeof p.Adapter&&("undefined"!=typeof p.initCore&&p.initCore(),"undefined"!=typeof p.initHtml4&&p.initHtml4(),!0)},p.initCore=function(){if("undefined"!=typeof p.initCore.initialized)return!1;if(p.initCore.initialized=!0,p.options=p.options||{},p.options.hashChangeInterval=p.options.hashChangeInterval||100,p.options.safariPollInterval=p.options.safariPollInterval||500,p.options.doubleCheckInterval=p.options.doubleCheckInterval||500,p.options.disableSuid=p.options.disableSuid||!1,p.options.storeInterval=p.options.storeInterval||1e3,p.options.busyDelay=p.options.busyDelay||250,p.options.debug=p.options.debug||!1,p.options.initialTitle=p.options.initialTitle||c.title,p.options.html4Mode=p.options.html4Mode||!1,p.options.delayInit=p.options.delayInit||!1,p.intervalList=[],p.clearAllIntervals=function(){var e,t=p.intervalList;if(null!=t){for(e=0;e<t.length;e++)u(t[e]);p.intervalList=null}},p.debug=function(){p.options.debug&&p.log.apply(p,arguments)},p.log=function(e){var t,i,n,r,a,o=void 0!==l&&"undefined"!=typeof l.log&&"undefined"!=typeof l.log.apply,s=c.getElementById("log");for(o?(t=(r=Array.prototype.slice.call(arguments)).shift(),"undefined"!=typeof l.debug?l.debug.apply(l,[t,r]):l.log.apply(l,[t,r])):t="\n"+e+"\n",i=1,n=arguments.length;i<n;++i){if("object"==typeof(a=arguments[i])&&void 0!==h)try{a=h.stringify(a)}catch(u){}t+="\n"+a+"\n"}return s?(s.value+=t+"\n-----\n",s.scrollTop=s.scrollHeight-s.clientHeight):o||f(t),!0},p.getInternetExplorerMajorVersion=function(){return p.getInternetExplorerMajorVersion.cached="undefined"!=typeof p.getInternetExplorerMajorVersion.cached?p.getInternetExplorerMajorVersion.cached:function(){for(var e=3,t=c.createElement("div"),i=t.getElementsByTagName("i");(t.innerHTML="<!--[if gt IE "+ ++e+"]><i></i><![endif]-->")&&i[0];);return 4<e&&e}()},p.isInternetExplorer=function(){return p.isInternetExplorer.cached="undefined"!=typeof p.isInternetExplorer.cached?p.isInternetExplorer.cached:Boolean(p.getInternetExplorerMajorVersion())},p.options.html4Mode?p.emulated={pushState:!0,hashChange:!0}:p.emulated={pushState:!Boolean(o.history&&o.history.pushState&&o.history.replaceState&&!/ Mobile\/([1-7][a-z]|(8([abcde]|f(1[0-8]))))/i.test(n.userAgent)&&!/AppleWebKit\/5([0-2]|3[0-2])/i.test(n.userAgent)),hashChange:Boolean(!("onhashchange"in o||"onhashchange"in c)||p.isInternetExplorer()&&p.getInternetExplorerMajorVersion()<8)},p.enabled=!p.emulated.pushState,p.bugs={setHash:Boolean(!p.emulated.pushState&&"Apple Computer, Inc."===n.vendor&&/AppleWebKit\/5([0-2]|3[0-3])/.test(n.userAgent)),safariPoll:Boolean(!p.emulated.pushState&&"Apple Computer, Inc."===n.vendor&&/AppleWebKit\/5([0-2]|3[0-3])/.test(n.userAgent)),ieDoubleCheck:Boolean(p.isInternetExplorer()&&p.getInternetExplorerMajorVersion()<8),hashEscape:Boolean(p.isInternetExplorer()&&p.getInternetExplorerMajorVersion()<7)},p.isEmptyObject=function(e){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0},p.cloneObject=function(e){var t,i;return e?(t=h.stringify(e),i=h.parse(t)):i={},i},p.getRootUrl=function(){var e=c.location.protocol+"//"+(c.location.hostname||c.location.host);return c.location.port&&(e+=":"+c.location.port),e+="/"},p.getBaseHref=function(){var e=c.getElementsByTagName("base"),t="";return 1===e.length&&(t=e[0].href.replace(/[^\/]+$/,"")),(t=t.replace(/\/+$/,""))&&(t+="/"),t},p.getBaseUrl=function(){return p.getBaseHref()||p.getBasePageUrl()||p.getRootUrl()},p.getPageUrl=function(){return((p.getState(!1,!1)||{}).url||p.getLocationHref()).replace(/\/+$/,"").replace(/[^\/]+$/,function(e){return/\./.test(e)?e:e+"/"})},p.getBasePageUrl=function(){return p.getLocationHref().replace(/[#\?].*/,"").replace(/[^\/]+$/,function(e){return/[^\/]$/.test(e)?"":e}).replace(/\/+$/,"")+"/"},p.getFullUrl=function(e,t){var i=e,n=e.substring(0,1);return t=void 0===t||t,/[a-z]+\:\/\//.test(e)||(i="/"===n?p.getRootUrl()+e.replace(/^\/+/,""):"#"===n?p.getPageUrl().replace(/#.*/,"")+e:"?"===n?p.getPageUrl().replace(/[\?#].*/,"")+e:t?p.getBaseUrl()+e.replace(/^(\.\/)+/,""):p.getBasePageUrl()+e.replace(/^(\.\/)+/,"")),i.replace(/\#$/,"")},p.getShortUrl=function(e){var t=e,i=p.getBaseUrl(),n=p.getRootUrl();return p.emulated.pushState&&(t=t.replace(i,"")),t=t.replace(n,"/"),p.isTraditionalAnchor(t)&&(t="./"+t),t=t.replace(/^(\.\/)+/g,"./").replace(/\#$/,"")},p.getLocationHref=function(e){return(e=e||c).URL===e.location.href?e.location.href:e.location.href===decodeURIComponent(e.URL)?e.URL:e.location.hash&&decodeURIComponent(e.location.href.replace(/^[^#]+/,""))===e.location.hash?e.location.href:-1==e.URL.indexOf("#")&&-1!=e.location.href.indexOf("#")?e.location.href:e.URL||e.location.href},p.store={},p.idToState=p.idToState||{},p.stateToId=p.stateToId||{},p.urlToId=p.urlToId||{},p.storedStates=p.storedStates||[],p.savedStates=p.savedStates||[],p.normalizeStore=function(){p.store.idToState=p.store.idToState||{},p.store.urlToId=p.store.urlToId||{},p.store.stateToId=p.store.stateToId||{}},p.getState=function(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);var i=p.getLastSavedState();return!i&&t&&(i=p.createStateObject()),e&&((i=p.cloneObject(i)).url=i.cleanUrl||i.url),i},p.getIdByState=function(e){var t,i=p.extractId(e.url);if(!i)if(t=p.getStateString(e),"undefined"!=typeof p.stateToId[t])i=p.stateToId[t];else if("undefined"!=typeof p.store.stateToId[t])i=p.store.stateToId[t];else{for(;i=(new Date).getTime()+String(Math.random()).replace(/\D/g,""),"undefined"!=typeof p.idToState[i]||"undefined"!=typeof p.store.idToState[i];);p.stateToId[t]=i,p.idToState[i]=e}return i},p.normalizeState=function(e){var t,i;return e&&"object"==typeof e||(e={}),"undefined"!=typeof e.normalized?e:(e.data&&"object"==typeof e.data||(e.data={}),(t={normalized:!0}).title=e.title||"",t.url=p.getFullUrl(e.url?e.url:p.getLocationHref()),t.hash=p.getShortUrl(t.url),t.data=p.cloneObject(e.data),t.id=p.getIdByState(t),t.cleanUrl=t.url.replace(/\??\&_suid.*/,""),t.url=t.cleanUrl,i=!p.isEmptyObject(t.data),(t.title||i)&&!0!==p.options.disableSuid&&(t.hash=p.getShortUrl(t.url).replace(/\??\&_suid.*/,""),/\?/.test(t.hash)||(t.hash+="?"),t.hash+="&_suid="+t.id),t.hashedUrl=p.getFullUrl(t.hash),(p.emulated.pushState||p.bugs.safariPoll)&&p.hasUrlDuplicate(t)&&(t.url=t.hashedUrl),t)},p.createStateObject=function(e,t,i){var n={data:e,title:t,url:i};return n=p.normalizeState(n)},p.getStateById=function(e){return e=String(e),p.idToState[e]||p.store.idToState[e]||i},p.getStateString=function(e){var t;return t={data:p.normalizeState(e).data,title:e.title,url:e.url},h.stringify(t)},p.getStateId=function(e){return p.normalizeState(e).id},p.getHashByState=function(e){return p.normalizeState(e).hash},p.extractId=function(e){var t,i;return i=-1!=e.indexOf("#")?e.split("#")[0]:e,(t=/(.*)\&_suid=([0-9]+)$/.exec(i))&&t[1]||e,(t?String(t[2]||""):"")||!1},p.isTraditionalAnchor=function(e){return!/[\/\?\.]/.test(e)},p.extractState=function(e,t){var i,n,r=null;return t=t||!1,(i=p.extractId(e))&&(r=p.getStateById(i)),r||(n=p.getFullUrl(e),(i=p.getIdByUrl(n)||!1)&&(r=p.getStateById(i)),!r&&t&&!p.isTraditionalAnchor(e)&&(r=p.createStateObject(null,null,n))),r},p.getIdByUrl=function(e){return p.urlToId[e]||p.store.urlToId[e]||i},p.getLastSavedState=function(){return p.savedStates[p.savedStates.length-1]||i},p.getLastStoredState=function(){return p.storedStates[p.storedStates.length-1]||i},p.hasUrlDuplicate=function(e){var t;return(t=p.extractState(e.url))&&t.id!==e.id},p.storeState=function(e){return p.urlToId[e.url]=e.id,p.storedStates.push(p.cloneObject(e)),e},p.isLastSavedState=function(e){var t=!1;return p.savedStates.length&&(t=e.id===p.getLastSavedState().id),t},p.saveState=function(e){return!p.isLastSavedState(e)&&(p.savedStates.push(p.cloneObject(e)),!0)},p.getStateByIndex=function(e){return void 0===e?p.savedStates[p.savedStates.length-1]:e<0?p.savedStates[p.savedStates.length+e]:p.savedStates[e]},p.getCurrentIndex=function(){return p.savedStates.length<1?0:p.savedStates.length-1},p.getHash=function(e){var t=p.getLocationHref(e);return p.getHashByUrl(t)},p.unescapeHash=function(e){var t=p.normalizeHash(e);return t=decodeURIComponent(t)},p.normalizeHash=function(e){return e.replace(/[^#]*#/,"").replace(/#.*/,"")},p.setHash=function(e,t){var i,n;return!1!==t&&p.busy()?(p.pushQueue({scope:p,callback:p.setHash,args:arguments,queue:t}),!1):(p.busy(!0),(i=p.extractState(e,!0))&&!p.emulated.pushState?p.pushState(i.data,i.title,i.url,!1):p.getHash()!==e&&(p.bugs.setHash?(n=p.getPageUrl(),p.pushState(null,null,n+"#"+e,!1)):c.location.hash=e),p)},p.escapeHash=function(e){var t=p.normalizeHash(e);return t=o.encodeURIComponent(t),p.bugs.hashEscape||(t=t.replace(/\%21/g,"!").replace(/\%26/g,"&").replace(/\%3D/g,"=").replace(/\%3F/g,"?")),t},p.getHashByUrl=function(e){var t=String(e).replace(/([^#]*)#?([^#]*)#?(.*)/,"$2");return t=p.unescapeHash(t)},p.setTitle=function(e){var t,i=e.title;i||(t=p.getStateByIndex(0))&&t.url===e.url&&(i=t.title||p.options.initialTitle);try{c.getElementsByTagName("title")[0].innerHTML=i.replace("<","&lt;").replace(">","&gt;").replace(" & "," &amp; ")}catch(n){}return c.title=i,p},p.queues=[],p.busy=function(e){if(void 0!==e?p.busy.flag=e:"undefined"==typeof p.busy.flag&&(p.busy.flag=!1),!p.busy.flag){s(p.busy.timeout);var n=function(){var e,t,i;if(!p.busy.flag)for(e=p.queues.length-1;0<=e;--e)0!==(t=p.queues[e]).length&&(i=t.shift(),p.fireQueueItem(i),p.busy.timeout=a(n,p.options.busyDelay))};p.busy.timeout=a(n,p.options.busyDelay)}return p.busy.flag},p.busy.flag=!1,p.fireQueueItem=function(e){return e.callback.apply(e.scope||p,e.args||[])},p.pushQueue=function(e){return p.queues[e.queue||0]=p.queues[e.queue||0]||[],p.queues[e.queue||0].push(e),p},p.queue=function(e,t){return"function"==typeof e&&(e={callback:e}),void 0!==t&&(e.queue=t),p.busy()?p.pushQueue(e):p.fireQueueItem(e),p},p.clearQueue=function(){return p.busy.flag=!1,p.queues=[],p},p.stateChanged=!1,p.doubleChecker=!1,p.doubleCheckComplete=function(){return p.stateChanged=!0,p.doubleCheckClear(),p},p.doubleCheckClear=function(){return p.doubleChecker&&(s(p.doubleChecker),p.doubleChecker=!1),p},p.doubleCheck=function(e){return p.stateChanged=!1,p.doubleCheckClear(),p.bugs.ieDoubleCheck&&(p.doubleChecker=a(function(){return p.doubleCheckClear(),p.stateChanged||e(),!0},p.options.doubleCheckInterval)),p},p.safariStatePoll=function(){var e=p.extractState(p.getLocationHref());if(!p.isLastSavedState(e))return e||p.createStateObject(),p.Adapter.trigger(o,"popstate"),p},p.back=function(e){return!1!==e&&p.busy()?(p.pushQueue({scope:p,callback:p.back,args:arguments,queue:e}),!1):(p.busy(!0),p.doubleCheck(function(){p.back(!1)}),b.go(-1),!0)},p.forward=function(e){return!1!==e&&p.busy()?(p.pushQueue({scope:p,callback:p.forward,args:arguments,queue:e}),!1):(p.busy(!0),p.doubleCheck(function(){p.forward(!1)}),b.go(1),!0)},p.go=function(e,t){var i;if(0<e)for(i=1;i<=e;++i)p.forward(t);else{if(!(e<0))throw new Error("History.go: History.go requires a positive or negative integer passed.");for(i=-1;e<=i;--i)p.back(t)}return p},p.emulated.pushState){var e=function(){};p.pushState=p.pushState||e,p.replaceState=p.replaceState||e}else p.onPopState=function(e,t){var i,n,r=!1,a=!1;return p.doubleCheckComplete(),(i=p.getHash())?((n=p.extractState(i||p.getLocationHref(),!0))?p.replaceState(n.data,n.title,n.url,!1):(p.Adapter.trigger(o,"anchorchange"),p.busy(!1)),p.expectedStateId=!1):((a=(r=p.Adapter.extractEventData("state",e,t)||!1)?p.getStateById(r):p.expectedStateId?p.getStateById(p.expectedStateId):p.extractState(p.getLocationHref()))||(a=p.createStateObject(null,null,p.getLocationHref())),p.expectedStateId=!1,p.isLastSavedState(a)?(p.busy(!1),!1):(p.storeState(a),p.saveState(a),p.setTitle(a),p.Adapter.trigger(o,"statechange"),p.busy(!1),!0))},p.Adapter.bind(o,"popstate",p.onPopState),p.pushState=function(e,t,i,n){if(p.getHashByUrl(i)&&p.emulated.pushState)throw new Error("History.js does not support states with fragement-identifiers (hashes/anchors).");if(!1!==n&&p.busy())return p.pushQueue({scope:p,callback:p.pushState,args:arguments,queue:n}),!1;p.busy(!0);var r=p.createStateObject(e,t,i);return p.isLastSavedState(r)?p.busy(!1):(p.storeState(r),p.expectedStateId=r.id,b.pushState(r.id,r.title,r.url),p.Adapter.trigger(o,"popstate")),!0},p.replaceState=function(e,t,i,n){if(p.getHashByUrl(i)&&p.emulated.pushState)throw new Error("History.js does not support states with fragement-identifiers (hashes/anchors).");if(!1!==n&&p.busy())return p.pushQueue({scope:p,callback:p.replaceState,args:arguments,queue:n}),!1;p.busy(!0);var r=p.createStateObject(e,t,i);return p.isLastSavedState(r)?p.busy(!1):(p.storeState(r),p.expectedStateId=r.id,b.replaceState(r.id,r.title,r.url),p.Adapter.trigger(o,"popstate")),!0};if(r){try{p.store=h.parse(r.getItem("History.store"))||{}}catch(t){p.store={}}p.normalizeStore()}else p.store={},p.normalizeStore();p.Adapter.bind(o,"unload",p.clearAllIntervals),p.saveState(p.storeState(p.extractState(p.getLocationHref(),!0))),r&&(p.onUnload=function(){var e,t,i;try{e=h.parse(r.getItem("History.store"))||{}}catch(c){e={}}for(t in e.idToState=e.idToState||{},e.urlToId=e.urlToId||{},e.stateToId=e.stateToId||{},p.idToState)p.idToState.hasOwnProperty(t)&&(e.idToState[t]=p.idToState[t]);for(t in p.urlToId)p.urlToId.hasOwnProperty(t)&&(e.urlToId[t]=p.urlToId[t]);for(t in p.stateToId)p.stateToId.hasOwnProperty(t)&&(e.stateToId[t]=p.stateToId[t]);p.store=e,p.normalizeStore(),i=h.stringify(e);try{r.setItem("History.store",i)}catch(n){if(n.code!==DOMException.QUOTA_EXCEEDED_ERR)throw n;r.length&&(r.removeItem("History.store"),r.setItem("History.store",i))}},p.intervalList.push(d(p.onUnload,p.options.storeInterval)),p.Adapter.bind(o,"beforeunload",p.onUnload),p.Adapter.bind(o,"unload",p.onUnload)),p.emulated.pushState||(p.bugs.safariPoll&&p.intervalList.push(d(p.safariStatePoll,p.options.safariPollInterval)),"Apple Computer, Inc."!==n.vendor&&"Mozilla"!==(n.appCodeName||"")||(p.Adapter.bind(o,"hashchange",function(){p.Adapter.trigger(o,"popstate")}),p.getHash()&&p.Adapter.onDomLoad(function(){p.Adapter.trigger(o,"hashchange")})))},(!p.options||!p.options.delayInit)&&p.init()}(window);var Dribbble=Dribbble||{};Dribbble.JsConfig=Dribbble.JsConfig||{},Dribbble.isMobile=function(){var e=/iPhone|iPad|iPod|Android/i.test(navigator.userAgent),t="ontouchstart"in window||window.DocumentTouch&&document instanceof DocumentTouch;return e&&t},Dribbble.isMobileSafari=function(){return!!navigator.userAgent.match(/(iPad|iPhone|iPod touch)/)},Dribbble.isAndroidChrome=function(){var e=navigator.userAgent,t=/Android/i.test(e),i=/Chrome/i.test(e);return t&&i},Dribbble.isIosSafari=function(e){var t,i=navigator.userAgent,n="MacIntel"===navigator.platform&&1<navigator.maxTouchPoints;switch(e){case"iPhone":t=/(iPhone)/i.test(i);break;case"iPad":t=/(iPad)/i.test(i)||n;break;default:t=/(iPhone|iPad)/i.test(i)||n}var r=/WebKit/i.test(i),a=/CriOS/i.test(i),o=/FxiOS/i.test(i);return t&&r&&!a&&!o},Dribbble.isMobileViewport=function(){return window.matchMedia("screen and (max-width: 959px)").matches},$(document).ajaxSend(function(e,t,i){var n=$('meta[name="csrf-token"]').attr("content");i.noCSRF!=undefined&&0!=i.noCSRF||t.setRequestHeader("X-CSRF-Token",n)});var notificationSelectors=[".subscribe-to-notification-target",".subscribe-to-notification"],notificationLinkSelectors=$(notificationSelectors).map(function(e,t){return t+" a[data-method]"}).get();$(document).on("ajax:success",notificationLinkSelectors.join(", "),function(e,t){var i=$(e.target);$(i.parents(".subscribe-to-notification-target")[0]||i.closest(notificationSelectors.join(", "))).addClass("subscribe-to-notification").html(t)}),jQuery.fn.showAndHide=function(e){return $(e).hide(),this.show()},jQuery.fn.modelId=function(){var e=$(this).attr("id");if(null==e)return null;var t=e.split(/[-_]/g);return 1<t.length?t[t.length-1]:null},ShowAndHideControl=function(e){var t=$(e.target),i=$(e.showControl),n=$(e.hideControl);i.on("click",function(){return t.showAndHide(i),!1}),n.on("click",function(){return t.hide(),i.show(),!1})},$("a[data-hover]").on("hover",function(){var e=$(this);e.attr("data-hover-out",e.text()).text(e.attr("data-hover"))},function(){var e=$(this);e.text(e.attr("data-hover-out")),e.removeAttr("data-hover-out")}),$(document).on("click","a.block",function(e){var t=this;$.post(t.href,function(e){memberContainer(t).addClass("blocked-by-current-user").removeClass("followed-by-current-user listed-by-current-user"),e&&e.data&&Dribbble.EventBus.$emit("track:userBlocked",e.data)}),e.preventDefault()}),$(document).on("click","a.unblock",function(e){var t=this;$.ajax(t.href,{type:"DELETE",success:function(){memberContainer(t).removeClass("blocked-by-current-user")}}),e.preventDefault()}),$([".remove-from-team a",".leave-team a",".js-remove-from-team-link"]).each(function(e,t){$(document).on("click",".actions-list "+t,function(e){var t=this;$.ajax(t.href,{type:"DELETE",success:function(e){e.error?Dribbble.Notify.error(e.error):Dribbble.Notify.alert(e.success)}}),e.preventDefault()})}),window.matchMedia("screen and (min-width: "+DEVICE_WIDTH_BREAKPOINT+")").matches||$("#main .tabs > li.has-dd > a, div.full-tabs ul.full-tabs-links > li.more > a").on("click",function(){var e=$(this).parent();return e.hasClass("hover")?e.removeClass("hover"):($("#main .tabs > li.hover, div.full-tabs ul.full-tabs-links > li.hover").removeClass("hover"),e.addClass("hover")),!1}),$(document).on("click","a.action.draft",function(){var e=$(this).parent("form");return confirm($(this).data("message")+" "+e.data().userName+"?")&&$(this).parent("form").trigger("submit"),!1}),$(document).on("ajax:success",".draft-form",function(e,t,i,n){$(this).replaceWith(n.responseText)}).on("ajax:error",".draft-form",function(e,t){var i="Hrm\u2026 Something went wrong. If this persists, try refreshing the page.";406==t.status&&(i="This designer has already been invited. Find another designer to draft!",$(this).replaceWith(t.responseText)),Dribbble.Notify.alert(i)}),Dribbble.ActionMenu={bindEventListeners:function(){$("body").on({mouseenter:this.show.bind(this),mouseleave:this.hide.bind(this)},".actions-menu"),$(".action.settings, .action.grid").on("touchstart",this.toggle.bind(this)),$(".actions-list li a").on("click",function(e){this.hide(e)}.bind(this))},show:function(e){Dribbble.HoverCards&&Dribbble.HoverCards.dismiss(),$(e.target).closest(".actions-menu").addClass("active").find(".actions-list").show()},hide:function(e){$(e.target).closest(".actions-menu").removeClass("active").find(".actions-list").hide()},toggle:function(e){$(e.target).closest(".actions-menu").hasClass("active")?this.hide(e):this.show(e)}},Dribbble.ActionMenu.bindEventListeners(),Dribbble.defaultTipsyOptions={fade:!1,opacity:1,className:function(){return this.getAttribute("data-tipsy-class")},gravity:function(){var e=$(this);if(e.data("tipsy-gravity"))return e.data("tipsy-gravity");var t=100,i=e.offset(),n=i.left,r=i.top;return n<t?"sw":n>$(window).width()-t?r<t?"ne":"se":r<t?"n":"s"},html:!0},Dribbble.tipsyInit=function(){Dribbble.isMobile()?$("[rel*=tipsy]:not(.tipsy-mobile-disabled)").tipsy(Dribbble.defaultTipsyOptions):$("[rel*=tipsy]").tipsy(Dribbble.defaultTipsyOptions)},Dribbble.tipsyInit(),$(document).ajaxSuccess(function(){$(".tipsy").hide(),Dribbble.tipsyInit()}),Dribbble.imageOnLoad=function(e,t){function i(e,t){var i;return function(){return e&&(i=e.apply(t||this,arguments),e=null),i}}var n=i(t);e.addEventListener("load",n),e.complete&&0<e.naturalWidth&&n()},Dribbble.FollowButton=function(e,o){return o=o||function(){return null},{initialize:function(){this.bindEventListeners()},bindEventListeners:function(){User.loggedIn()&&(e.on("click.follow-buttton",".follow-prompt a.follow",this.follow.bind(this)),e.on("click.follow-buttton",".follow-prompt a.unfollow",this.unfollow.bind(this)))},container:function(e){return e.data("container")?$(e.data("container")):memberContainer(e)},follow:function(e){var t=$(e.currentTarget),i=this.container(t);return i.hasClass("blocking-current-user")?Dribbble.Notify.alert("You have been blocked from following this account at the request of the member."):$.ajax(t.attr("href"),{type:"POST",beforeSend:function(){t.addClass("processing")}}).done(function(e){i.addClass("followed-by-current-user"),this.modifyFollowerCount(1,i),this.logAnalyticsFollow(t,{action:"follow"}),t.data("notify")&&Dribbble.Notify.success(t.data("notify")),Dribbble.EventBus&&Dribbble.EventBus.$emit("userFollow",{isFollowing:!0,username:t.attr("href").split("/")[1]}),e&&e.data&&Dribbble.EventBus.$emit("track:userFollowed",e.data)}.bind(this)).fail(function(e){Dribbble.Notify.alert(e.responseText)}).always(function(){t.removeClass("processing")}),!1},unfollow:function(e){var t=$(e.currentTarget),i=this.container(t);return $.ajax(t.attr("href"),{type:"DELETE",beforeSend:function(){t.addClass("processing")}}).done(function(){i.removeClass("followed-by-current-user"),this.modifyFollowerCount(-1,i),this.logAnalyticsFollow(t,{action:"unfollow"}),t.data("notify")&&Dribbble.Notify.success(t.data("notify")),Dribbble.EventBus&&Dribbble.EventBus.$emit("userFollow",{isFollowing:!1,username:t.attr("href").split("/")[1]})}.bind(this)).always(function(){t.removeClass("processing")}),!1},modifyFollowerCount:function(e,t){var i=o(t);if(i){var n=i.find(".meta"),r=i.find(".count"),a=parseInt(r.first().text().replace(",",""),10)+e;r.text(numberWithDelimiter(a)),1==a?n.text("Follower"):n.text("Followers")}},getAnalyticsInfo:function(e){return $container=e.closest(".follow-prompt"),[$container.data("ga-name"),$container.data("ga-key")]},logAnalyticsFollow:function(e,t){var i=this.getAnalyticsInfo(e);i[0]!==undefined&&Dribbble.Analytics.log(i[1],i[0],"event",{event_category:"Followers",event_action:t.action||"follow"})}}},new Dribbble.FollowButton($("#wrap")).initialize(),new Dribbble.FollowButton($(".player-cards .player-info"),function(e){return e.find(".stat-followers")}).initialize(),new Dribbble.FollowButton($(".shot-overlay")).initialize(),new Dribbble.FollowButton($(".profile-essentials"),function(){return $(".full-tabs .full-tabs-links .followers")}).initialize(),Dribbble.Overlay={overlays:[],anyOpen:function(){for(var e=0;e<this.overlays.length;e++)if(this.overlays[e].isOpen)return!0;return!1},find:function(e){for(var t=null,i=0;i<this.overlays.length;i++)this.overlays[i].$el&&this.overlays[i].$el.attr("id")&&this.overlays[i].$el.attr("id")===e&&(t=this.overlays[i]);return t},applyOptions:function(e,t){var i=function(e){return[].concat(e||[])};return e.$el=t.$el,t.trigger&&(e.trigger=t.trigger,e.bindTrigger()),e.beforeShow=i(t.beforeShow),e.onShow=i(t.onShow),e.beforeHide=i(t.beforeHide),e.onHide=i(t.onHide),t.remoteContent&&(e.remoteContent=t.remoteContent,e.beforeShow.push(t.loadRemoteContent||e.defaultLoadRemoteContent.bind(e)),e.onHide.push(t.hideRemoteContent||e.defaultHideRemoteContent.bind(e))),e.suppressClickToClose=t.suppressClickToClose,e.suppressEscToClose=t.suppressEscToClose,e}},Dribbble.Overlay.Simple=function(e){Dribbble.Overlay.applyOptions(this,e),Dribbble.Overlay.overlays.push(this)},Dribbble.Overlay.Simple.prototype={isOpen:!1,currentTarget:null,_runCallbacks:function(e){var i=!0;return $.each(e,function(e,t){i&&0==t()&&(i=!1)}.bind(this)),i},triggerHandler:function(e){e.preventDefault(),this.currentTarget=e.currentTarget,this.currentTarget.classList.contains("disabled")||this.show()},bindTrigger:function(){$(this.trigger).on("click",function(e){this.triggerHandler(e)}.bind(this))},find:function(e){return this.$el.find(e)},defaultOnComplete:function(e){e.redirect?window.location=e.redirect:$(this.remoteContent.el).html(e),$(document).trigger("overlay:ajaxComplete",this.remoteContent.el)},defaultLoadRemoteContent:function(){var e=this.remoteContent.url||$(
this.currentTarget||this.trigger).attr("href");$.isFunction(e)&&(e=e.apply(this));var t={context:this,type:this.remoteContent.verb||"GET",url:e,success:this.defaultOnComplete.bind(this),complete:function(e,t){(this.remoteContent.onComplete||function(){}).call(this,e.responseText,t,e)}};this.remoteContent.data&&($.isFunction(this.remoteContent.data)?t.data=this.remoteContent.data.apply(this):t.data=this.remoteContent.data),$.ajax(t)},defaultHideRemoteContent:function(){$(this.remoteContent.el).html('<div class="processing">Loading\u2026</div>')},listenForEvents:function(){this.$el.on("click.overlay",".close",this.hide.bind(this)),this.$el.on("click.overlay",".cancel",this.hide.bind(this)),this.suppressClickToClose||this.$el.on("click.overlay",function(e){$(e.target).is(this.$el)&&this.hide()}.bind(this)),this.suppressEscToClose||(this.keyListeners=Dribbble.Hotkeys.map("overlay",{ESCAPE:this.hide.bind(this)}))},stopListeningForEvents:function(){this.$el.off("click.overlay"),this.keyListeners&&this.keyListeners.off()},show:function(e){return this._runCallbacks(this.beforeShow)&&(this.listenForEvents(),this.$el.css("height","100%"),this.$el.addClass("overlay-visible").one(this.whichTransitionEvent(),function(){this.$el.trigger("focus"),this._runCallbacks(this.onShow)}.bind(this)),this.isOpen=!0,e&&(this.modalClass=e,this.$el.addClass(e))),!1},hide:function(){return this._runCallbacks(this.beforeHide)&&(this.stopListeningForEvents(),this.$el.removeClass("overlay-visible").one(this.whichTransitionEvent(),function(){this._runCallbacks(this.onHide)}.bind(this)),this.isOpen=!1,this.modalClass&&(this.$el.removeClass(this.modalClass),this.modalClass=null)),!1},whichTransitionEvent:function(){if(this.transitionEvent)return this.transitionEvent;var e,t=document.createElement("fakeelement"),i={transition:"transitionend",OTransition:"oTransitionEnd",MozTransition:"transitionend",WebkitTransition:"webkitTransitionEnd"};for(e in i)if(t.style[e]!==undefined)return this.transitionEvent=i[e],i[e]}},Dribbble.Overlay.RemoteForm=function(e){Dribbble.Overlay.applyOptions(this,e),this.form=e.form,this.verb=e.verb||"POST",this.onSubmit=e.onSubmit||this.defaultOnSubmit.bind(this),this.onSuccess=e.onSuccess||this.defaultOnSuccess.bind(this),this.onError=e.onError||this.defaultOnError.bind(this),this.containerToUpdateOnSuccess=e.containerToUpdateOnSuccess,this.containerToUpdateOnFailure=e.containerToUpdateOnFailure,this.successNotification=e.successNotification,Dribbble.Overlay.overlays.push(this)},Dribbble.Overlay.RemoteForm.prototype=$.extend({},Dribbble.Overlay.Simple.prototype,{defaultOnError:function(e){422==e.status?$(this.containerToUpdateOnFailure).html(e.responseText):Dribbble.Notify.alert(e.responseText)},listenForEvents:function(){Dribbble.Overlay.Simple.prototype.listenForEvents.apply(this),this.$el.on("submit.remoteOverlay",this.form||"form",this.onSubmit)},stopListeningForEvents:function(){Dribbble.Overlay.Simple.prototype.stopListeningForEvents.apply(this),this.$el.off("submit.remoteOverlay")},defaultOnSuccess:function(e){0==this.$el.find(this.containerToUpdateOnSuccess).length&&this.hide(),$(this.containerToUpdateOnSuccess).html(e),this.successNotification&&Dribbble.Notify.success(this.successNotification)},defaultOnSubmit:function(e){var t;e&&e.preventDefault(),t=e&&"FORM"==e.currentTarget.tagName?$(e.currentTarget):this.find(this.form||"form"),$.ajax({context:this,type:this.verb,url:t.attr("action"),data:t.serialize(),success:this.onSuccess,error:this.onError})}}),Dribbble.Overlay.Adapters={bind:function(e,t){var i=e.data(),n={$el:$(i.overlay||"#overlay"),trigger:e,form:i.form,containerToUpdateOnFailure:i.failureContainer||i.remoteContent&&i.remoteContent.container,containerToUpdateOnSuccess:i.successContainer,successNotification:i.notification,suppressClickToClose:i.suppressClickToClose,suppressEscToClose:i.suppressEscToClose};i.remoteContent&&(n.remoteContent={el:i.remoteContent.container,url:i.remoteContent.url}),e.data().overlayObj=new t(n),i.trackingData&&(e.data().overlayObj.trackingData=i.trackingData)},SimpleTrigger:function(e){Dribbble.Overlay.Adapters.bind(e,Dribbble.Overlay.Simple)},RemoteFormTrigger:function(e){Dribbble.Overlay.Adapters.bind(e,Dribbble.Overlay.RemoteForm)},bindAll:function(){$(".overlay-trigger").each(function(){$(this).data().overlayObj||new Dribbble.Overlay.Adapters.SimpleTrigger($(this))}),$(".overlay-form-trigger").each(function(){$(this).data().overlayObj||new Dribbble.Overlay.Adapters.RemoteFormTrigger($(this))})}},Dribbble.Overlay.Adapters.bindAll(),$(document).ajaxComplete(function(){setTimeout(function(){Dribbble.Overlay.Adapters.bindAll()})}),Dribbble.Carousel=function(e){this.create(e)},Dribbble.Carousel.prototype={create:function(e){if(this.applyOptions(e),this.selector="string"==typeof this.selector?document.querySelector(this.selector):this.selector,null===this.selector)throw new Error("Something wrong with your selector");this.activeBreakpoint=null,this.updateFromBreakpoint(),this.selectorWidth=this.selector.offsetWidth,this.innerElements=[].slice.call(this.selector.children),this.slidesCount=this.innerElements.length,this.currentSlideIndex=Math.max(0,Math.min(this.startIndex,this.slidesCount-this.perPage)),this.transformProperty=this.getTransformProperty(),this.resizeTimer=null,Dribbble.Globals.throttle().then(function(e){this.resizeHandler=e(function(){this.recalculateSlides()}.bind(this),100),window.addEventListener("resize",this.resizeHandler)}.bind(this));var t=["touchstartHandler","touchendHandler","touchmoveHandler","mousedownHandler","mouseupHandler","mouseleaveHandler","mousemoveHandler","clickHandler"];this.resizeObserverSelector&&this.observeResize();for(var i=0;i<t.length;i++)this[t[i]]=this[t[i]].bind(this);this.initialize()},applyOptions:function(e){var t=e||{},i={selector:".dribbble-carousel",wrapperClass:"dribbble-carousel-wrapper display-flex",slideClass:"dribbble-carousel-slide",disableAnimation:!1,duration:200,easing:"ease-out",perPage:1,spaceBetween:0,previewPadding:0,preventClickOnDrag:!1,preview:!1,breakpoints:{},startIndex:0,draggable:!0,multipleDrag:!0,threshold:20,resizeObserverSelector:"",onInit:function(){},onBeforeChange:function(){},onChange:function(){}};for(var n in i)this[n]=t.hasOwnProperty(n)?t[n]:i[n];this.preview&&(this.previewPadding=2*this.spaceBetween)},updateFromBreakpoint:function(){var e=this.activeBreakpoint;for(var t in this.breakpoints)window.innerWidth>=t&&(this.activeBreakpoint=t);this.activeBreakpoint=null!==this.activeBreakpoint&&window.innerWidth>=this.activeBreakpoint?this.activeBreakpoint:null,e!==this.activeBreakpoint&&(null!==e&&null===this.activeBreakpoint?this.updateOptions():this.updateOptions(this.breakpoints[this.activeBreakpoint]))},updateOptions:function(e){var t=e||{};for(var i in this.defaultSet||(this.defaultAllowedOptions={disableAnimation:this.disableAnimation,duration:this.duration,easing:this.easing,perPage:this.perPage,draggable:this.draggable,multipleDrag:this.multipleDrag,threshold:this.threshold,spaceBetween:this.spaceBetween,preview:this.preview},this.defaultSet=!0),this.defaultAllowedOptions)this[i]=t.hasOwnProperty(i)?t[i]:this.defaultAllowedOptions[i];this.eventsBinded&&(this.draggable?this.bindDragEvent():this.removeDragEvents())},initialize:function(){this.bindEventListeners(),this.selector.style.overflow="hidden",this.buildSliderFrame(),this.onInit(this)},bindEventListeners:function(){this.bindDragEvent(),this.eventsBinded=!0},bindDragEvent:function(){this.draggable&&(this.pointerDown=!1,this.drag={startX:0,endX:0,startY:0,letItGo:null,preventClick:!1},this.selector.addEventListener("touchstart",this.touchstartHandler,{passive:!0}),this.selector.addEventListener("touchend",this.touchendHandler),this.selector.addEventListener("touchmove",this.touchmoveHandler),this.selector.addEventListener("mousedown",this.mousedownHandler),this.selector.addEventListener("mouseup",this.mouseupHandler),this.selector.addEventListener("mouseleave",this.mouseleaveHandler),this.selector.addEventListener("mousemove",this.mousemoveHandler),this.selector.addEventListener("click",this.clickHandler))},removeDragEvents:function(){this.selector.removeEventListener("touchstart",this.touchstartHandler),this.selector.removeEventListener("touchend",this.touchendHandler),this.selector.removeEventListener("touchmove",this.touchmoveHandler),this.selector.removeEventListener("mousedown",this.mousedownHandler),this.selector.removeEventListener("mouseup",this.mouseupHandler),this.selector.removeEventListener("mouseleave",this.mouseleaveHandler),this.selector.removeEventListener("mousemove",this.mousemoveHandler),this.selector.removeEventListener("click",this.clickHandler)},removeAllEventListeners:function(){window.removeEventListener("resize",this.resizeHandler),this.removeDragEvents()},buildSliderFrame:function(){this.sliderFrame=document.createElement("div"),this.sliderFrame.className=this.wrapperClass,this.sliderFrame.style.width=this.getSlideWidth(!0)*this.slidesCount+this.previewPadding+"px",this.enableTransition(),this.selector.style.cursor=this.draggable?"-webkit-grab":"inherit";for(var e=document.createDocumentFragment(),t=0;t<this.slidesCount;t++){var i=this.buildSliderFrameItem(this.innerElements[t],0===t);e.appendChild(i)}this.sliderFrame.appendChild(e),this.selector.innerHTML="",this.selector.appendChild(this.sliderFrame),this.slideToCurrent()},buildSliderFrameItem:function(e,t){var i=document.createElement("div");return i.className=this.slideClass,this.spaceBetween?(i.style.width=this.getSlideWidth()+"px",t&&this.preview&&(i.style.marginLeft=this.previewPadding+"px"),i.style.marginRight=this.spaceBetween+"px"):i.style.width=100/this.slidesCount+"%",i.appendChild(e),i},getSlideWidth:function(e){var t=this.selectorWidth/this.perPage;if(this.spaceBetween){if(this.preview)var i=this.perPage*(2*this.previewPadding);else i=(this.perPage-1)*this.spaceBetween;t=(this.selectorWidth-i)/this.perPage,e&&(t+=this.spaceBetween)}return t},slideToCurrent:function(e,t){var i=-1*this.currentSlideIndex*this.getSlideWidth(!0);if(this.disableAnimation&&this.disableTransition(),this.sliderFrame.style[this.transformProperty]="translate3d("+i+"px, 0, 0)",e)if(this.onBeforeChange(t,this.currentSlideIndex),this.disableAnimation)this.onChange(t,this.currentSlideIndex);else{var n=this;window.setTimeout(function(){n.onChange(t,n.currentSlideIndex)},n.duration)}},prev:function(e,t){var i=e||1;if(!(this.slidesCount<=this.perPage)){var n=this.currentSlideIndex;this.currentSlideIndex=Math.max(this.currentSlideIndex-i,0),isNaN(this.currentSlideIndex)||this.currentSlideIndex===n||(this.slideToCurrent(!0,n),t&&t.call(this))}},next:function(e,t){var i=e||1;if(!(this.slidesCount<=this.perPage)){var n=this.currentSlideIndex;this.currentSlideIndex=Math.min(this.currentSlideIndex+i,this.slidesCount-this.perPage),isNaN(this.currentSlideIndex)||this.currentSlideIndex===n||(this.slideToCurrent(!0,n),t&&t.call(this))}},goTo:function(e,t){if(!(this.slidesCount<=this.perPage)){var i=this.currentSlideIndex;this.currentSlideIndex=Math.min(Math.max(e,0),this.slidesCount-this.perPage),isNaN(this.currentSlideIndex)||this.currentSlideIndex===i||(this.slideToCurrent(!0,i),t&&t.call(this))}},recalculateSlides:function(){this.updateFromBreakpoint(),this.currentSlideIndex+this.perPage>this.slidesCount&&(this.currentSlideIndex=this.slidesCount<=this.perPage?0:this.slidesCount-this.perPage),this.selectorWidth=this.selector.offsetWidth,this.buildSliderFrame()},observeResize:function(){var e=new ResizeObserver(function(){this.recalculateSlides()}.bind(this)),t=document.querySelector(this.resizeObserverSelector);t&&e.observe(t)},touchstartHandler:function(e){-1!==["TEXTAREA","OPTION","INPUT","SELECT"].indexOf(e.target.nodeName)||(e.stopPropagation(),this.pointerDown=!0,this.drag.startX=e.touches[0].pageX,this.drag.startY=e.touches[0].pageY)},touchendHandler:function(e){e.stopPropagation(),this.pointerDown=!1,this.enableTransition(),this.drag.endX&&this.updateAfterDrag(),this.clearDrag()},touchmoveHandler:function(e){if(e.stopPropagation(),null===this.drag.letItGo&&(this.drag.letItGo=Math.abs(this.drag.startY-e.touches[0].pageY)<Math.abs(this.drag.startX-e.touches[0].pageX)),this.pointerDown&&this.drag.letItGo){e.preventDefault(),this.drag.endX=e.touches[0].pageX;var t="all 0ms "+this.easing;this.sliderFrame.style.webkitTransition=t,this.sliderFrame.style.transition=t;var i=-1*(this.currentSlideIndex*this.getSlideWidth(!0)-(this.drag.endX-this.drag.startX));this.sliderFrame.style[this.transformProperty]="translate3d("+i+"px, 0, 0)"}},mousedownHandler:function(e){-1!==["TEXTAREA","OPTION","INPUT","SELECT"].indexOf(e.target.nodeName)||(e.preventDefault(),e.stopPropagation(),this.pointerDown=!0,this.drag.startX=e.pageX)},mouseupHandler:function(e){e.stopPropagation(),this.pointerDown=!1,this.selector.style.cursor="-webkit-grab",this.enableTransition(),this.drag.endX&&this.updateAfterDrag(),this.clearDrag()},mouseleaveHandler:function(e){this.pointerDown&&(this.pointerDown=!1,this.selector.style.cursor="-webkit-grab",this.drag.endX=e.pageX,this.drag.preventClick=!1,this.enableTransition(),this.updateAfterDrag(),this.clearDrag())},mousemoveHandler:function(e){if(e.preventDefault(),this.pointerDown){("A"===e.target.nodeName||this.preventClickOnDrag)&&(this.drag.preventClick=!0),this.drag.endX=e.pageX,this.selector.style.cursor="-webkit-grabbing";var t="all 0ms "+this.easing;this.sliderFrame.style.webkitTransition=t,this.sliderFrame.style.transition=t;var i=-1*(this.currentSlideIndex*this.getSlideWidth(!0)-(this.drag.endX-this.drag.startX));this.sliderFrame.style[this.transformProperty]="translate3d("+i+"px, 0, 0)"}},clickHandler:function(e){this.drag.preventClick&&e.preventDefault(),this.drag.preventClick=!1},updateAfterDrag:function(){var e=1*(this.drag.endX-this.drag.startX),t=Math.abs(e),i=this.multipleDrag?Math.ceil(t/this.getSlideWidth(!0)):1;0<e&&t>this.threshold&&this.slidesCount>this.perPage?this.prev(i):e<0&&t>this.threshold&&this.slidesCount>this.perPage&&this.next(i),this.slideToCurrent()},remove:function(e,t){if(e<0||e>=this.slidesCount)throw new Error("Item to remove doesn't exist");var i=e<this.currentSlideIndex,n=this.currentSlideIndex+this.perPage-1===e;(i||n)&&this.currentSlideIndex--,this.innerElements.splice(e,1),this.slidesCount=this.innerElements.length,this.buildSliderFrame(),t&&t.call(this)},insert:function(e,t,i){if(t<0||t>this.slidesCount+1)throw new Error("Unable to inset it at this index");if(-1!==this.innerElements.indexOf(e))throw new Error("Can't insert the same item");var n=0<(t<=this.currentSlideIndex)&&this.slidesCount;this.currentSlideIndex=n?this.currentSlideIndex+1:this.currentSlideIndex,this.innerElements.splice(t,0,e),this.slidesCount=this.innerElements.length,this.buildSliderFrame(),i&&i.call(this)},prepend:function(e,t){this.insert(e,0),t&&t.call(this)},append:function(e,t){this.insert(e,this.slidesCount+1),t&&t.call(this)},destroy:function(e,t){var i=e||!1;if(this.removeAllEventListeners(),this.selector.style.cursor="auto",i){for(var n=document.createDocumentFragment(),r=0;r<this.slidesCount;r++)n.appendChild(this.innerElements[r]);this.selector.innerHTML="",this.selector.appendChild(n),this.selector.removeAttribute("style")}t&&t.call(this)},enableTransition:function(){var e="transform "+this.duration+"ms "+this.easing;this.sliderFrame.style.webkitTransition=e,this.sliderFrame.style.transition=e},disableTransition:function(){var e="transform 0ms "+this.easing;this.sliderFrame.style.webkitTransition=e,this.sliderFrame.style.transition=e},clearDrag:function(){this.drag={startX:0,endX:0,startY:0,letItGo:null,preventClick:this.drag.preventClick}},getTransformProperty:function(){return"string"==typeof document.documentElement.style.transform?"transform":"WebkitTransform"}},Dribbble.ClickTracking={},Dribbble.ClickTracking.init=function(e){$(e||document).find("a[data-url]").each(function(){var e=$(this),t=e.data("url"),i=e.attr("href");e.data("url",i).attr("href",t)}).on("mousedown",function(){var e=$(this);e.attr("href",e.data("url"))})},Dribbble.ClickTracking.init(document),$(".psst .close").on("click",function(){var e=$(this).next(),t=e.next();$.post(e.data("url")),0==t.length?$(".psst").slideUp():e.fadeOut(function(){e.remove(),t.fadeIn()})}),Dribbble.Shots={data:{},loggingEnabled:!0,gaLoggingEnabled:!0,get:function(e){return this.data[e]},add:function(e){$(e).each(function(e,t){this.data[t.id]?this.update(t.id,t):this.data[t.id]=t}.bind(this))},update:function(e,t){var i=this.data[e];if(i){for(var n in t)i[n]=t[n];i.likeCount&&i.likeCount.render()}},clear:function(){this.data={}},idFromShotDiv:function(e){return e[0].id.split("-")[1]},logView:function(e){this.loggingEnabled&&(Dribbble.ShotViewRecorder.queueView(e),this.logAnalyticsView(e))},logViewForShotInDiv:function(e){this.logView(this.idFromShotDiv(e))},analyticsKeys:function(e){return this.gaLoggingEnabled&&this.data[e]?this.data[e].ga:[]},logAnalyticsView:function(e){for(var t=this.analyticsKeys(e),i=0;i<t.length;i++){var n=t[i],r=this.data[e];Dribbble.Analytics.logPageView(n[1],n[0],r.path,r.title)}},logAnalyticsLike:function(e,t){for(var i=this.analyticsKeys(e),n=0;n<i.length;n++){var r=i[n];Dribbble.Analytics.log(r[1],r[0],"event",{event_category:"Shots",event_action:t.action||"like",event_label:"Shot: "+e})}}},Dribbble.TeaserStats={init:function(e){var t=$("#main ol.dribbbles>li");$(e).each(function(e,t){var i=$("ol.dribbbles li#screenshot-"+t.id);0<t.rebounds_count?i.find(".has-rebounds").html(numberWithDelimiter(t.rebounds_count)).closest("a").show():t.is_rebound&&i.find(".is-rebound").closest("a").show(),0<t.attachments_count&&i.find(".attachments-mark").show(),i.find(".js-shot-views-container .js-shot-views-count").html(numberWithDelimiter(t.view_count)),i.find(".js-shot-comments-container").each(function(){$(this).find(".js-shot-comments-count").html(numberWithDelimiter(t.comments_count)).tipsy(Dribbble.defaultTipsyOptions)}),i.find(".timestamp").html(t.published_at),t.likeCount=new Dribbble.TeaserStats.LikeCount(t,i),t.likeCount.render()}),t.find(".js-shot-details-container").css({visibility:"visible"})}},Dribbble.TeaserStats.LikeCount=function(e,t){var i;return{initialized:!1,init:function(){this.initialized||(i=t.find(".js-shot-likes-container"),User.loggedIn()||t.find("a.like-shot").attr({"data-signup-trigger":"","data-context":"like-shot"}),this.initialized=!0)},render:function(){this.init(),i.find(".js-shot-likes-count").html(numberWithDelimiter(e.likes_count)),i.toggleClass("marked",e.liked)}}},"undefined"!=typeof newShots&&(Dribbble.Shots.add(newShots),Dribbble.TeaserStats.init(newShots)),Dribbble.String={capitalize:function(e){return e.charAt(0).toUpperCase()+e.slice(1)},kebabCaseToTitleCase:function(e){return e.split("-").join(" ").replace(/\w\S*/g,function(e){return this.capitalize(e)}.bind(this))}},Dribbble.AdNetworks=Dribbble.AdNetworks||{},Dribbble.AdNetworks.Boost={load:function(e){var t={impression_id:e.requestId,placement:Dribbble.String.kebabCaseToTitleCase(e.placement),provider:"Dribbble",unit_type:"Boosted Shots"};Dribbble.Itly.adRequested(t);var i=document.querySelector(".js-shot-page-ad-boosted");if(i){var n=i.dataset.boostUrl||"/screenshot_boost?request_source=Similar Work";$.ajax({type:"GET",url:n,success:function(e){$(".boosted-shot-container").html(e),Dribbble.AdNetworks.Ad.init({itlyTrackingEventProperties:t,adElement:i}),Dribbble.EventBus.$emit("boostedShotsAd:initialize")},error:function(){Dribbble.Itly.adRequestFailed(Object.assign(t,{reason:"No boosted shots available."})),i.style.display="none";var e=document.querySelector('.js-thumbnail[data-is-boost-fallback="true"]');e&&e.classList.remove("hidden-shot")}.bind(this)})}},logBoostedAdEvent:function(e,t){if(t&&t.ad_link){var i=t.ad_id;$.ajax({type:"POST",url:"/client_app/screenshot_boost_ad_events",data:{event_name:e,screenshot_boost_id:i,request_source:"Similar Work"}})}}},Dribbble.AdNetworks.Ad={init:function(e){if(this.adElement=e.adElement,this.hasBeenScrolledIntoView=!1,this.itlyTrackingEventProperties=e.itlyTrackingEventProperties,this.adElement){var t=this.adElement.querySelector("[data-ad-data]");if(t){var i;switch(this.adData=JSON.parse(t.dataset.adData),this.adData.ad_link_type){case"shot-page":i="Shot";break;case"profile":i="Profile";break;case"custom":default:i="Custom URL"}this.itlyAdData={ad_id:this.adData.ad_id.toString(),ad_link:this.adData.ad_link,ad_link_type:i,ad_text:this.adData.ad_text||"",has_cta:!1}}Dribbble.Itly.adServed(Object.assign(this.itlyTrackingEventProperties,this.itlyAdData)),Dribbble.AdNetworks.Boost.logBoostedAdEvent("ad_served",this.adData),this.bindEvents()}else Dribbble.Itly.adRequestFailed(Object.assign(this.itlyTrackingEventProperties,{reason:"Ad element was not found."}))},bindEvents:function(){this.adElement.querySelectorAll(".js-boosted-shot-link").forEach(function(e){e.onclick=this.onClickAd.bind(this)},this);var e=document.querySelector(".shot-overlay.overlay-visible .overlay-content");e?e.onscroll=function(){this.onScroll(e)}.bind(this):window.onscroll=function(){this.onScroll()}.bind(this)},onClickAd:function(){Dribbble.Itly.adClicked(Object.assign(this.itlyTrackingEventProperties,this.itlyAdData))},onScroll:function(){this.adElement&&(this.adElement.getBoundingClientRect().bottom<=window.innerHeight&&!this.hasBeenScrolledIntoView&&(this.hasBeenScrolledIntoView=!0,Dribbble.Itly.adImpressionViewed(Object.assign(this.itlyTrackingEventProperties,this.itlyAdData)),Dribbble.AdNetworks.Boost.logBoostedAdEvent("ad_impression_viewed",this.adData)))}},Dribbble.Hotkeys={listeners:{},KEY_ALIASES:{LEFT:37,RIGHT:39,ESCAPE:27,ENTER:13},initialize:function(){this.bindKeys()},bindKeys:function(){$(document).on("keyup",function(e){if(!this.isFormField(e.target.tagName)&&!this.isModifierKey(e)){var t=this.keyName(e.which);this.keyName&&this.trigger(t,e)}}.bind(this))},isFormField:function(e){return"INPUT"==e||"TEXTAREA"==e},isModifierKey:function(e){return e.ctrlKey||e.altKey||e.shiftKey||e.metaKey},keyName:function(e){var t=Dribbble.Hotkeys.KEY_ALIASES;for(var i in t)if(t[i]===e)return i;return String.fromCharCode(e)},map:function(e,t,i){return this.listeners[e]&&this.listeners[e].off(),this.listeners[e]=t,this.listeners[e].isApplicable=i||function(){return!0},this.listeners[e].off=function(){delete this.listeners[e]}.bind(this),this.listeners[e]},trigger:function(e,t){for(var i in this.listeners){var n=this.listeners[i];n[e]&&n.isApplicable()&&n[e](t)}}},Dribbble.Hotkeys.TextAreaSubmission={eventName:"keydown.keyboardSubmit",shortcutCopy:(navigator.userAgent.match(/Macintosh/)?"\u2318":"ctrl")+"+enter",enable:function(){$(document).off(this.eventName).on(this.eventName,"textarea",function(e){(e.metaKey||e.ctrlKey)&&e.keyCode==Dribbble.Hotkeys.KEY_ALIASES.ENTER&&(e.preventDefault(),$(this).closest("form").trigger("submit"))}),this.fillShortcuts()},fillShortcuts:function(){$(".keyboard-submit-shortcut").text(this.shortcutCopy)}},Dribbble.Hotkeys.initialize(),Dribbble.Hotkeys.TextAreaSubmission.enable(),function(){var i,e=function(e){var t=$.trim($(".pagination:last").find(e).attr("href"));t&&(i.off(),document.location=t)};i=Dribbble.Hotkeys.map("list",{LEFT:function(){e(".previous_page")},RIGHT:function(){e(".next_page")}},function(){return!(!(0<$(".previous_page:visible, .next_page:visible").length)||Dribbble.Overlay&&Dribbble.Overlay.anyOpen()||Dribbble.ShotOverlay&&Dribbble.ShotOverlay.isOpen)})}(),Dribbble.Router={initialize:function(){Dribbble.isHistorySupported&&History.Adapter.bind(window,"statechange",function(){var e=document.location.pathname,t=/\/attachments(\/|$)/;$(".attachment-overlay").is(":visible")?e.match(t)?Dribbble.Attachments.showOverlay(document.location.toString()):Dribbble.Attachments.hideOverlay():e.match(t)?void 0!==Dribbble&&Dribbble.Attachments?Dribbble.Attachments.showOverlay(document.location.toString()):window.location.reload(!0):$("#viewer").length?window.location.reload(!0):Dribbble.GoodOverlay||Dribbble.ShotOverlay&&Dribbble.ShotOverlay.active?e.match(/^\/shots\/\d+[^/]+$/)?Dribbble.ShotOverlay.show(document.location.toString(),Dribbble.Router.referer):e.match(/^\/goods\/\d+[^/]+$/)?Dribbble.GoodOverlay.show(document.location.toString(),Dribbble.Router.referer):Dribbble.GoodOverlay&&Dribbble.GoodOverlay.isOpen()?Dribbble.GoodOverlay.hide():Dribbble.ShotOverlay.hide():Dribbble.ShotOverlayModal&&Dribbble.ShotOverlayModal.active?e.match(/^\/shots\/\d+[^/]+$/)?Dribbble.ShotOverlayModal.show(document.location.toString()):Dribbble.ShotOverlayModal.hide():e.match(/\/scout/)?window.location.reload(!0):e.match(/^\/admin\/talent/)?$(document).scrollTop(0):($(document).scrollTop(0),window.location.reload(!0)),history.state!=undefined&&history.state.title!=undefined&&(document.title=history.state.title)})},go:function(e){history.go(e)},pushState:function(e,t,i){this.referer=window.location.toString(),History.pushState(e,t,i)},replaceState:function(e,t,i){history.replaceState(e,t,i),document.title=t}},Dribbble.Router.initialize(),function(e){var t=!1;if("function"==typeof define&&define.amd&&(define(e),t=!0),"object"==typeof exports&&(module.exports=e(),t=!0),!t){var i=window.Cookies,n=window.Cookies=e();n.noConflict=function(){return window.Cookies=i,n}}}(function(){function m(){for(var e=0,t={};e<arguments.length;e++){var i=arguments[e];for(var n in i)t[n]=i[n]}return t}function e(p){function b(e,t,i){var n;if("undefined"!=typeof document){if(1<arguments.length){if("number"==typeof(i=m({path:"/"},b.defaults,i)).expires){var r=new Date;r.setMilliseconds(r.getMilliseconds()+864e5*i.expires),i.expires=r}i.expires=i.expires?i.expires.toUTCString():"";try{n=JSON.stringify(t),/^[\{\[]/.test(n)&&(t=n)}catch(f){}t=p.write?p.write(t,e):encodeURIComponent(String(t)).replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g,decodeURIComponent),e=(e=(e=encodeURIComponent(String(e))).replace(/%(23|24|26|2B|5E|60|7C)/g,decodeURIComponent)).replace(/[\(\)]/g,escape);var a="";for(var o in i)i[o]&&(a+="; "+o,!0!==i[o]&&(a+="="+i[o]));return document.cookie=e+"="+t+a}e||(n={});for(var s=document.cookie?document.cookie.split("; "):[],l=/(%[0-9A-Z]{2})+/g,c=0;c<s.length;c++){var d=s[c].split("="),u=d.slice(1).join("=");'"'===u.charAt(0)&&(u=u.slice(1,-1));try{var h=d[0].replace(l,decodeURIComponent);if(u=p.read?p.read(u,h):p(u,h)||u.replace(l,decodeURIComponent),this.json)try{u=JSON.parse(u)}catch(f){}if(e===h){n=u;break}e||(n[h]=u)}catch(f){}}return n}}return(b.set=b).get=function(e){return b.call(b,e)},b.getJSON=function(){return b.apply({json:!0},[].slice.call(arguments))},b.defaults={},b.remove=function(e,t){b(e,"",m(t,{expires:-1}))},b.withConverter=e,b}return e(function(){})}),Dribbble.ShotViewRecorder={seenShotIds:[],recordedIds:[],recordingDelay:3e3,recordingMaxQueueDepth:10,recordingTimeoutId:null,submitting:!1,queueView:function(e){-1==this.seenShotIds.indexOf(e)&&this.seenShotIds.push(e),this.queueRecordViewedShots()},queueRecordViewedShots:function(){clearTimeout(this.recordingTimeoutId);var e=this.recordingDelay;this.seenShotIds.length-this.recordedIds.length>=this.recordingMaxQueueDepth&&(e=0),this.recordingTimeoutId=setTimeout(function(){this.submitting?this.queueRecordViewedShots():this.recordViewedShots()}.bind(this),e)},recordViewedShots:function(){this.submitting=!0;var e=$(this.seenShotIds).not(this.recordedIds).get();if(0<e.length){var t=$.post("/shots/log_views",{ids:e});t.done(function(){this.recordedIds=this.recordedIds.concat(e)}.bind(this)),t.always(function(){this.submitting=!1}.bind(this))}else this.submitting=!1},getFeedLocation:function(){var e=window.location.pathname,t={list:document.body.id};switch(t.list){case"home":t.list="following";break;case"colors":t.color=e.slice(1).split("/")[1];break;case"places":t.place=e.slice(1).split("/")[1];break;case"page":0<e.indexOf("lists")&&(t.list="list");break;case"shots":params=window.location.search.slice(1),params.length&&params.split("&").forEach(function(e){e=e.split("="),-1<["list","sort","timeframe"].indexOf(e[0])&&(t[e[0]]=e[1])}),t.sort||(t.sort="popular");break;case"profile-secondary":0<e.indexOf("buckets")?t.list="bucket":0<e.indexOf("goods")?t.list="goods":0<e.indexOf("likes")&&(t.list="likes")}return t}},Dribbble.ButtonDropdown={buttonDropdowns:document.querySelectorAll(".btn-dropdown"),buttonDropdownLinks:document.querySelectorAll(".btn-dropdown-link"),initialize:function(){this.buttonDropdowns.length&&this.buttonDropdownLinks.length&&(this.bindEventListeners(),this.buttonDropdownLinks.forEach(function(e){var t=e.querySelector("span");e.dataset.dropdownState="closed",t&&t.dataset.fadeDefault&&t.innerText.trim()==t.dataset.prompt&&t.classList.add("default-option")}))},bindEventListeners:function(){0!==this.buttonDropdownLinks.length&&this.buttonDropdownLinks.forEach(this._bindEachDropdown.bind(this))},addLinks:function(e,t){this.buttonDropdownLinks=Array.prototype.slice.call(this.buttonDropdownLinks).concat(t),this.buttonDropdowns=Array.prototype.slice.call(this.buttonDropdowns).concat(e),t.forEach(this._bindEachDropdown.bind(this))},closeAll:function(){this.buttonDropdowns.forEach(function(e){e.classList.contains("open")&&(e.classList.remove("open"),e.querySelector(".btn-dropdown-link").dataset.dropdownState="closed")})},closeAllWhenClickedOutside:function(t){var i=!1;this.buttonDropdownLinks.forEach(function(e){e.contains(t.target)&&(i=!0)}),i||this.closeAll()},resetDropdown:function(e,t){e.querySelectorAll(".active").forEach(function(e){e.classList.remove("active")});var i=e.querySelector(".btn-dropdown-link span");i.dataset.fadeDefault&&i.classList.add("default-option"),i.innerText=t||i.dataset.prompt,e.querySelector(".btn-dropdown-options .default-filter-option").classList.add("active")},updateDropdown:function(e,t){if(t){var i=e.querySelector('[data-value="'+t+'"] a');i&&this._selectOption({target:i})}else this.resetDropdown(e)},click:function(e,t){t||(t={bubbles:!0,cancelable:!1});var i=document.querySelector(e);i&&(event=new MouseEvent("click",t),i.dispatchEvent(event))},_bindEachDropdown:function(e){e.addEventListener("click",this._openDropdown.bind(this)),e.nextElementSibling.querySelectorAll("a").forEach(this._bindEachOption.bind(this))},_bindEachOption:function(e){e.addEventListener("click",this._selectOption.bind(this))},_selectOption:function(e,t){var i=e.target,n=i.closest(".btn-dropdown"),r=n.querySelector("span"),a=n.querySelector(".active");a&&a.classList.remove("active"),r&&(r.innerHTML=t||i.innerHTML,r.dataset.fadeDefault&&r.innerText.trim()==r.dataset.prompt?r.classList.add("default-option"):r.classList.remove("default-option")),i.parentNode.classList.add("active")},_openDropdown:function(e){e.preventDefault(),e.currentTarget.dataset.dropdownState="open"==e.currentTarget.dataset.dropdownState?"closed":"open";var t=e.target.closest(".btn-dropdown-link"),i=t.parentNode.classList.contains("open");this.closeAll(),i||(document.addEventListener("click",this.closeAllWhenClickedOutside.bind(this)),t.parentNode.classList.add("open"))}},Dribbble.pathName=function(e){var t=document.createElement("a");t.href=e;var i=t.pathname;return"/"!=i.charAt(0)?"/"+i:i},Dribbble.Analytics={existingProperties:[],sentEvents:[],reset:function(){this.existingProperties=[],this.sentEvents=[],this.namedRoot=null,this.googleAnalyticsKey=null},logDribbbleGAPageView:function(){gtag("config",this.googleAnalyticsKey)},logPageView:function(e,t,i,n){this.log(e,"page_view","event",{page_path:i,page_title:n})},log:function(e,t,i,n,r){if("undefined"==typeof ga&&"undefined"==typeof gtag)(r=2*(r||50))<1e4&&setTimeout(function(){this.log(e,t,i,n,r)}.bind(this),r);else{var a={send_to:e,name:t};gtag(i,t,Object.assign(a,n))}}},Dribbble.Media||(Dribbble.Media={}),Dribbble.Media.Load=function(e,t){if(this.mediaContainer=e,this.loadedCallback=t||null,(this.mediaType=null)===this.mediaContainer&&this.mediaContainer.dataset.lazy)throw new Error("Something wrong with the mediaElement");if(this.imageElement=this.mediaContainer.querySelector("img"),this.videoElement=this.mediaContainer.querySelector("video"),this.imageElement&&(this.mediaType="image"),this.videoElement&&(this.mediaType="video"),!this.mediaType)throw new Error("Could not find images or videos inside mediaElement");this.replaceSource()},Dribbble.Media.Load.prototype={replaceSource:function(){this.mediaContainer.querySelectorAll("source, img, video").forEach(function(e){var t=e.dataset.srcset||null,i=e.dataset.src||null;t&&(e.setAttribute("srcset",t),e.removeAttribute("data-srcset")),i&&(e.setAttribute("src",i),e.removeAttribute("data-src"))}),this.handleImageLoad()},handleImageLoad:function(){var e=this;if("image"===this.mediaType){var t=function(){
e.mediaContainer.removeAttribute("data-lazy"),e.loadedCallback&&e.loadedCallback(e.mediaContainer)};this.imageElement?Dribbble.imageOnLoad(this.imageElement,t):t()}"video"===this.mediaType&&(this.videoElement.onloadstart=function(){e.mediaContainer.removeAttribute("data-lazy")})}},Dribbble.Media||(Dribbble.Media={}),Dribbble.Media.LoadingIndicator=function(e){var t=[24,48,72,96],i=t.length,n=!1;return{activate:function(){n=!0,this.show(),this.progress()},deactivate:function(){n=!1,e.style.backgroundPositionY="0px"},hide:function(){e.style.display="none"},progress:function(){n&&(0<i?i-=1:i=t.length,e.style.backgroundPositionY=t[i]+"px",setTimeout(this.progress.bind(this),250))},show:function(){e.style.display="block"}}},Dribbble.AnimatedGifSwap={animatedUrl:function(e){return e.replace(/(.*)_(1x|still_2x|still|teaser)(.*?)$/,"$1$3")},attach:function(e,t){$(document).on({mouseenter:function(e){this.swapperFor($(e.currentTarget),t).activate()}.bind(this),mouseleave:function(e){this.swapperFor($(e.currentTarget)).deactivate()}.bind(this)},e)},swapperFor:function(e,t){if(!e.data("gif-swapper")){var i=e;t.parentSelector&&(i=e.parents(t.parentSelector)),e.data("gif-swapper",new Dribbble.AnimatedGifSwap.Swapper(i,t.picSelector))}return e.data("gif-swapper")}},Dribbble.AnimatedGifSwap.Swapper=function(e,t){var i=e.find(t),n=i.find("img"),r=n.data("animated-url")||null,a=new Dribbble.Media.LoadingIndicator(e.find(".loading-indicator")[0]);return{$shot:e,$imageContainer:i,$image:n,loaded:!1,hovering:!1,animatedSrc:r||Dribbble.AnimatedGifSwap.animatedUrl(i.find("img").attr("src")),activate:function(){this.hovering=!0,this.swapImage()},deactivate:function(){this.hovering=!1,this.restoreImage(),a.deactivate(),a.show()},loadImageInBackground:function(e,t){var i=new Image;i.onload=function(){this.loaded=!0,t()}.bind(this),i.src=e},restoreImage:function(){this.$imageContainer.find("source, img").each(function(e,t){this.revertSrc($(t))}.bind(this)),"undefined"!==n.data("srcset")&&this.loaded&&this.revertSrcset(n.data("srcset"))},swapImage:function(){this.loaded?(this.$imageContainer.find("source, img").each(function(e,t){this.updateSrc($(t))}.bind(this)),Dribbble.Shots.logViewForShotInDiv(e),a.deactivate(),a.hide()):(a.activate(),this.loadImageInBackground(this.animatedSrc,function(){this.hovering&&this.swapImage()}.bind(this)))},updateSrc:function(e){var t=this.srcAttr(e),i=e.attr(t);(e.is("img")&&e.attr("srcset")?e.attr("srcset"):null)&&e.removeAttr("srcset"),void 0===e.attr("data-originalsrc")&&e.attr("data-originalsrc",i),e.attr(t,this.animatedSrc)},revertSrc:function(e){e.attr(this.srcAttr(e),e.attr("data-originalsrc"))},revertSrcset:function(e){this.$image.attr("srcset",e)},srcAttr:function(e){return e.is("img")?"src":"srcset"}}},Dribbble.MediaSwap=function(){var e=function(e){if(this.selector=e,this.elements=document.querySelectorAll(e),this.active=!1,!Dribbble.isMobile()){for(var t=0;t<this.elements.length;t++)el=this.elements[t],el.triggerTarget||(el.loaded=!1,el.triggerTarget=el,el.triggerTarget.parentContainer=el,el.triggerTarget.loadingIndicator=new Dribbble.Media.LoadingIndicator(el.querySelector(".loading-indicator")),el.triggerTarget.addEventListener("mouseenter",i.mouseenter),el.triggerTarget.addEventListener("mouseleave",i.mouseleave));this.active=!0}},i={mouseenter:function(){var e=function(){this.loaded=!0,this.loadingIndicator.deactivate(),this.loadingIndicator.hide()};if(!this.parentContainer.video){var t=document.createElement("video");(this.parentContainer.video=t).className="dribbble-video",t.muted=!0,t.autoplay=!0,t.loop=!0,this.parentContainer.offsetWidth<400?t.src=this.parentContainer.dataset.videoTeaserSmall:this.parentContainer.offsetWidth<800?t.src=this.parentContainer.dataset.videoTeaserMedium:t.src=this.parentContainer.dataset.videoTeaserLarge,this.loaded||t.addEventListener("canplaythrough",e.bind(this)),this.parentContainer.appendChild(t),this.parentContainer.insertBefore(t,this.parentContainer.firstChild)}this.parentContainer.video.paused&&this.parentContainer.video.play()["catch"](function(){}),this.parentContainer.video.style.display="block",this.parentContainer.video.style.visibility="visible",this.loaded?(this.loadingIndicator.deactivate(),this.loadingIndicator.hide()):this.loadingIndicator.activate()},mouseleave:function(){if(!this.parentContainer.video.paused)try{this.parentContainer.video.pause()}catch(e){}this.parentContainer.video.style.display="none",this.parentContainer.video.style.visibility="hidden",this.loadingIndicator.deactivate(),this.loadingIndicator.show()}};return e}(),Dribbble.HoverCards={fetchDelay:500,displayDelay:1e3,pendingDisplayId:null,pendingFetchId:null,tooCloseToTop:350,cache:{},displayedURL:null,dismissed:!1,cardBoundaries:null,wrapper:null,initialize:function(e){this.scopeSelector=e,this.bindEventListeners()},bindEventListeners:function(){$(this.scopeSelector).on("mouseenter.hovercard","a.hoverable",this.mouseenter.bind(this))},findCard:function(){var e="new-hover-card",t=$("."+e);return 0<t.length?t:($("body").prepend('<div class="'+e+'"></div>'),new Dribbble.FollowButton($("."+e),function(e){return e.parent().find(".followers")}).initialize(),this.findCard())},mouseenter:function(e){if(!e.ctrlKey&&!e.metaKey){var t=$(e.currentTarget),i=(t.data("card")||t.attr("href"))+"/hover_card",n=this.displayedURL!=i;if(this.displayedURL)if(n)this.dismiss();else if(!this.dismissed)return!1;this.displayedURL=i,this.dismissed=!1;var r=+new Date,a=this.findCard();return this.wrapper=t.closest(".hover-card-parent"),0==this.wrapper.length&&(this.wrapper=t.parent()),this.addHandlerForDismissal(),this.cache[i]?(n&&a.html(this.cache[i]),this.delayedShow(a,r)):this.delayedFetch(a,i,r),!1}},dismiss:function(){this.dismissed&&!this.pendingDisplayId||(this.dismissed=!0,clearTimeout(this.pendingFetchId),clearTimeout(this.pendingDisplayId),this.pendingFetchId=this.pendingDisplayId=null,this.cardBoundaries=null,$(this.scopeSelector).off("mousemove.hovercard"),this.findCard().hide())},delayedShow:function(e,t){var i=this.displayDelay-(+new Date-t);this.triggerBoundaries=this.getBoundariesForElement(this.wrapper);var n=this.triggerBoundaries.left+e.outerWidth(),r=this.wrapper[0].getBoundingClientRect().top;e.toggleClass("center",n>document.documentElement.clientWidth),e.toggleClass("top",r<this.tooCloseToTop);var a={left:this.triggerBoundaries.left,bottom:"",top:""};r<this.tooCloseToTop?a.top=this.triggerBoundaries.top+this.wrapper.outerHeight():a.bottom=$(window).height()-this.triggerBoundaries.top,e.css(a),clearTimeout(this.pendingDisplayId),this.pendingDisplayId=setTimeout(function(){User.loggedOut()&&Dribbble.SignupModal.overlay.bindTrigger(),e.show(),this.cardBoundaries=this.getBoundariesForElement(e),r<this.tooCloseToTop?this.cardBoundaries.top-=this.wrapper.outerHeight():this.cardBoundaries.bottom+=this.wrapper.outerHeight()}.bind(this),i)},delayedFetch:function(t,i,n){clearTimeout(this.pendingFetchId),this.pendingFetchId=setTimeout(function(){this.dismissed||$.get(i,function(e){this.dismissed||(t.html(e).find("[rel=tipsy]").removeAttr("rel"),this.cache[i]=t.html(),this.delayedShow(t,n))}.bind(this))}.bind(this),this.fetchDelay)},checkShouldDismissOnMouseMove:function(e){this.mouseIn(e,this.cardBoundaries)||this.wrapper.is(":hover")||this.dismiss()},getBoundariesForElement:function(e){var t=e.offset();return t.right=e.outerWidth()+t.left,t.bottom=e.outerHeight()+t.top,t},mouseIn:function(e,t){return null!=t&&e.pageX>=t.left&&e.pageX<=t.right&&e.pageY>=t.top&&e.pageY<=t.bottom},addHandlerForDismissal:function(){this.removeHandlerForDismissal(),$(this.scopeSelector).on("mousemove.hovercard",this.checkShouldDismissOnMouseMove.bind(this))},removeHandlerForDismissal:function(){$(this.scopeSelector).off("mousemove.hovercard")}},Dribbble.isMobile()||Dribbble.HoverCards.initialize("body"),Dribbble.Url={addParams:function(e,t){var i=e.split("#"),n=i[0]+this.urlJoinCharacter(e),r=i[1],a=[];($.isArray(t)?t:Object.keys(t).map(function(e){return[e,t[e]]})).forEach(function(e){var t=e[0],i=e[1];$.isArray(i)?i.forEach(function(e){a.push(t+"[]="+encodeURIComponent(e))}):a.push(t+"="+encodeURIComponent(i))});var o=n+a.join("&");return r&&(o+="#"+r),o},parse:function(e){var t=document.createElement("a");return t.href=e,t},urlJoinCharacter:function(e){return-1==e.indexOf("?")?"?":"&"},extractParams:function(e){return e.split("?")[1]},getUrlParamByName:function(e,t){t=t||window.location;var i=RegExp("[?&]"+e+"=([^&]*)").exec(t.search);return i&&decodeURIComponent(i[1].replace(/\+/g," "))}},Dribbble.SingleSubmit={},Dribbble.SingleSubmit.disableForm=function(e,t){t=t||"Processing\u2026",e.attr("disabled","disabled"),e.find("button[type=submit], input[type=submit]").attr("disabled","disabled"),e.find("button[type=submit]").each(function(e,t){$(t).data("original-preprocessing-text",$(t).text())}).text(t),e.find("input[type=submit]").each(function(e,t){$(t).data("original-preprocessing-text",$(t).val())}).val(t)},Dribbble.SingleSubmit.enableForm=function(e){e.removeAttr("disabled"),e.find("button[type=submit], input[type=submit]").removeAttr("disabled","disabled"),e.find("button[type=submit]").each(function(e,t){$(t).text($(t).data("original-preprocessing-text")||"Submit")}),e.find("input[type=submit]").each(function(e,t){$(t).val($(t).data("original-preprocessing-text")||"Submit")})},Dribbble.SingleSubmit.bind=function(e,i){var t=$(e);t.data("single-submit")||(t.on("submit.singleSubmit",function(e){var t=$(e.currentTarget);Dribbble.SingleSubmit.disableForm(t,i)}),t.data("single-submit",!0))},Dribbble.Autocomplete=function(e,r,a){var o,s=$(e),t=function(){if(s.autocomplete(r),o=s.data("uiAutocomplete"),r.renderer&&(o._renderItem=r.renderer),r.keypress&&s.on("keypress.autocomplete",r.keypress),r.stayOpenOnMetaSelect){var t,i=o.menu.select;o.menu.select=function(e){t=e.shiftKey||e.ctrlKey||e.metaKey,i.apply(this,arguments)};var n=o._close;o._close=function(e){return!t&&n.apply(this,arguments)}}a&&a(o)};if(s.autocomplete)t(s);else{var i="keypress.autocomplete.preload";s.on(i,function(){s.off(i),$.getScript("/assets/jquery-ui-1.12.1-d981b6dbbbd74051ddbc7410e3794192a3f41b948ad0fc28bad142287d811f6b.js",function(){t(),s.autocomplete("search",s.val())})})}},Dribbble.AutocompleteCache=function(){this.content={}},Dribbble.AutocompleteCache.prototype={lookup:function(t,i,e){if(this.content.hasOwnProperty(t))i(this.content[t]);else{var n=function(e){this.content[t]=e,i(e)}.bind(this);e(t,n)}},termHasNoResults:function(e){return this.content[e]!=undefined&&0==this.content[e].length},isDeadEnd:function(e){if(this.termHasNoResults(e))return!0;for(var t=1;t<e.length+1;t++)if(this.termHasNoResults(e.substring(0,t)))return!0;return!1}},Dribbble.CartV2={initialize:function(e){this.$table=$("table[data-payment-table]"),this.setLineItems(e);var t=$("#postal_code, #backfill_postal_code, #existing_postal_code");this.$postalCodeField=t,this.$postalCodeField.on("input.postal-code",this.updatePostalCode.bind(this));var i=t.filter("#existing_postal_code");i.length&&i.trigger("input.postal-code"),Dribbble.SingleSubmit.bind($("form.create-order")),this.active=!0},setLineItems:function(e){this.lineItems=e,this.renderPriceTable()},calculatePriceData:function(e){var t=this.lineItems.slice(0);return e&&t.push(e),t},sumPrices:function(e){var t=0;return e.forEach(function(e){t+=100*Dribbble.Globals.truncatePrice(e)}),t/100},sumCart:function(e){var t=this.calculatePriceData(e).map(function(e){return e.price});return this.sumPrices(t)},postalCodeTaxes:function(e){var t={amount:0,type:"error",name:"Incorrect postal code value"};return{a:{amount:13,type:"HST",name:"Newfoundland and Labrador",abbrev:"NL"},b:{amount:15,type:"HST",name:"Nova Scotia",abbrev:"NS"},c:{amount:15,type:"GST",name:"Prince Edward Island",abbrev:"PE"},e:{amount:13,type:"HST",name:"New Brunswick",abbrev:"NB"},g:{amount:5,type:"GST",name:"Quebec",abbrev:"QC"},h:{amount:5,type:"GST",name:"Quebec",abbrev:"QC"},j:{amount:5,type:"GST",name:"Quebec",abbrev:"QC"},k:{amount:13,type:"HST",name:"Ontario",abbrev:"ON"},l:{amount:13,type:"HST",name:"Ontario",abbrev:"ON"},m:{amount:13,type:"HST",name:"Ontario",abbrev:"ON"},n:{amount:13,type:"HST",name:"Ontario",abbrev:"ON"},p:{amount:13,type:"HST",name:"Ontario",abbrev:"ON"},r:{amount:5,type:"GST",name:"Manitoba",abbrev:"MB"},s:{amount:5,type:"GST",name:"Saskatchewan",abbrev:"SK"},t:{amount:5,type:"GST",name:"Alberta",abbrev:"AB"},v:{amount:5,type:"GST",name:"British Columbia",abbrev:"BC"},x:{amount:5,type:"GST",name:"Northwest Territories and Nunavut",abbrev:"NT"},y:{amount:5,type:"GST",name:"Yukon",abbrev:"YT"}}[e]||t},checkPostalCode:function(e){var t=function(e){var t=e.split(""),i=6===t.length,n=/^[a-z]+$/.test([t[0],t[2],t[4]].join("")),r=/^\d+$/.test([t[1],t[3],t[5]].join(""));return i&&n&&r},i=function(e){return e.replace(/\W/g,"").toLowerCase()}(e);return t(i)?this.postalCodeTaxes(i.charAt(0)):null},update:function(e){this.lineItems[0].price=e,this.$postalCodeField.filter(":visible").length?this.$postalCodeField.filter(":visible").trigger("input.postal-code"):this.$postalCodeField.filter("[type=hidden]").trigger("input.postal-code")},updatePostalCode:function(e){var t=$(e.target).val(),i=this.sumCart(),n=this.checkPostalCode(t),r=null;n&&0<i&&(r={price:i*(.01*n.amount),description:n.amount+"% "+n.type+" tax"}),this.renderPriceTable(r)},renderPriceTable:function(e){this.$table.empty();var i=[],t=0,n=this.calculatePriceData(e),r=[];if($(n).each(function(e,t){i.push(this.addRow(t)),t.price=parseFloat(t.price),r.push(t.price)}.bind(this)),t=0<(t=this.sumPrices(r))?t:0,1<n.length){var a=$("<td>").append("Total:"),o=$("<strong>").append(Dribbble.Globals.formatPrice(t)).addClass("total text-dark text-weight-500"),s=$("<td>").append(o).addClass("num");i.push($("<tr>").addClass("total").append(a).append(s))}$(".paid").toggle(0<t),"undefined"!=typeof Card&&(Card.enabled=0<t),this.$table.append(i),$(".recurring-price").html(Dribbble.Globals.formatPrice(t)),$(document).trigger("dribbble.cartv2.renderPrice",{taxData:e,total:t})},addRow:function(e){var t=e.priceDescription||Dribbble.Globals.formatPrice(e.price),i=$("<td>").append(e.description),n=$("<td>").append(t).addClass("num");return $("<tr>").append(i).append(n)}},Dribbble.Recaptcha={initialized:!1,initialize:function(){if(!this.initialized){var e=setTimeout(function(){this.loaded()||this.showLoadError()}.bind(this),1e4);this.hideCaptchaNotLoaded=function(){clearInterval(e),$(".captcha-not-loaded").hide()},$.getScript("https://www.google.com/recaptcha/api.js",this.hideCaptchaNotLoaded),Dribbble.captchaSuccess=Dribbble.captchaSuccess||function(e){e.currentTarget.submit()},this.initialized=!0,this.bindEventListeners()}},bindEventListeners:function(){var t=$(".g-recaptcha").parents("form");t.one("submit.recaptcha",function(e){e.preventDefault(),window.captchaCallback=function(){this.hideCaptchaNotLoaded(),t.off("submit.recaptcha").on("submit",Dribbble.captchaSuccess),Dribbble.captchaSuccess(e)}.bind(this),this.loaded()?grecaptcha.execute():this.showLoadError()}.bind(this))},loaded:function(){return"undefined"!=typeof grecaptcha},showLoadError:function(){$(".captcha-not-loaded").show()}},Dribbble.SignupModalForm=function(e){this.$el=e,this.$errorContainer=this.$el.children(".errorExplanation"),this.$errorsList=this.$errorContainer.children("ul")},Dribbble.SignupModalForm.prototype={show:function(){this.$el.show()},submit:function(e){this.resetErrors();var t=this.$el.serializeArray();for(var i in e)e.hasOwnProperty(i)&&t.push({name:i,value:e[i]});var n=$.post(this.$el.attr("action"),t);n.done(this.onSuccess.bind(this)),n.fail(this.onFailure.bind(this))},onSuccess:function(e){window.location=e.redirect_to},onFailure:function(e){var t=JSON.parse(e.responseText).errors.map(function(e){return $("<li />").text(e)});this.$errorsList.append(t),this.$errorContainer.show()},resetErrors:function(){this.$errorsList.html(""),this.$errorContainer.hide()}},Dribbble.SignupModal={overlay:null,originalPitchContent:null,defaultParams:{signup:!0},variant:null,triggerData:{},signupReasons:{"like-shot":"To like a shot, please create an account.","view-shot":"To view this shot in detail, please create an account.","member-profile":"To view this profile, please create an account.","more-from-user":"To view more shots from this user, please create an account.","bucket-shot":"To save a shot, please create an account.","follow-user":"To follow this user, please create an account.","message-user":"To message this user, please create an account.","list-user":"To add this user to a list, please create an account.","share-overtime":"Keep up with the latest Overtime episodes by joining Dribbble today!","share-courtside":"Keep up with the latest Courtside posts by joining Dribbble today!","create-team":"Before purchasing a team, please create an account.","shot-navigation":"To browse more shots, please create an account.","more-profile-shots":"To view more shots from this user, please create an account.","logged-out-message":"To send this inquiry, please sign in or create an account.","new-pro-logged-out":"To sign up as a pro, please sign in or create an account.","shot-page-feed":"To like shots, follow users, and more, please create an account.","download-attachment":"To download attachments, please sign in or create an account.","global-survey-2019":"To download this report, please sign in or create an account."},initialize:function(){this.resetParams(),this.$el=$("#signup-overlay"),this.originalPitchContent=this.getPitch(),this.overlay=new Dribbble.Overlay.Simple({$el:this.$el,onHide:this.onHide.bind(this),remoteContent:{el:this.$el.find(".display"),url:"/signup/modal?return_to="+window.location.href,onComplete:function(e,t,i){Dribbble.Recaptcha.initialize(),Dribbble.captchaSuccess=function(e){e.preventDefault(),this.form.submit(this.additionalParams)}.bind(this),200==i.status?this.bindFormEventListeners():205==i.status?(this.$el.find(".display").html("Signing you in, please wait\u2026"),window.location.reload()):window.location="/signup"}.bind(this)}}),this.bindEventListeners()},open:function(e){this.triggerData=e,this.overlay.show(),this.triggerData={}},bindEventListeners:function(){0!=this.$el.length&&this.bindOnceListeners()},bindFormEventListeners:function(){var t=this.$el.find("#new_user"),i=this.$el.find(".email-signup-toggle");this.form=new Dribbble.SignupModalForm(t),i.on("click",function(e){e.preventDefault(),i.addClass("on")}),t.on("click",function(e){i.addClass("on"),$(e.currentTarget).hasClass("ab-2")||t.off("click")}),this.$el.find(".auth-google").on("click",function(e){Dribbble.Itly.googleAuthenticationStarted({trigger:"google",object_type:"Modal",type:e.currentTarget.authAction||"Sign Up"})}),this.$el.find(".auth-twitter").on("click",function(e){Dribbble.Itly.twitterAuthenticationStarted({trigger:"twitter",object_type:"Modal",type:e.currentTarget.dataset.authAction||"Sign Up"})}),this.$el.find("#already-a-member").on("click",function(){$("#signup-fields").addClass("hide"),$("#signin-fields").removeClass("hide")}),this.$el.find("#not-a-member").on("click",function(){$("#signin-fields").addClass("hide"),$("#signup-fields").removeClass("hide")});var e=this.getTriggerData();if(e.signupPitchContent){var n=$("#"+e.signupPitchContent).html();this.setPitch(n)}var r=e.returnTo||window.location;if(this.setParam("return_to",r),e.params)for(var a in e.params)e.params.hasOwnProperty(a)&&this.setParam(a,e.params[a]);var o=e.reason||this.signupReasons[e.context];o?this.$el.find(".signup-reason").attr("data-reason",e.reason).text(o).addClass("visible"):this.$el.find(".signup-reason").removeAttr("data-reason").empty().removeClass("visible"),this.updateSocialAuthLinks(),this.overlay.trackingData={name:"sign up",context:e.context},Dribbble.Itly.authenticationViewed({object_type:"Modal",trigger:e.context||"unknown",type:window.returningVisitor?"Sign In":"Sign Up"})},bindOnceListeners:function(){this.isBound||(this.isBound=!0,$(document.body).on("click","[data-signup-trigger]",this.overlay.triggerHandler.bind(this.overlay)))},getTriggerData:function(){return $(this.overlay.currentTarget).data()||this.triggerData},onHide:function(){this.restoreContent(),this.bindEventListeners(),this.$el.find(".signup-reason").removeClass("visible")},setParam:function(e,t){this.additionalParams[e]=t},setPitch:function(e){this.$el.find(".pitch-column").html(e)},getPitch:function(){return this.$el.find(".pitch-column").html()},resetParams:function(){this.additionalParams=this.defaultParams},restoreContent:function(){this.setPitch(this.originalPitchContent),this.resetParams()},setContext:function(){var e=document.querySelector(".signup-reason").innerText;if(e!==undefined)for(var t in this.signupReasons)if(this.signupReasons[t]==e)return t},updateSocialAuthLinks:function(){this.$el.find("a.social-signup").each(function(e,t){var i=$(t);i.data().originalUrl||i.data("originalUrl",i.attr("href"));var n=i.data().originalUrl;i.attr("href",Dribbble.Url.addParams(n,this.additionalParams))}.bind(this))}},Dribbble.SignupModal.initialize(),Dribbble.ResizingSelect={bindEventListeners:function(){$(document).on("change",".resizing-select",function(e){this.resizeSelect(e.currentTarget)}.bind(this)),this.trigger()},resizeSelect:function(e){var t=(e=$(e)).siblings(".sizer-select");t.children(".sizer-option").html(e.children(":selected").text()),e.width(t.width())},trigger:function(){$(".resizing-select").trigger("change")}},Dribbble.ResizingSelect.bindEventListeners(),Dribbble.Utils={widowlessText:function(e){return words=e.trim().split(/\s+/),1===words.length?e.trim():(head=words.slice(0,words.length-2),penultimate=words[words.length-2],ultimate=words[words.length-1],(head.join(" ")+" "+penultimate+"&nbsp;"+ultimate).trim())},isRetina:function(){return window.matchMedia&&(window.matchMedia("only screen and (min-resolution: 124dpi), only screen and (min-resolution: 1.3dppx), only screen and (min-resolution: 48.8dpcm)").matches||window.matchMedia("only screen and (-webkit-min-device-pixel-ratio: 1.3), only screen and (-o-min-device-pixel-ratio: 2.6/2), only screen and (min--moz-device-pixel-ratio: 1.3), only screen and (min-device-pixel-ratio: 1.3)").matches)||window.devicePixelRatio&&1.3<window.devicePixelRatio},setupInview:function(){function t(){[].forEach.call(e,function(e){var t=e.getAttribute("data-in-view-class"),i="yes"==e.getAttribute("data-in-view-remove-class");Dribbble.Utils.isInViewport(e)?e.classList.add(t):i&&e.classList.remove(t)})}var e=document.querySelectorAll("[data-in-view-class]");[].forEach.call(e,function(e){t.call(e)}),window.addEventListener("resize",t),window.addEventListener("scroll",t)},isInViewport:function(e,t){var i=e.getBoundingClientRect(),n=Math.max(document.documentElement.clientHeight,window.innerHeight||0);t=t||window.innerHeight;return i.top<n+t&&i.bottom>-t},getOffset:function(e){for(var t=0,i=0;e&&!isNaN(e.offsetLeft)&&!isNaN(e.offsetTop);)t+=e.offsetLeft-e.scrollLeft,i+=e.offsetTop-e.scrollTop,e=e.offsetParent;return{top:i,left:t}},getGlobalValueFromPath:function(e){return[window].concat(e.split(".")).reduce(function(e,t){return e[t]})},noop:function(){}},Dribbble.MessageDraft={notice:$("#draft-message"),purgeableAfter:3600,draftMessage:null,save:function(e){var t={data:e,storedAt:(new Date).getTime()};localStorage.setItem("draft_message",JSON.stringify(t))},init:function(){this.draftMessage=JSON.parse(localStorage.getItem("draft_message")),this.purge(),this.toggleNotice(),this.bindAll()},send:function(e){e.preventDefault(),this.draftMessage&&(this.notice.html("Processing..."),$.post("/messages",this.draftMessage.data,this.onSuccess.bind(this)).fail(this.onFailure.bind(this)))},cancel:function(e){this["delete"](),this.notice.hide(),e.preventDefault()},"delete":function(){Cookies.remove("draft_message"),localStorage.removeItem("draft_message"),this.draftMessage=undefined},purge:function(){this.draftMessage&&(((new Date).getTime()-this.draftMessage.storedAt)/1e3>this.purgeableAfter&&this["delete"]())},onSuccess:function(e,t,i){201==i.status?(this.notice.html(e.text),this["delete"]()):this.onFailure()},onFailure:function(){this.notice.html("Oops, something went wrong."),this["delete"]()},toggleNotice:function(){this.draftMessage?this.notice.show():this.notice.hide()},bindAll:function(){$('[data-draft-message="send"]').on("click",this.send.bind(this)),$('[data-draft-message="cancel"]').on("click",this.cancel.bind(this))}},Dribbble.MessageDraft.init(),Dribbble.MessageModalNew=function MessageModalNew(){this.init=function(e){e=e||{},this.form=$("form.message"),this.maxLength=e.maxLength},this.bindEventListeners=function(){this.form=$("form.message"),Dribbble.SingleSubmit.bind(this.form),this.form.on("change",function(e){this.submitToggle(e.target.closest("form"))}.bind(this)),this.form.trigger("change")},this.onSubmit=function(e){e.preventDefault();var t=$(e.target),i=t.serialize();if(!this.validate(e.target))return!1;$.post(t.attr("action"),i,this.onSuccess.bind(this)),User.loggedOut()&&Dribbble.MessageDraft.save(i)},this.onSuccess=function(e,t,i){201==i.status?User.loggedOut()?(this.messageAttemptOverlay.hide(),Dribbble.SignupModal.open({context:"logged-out-message"})):(Dribbble.EventBus.$emit("MessageSent",{userId:document.getElementById("message_recipient_id").value,messageThreadId:e.message_thread_id}),Dribbble.EventBus.$emit("track:userMessaged",{message_body:document.getElementById("message_body").value,message_recipient_id:document.getElementById("message_recipient_id").value,message_thread_id:e.message_thread_id.toString(),message_id:e.message_id.toString()}),$(this.messageAttemptOverlay.remoteContent.el).html("<p class='response-text'>"+e.text+"</p>"),setTimeout(function(){this.messageAttemptOverlay.hide()}.bind(this),2e3)):(this.form.parents(".display").html(e),this.bindEventListeners())},this.overlayLoaded=function(e,t){if("error"==t){var i;try{i=$.parseJSON(e).path}catch(n){console.warn(n)}return i?window.location=i:$(this.messageAttemptOverlay.remoteContent.el).empty().html('<p class="error">Something went wrong. Please refresh the page and try again.</p>'),!1}this.bindEventListeners()},this.characterCounter=function(e,t,i){function n(){i.innerHTML="Characters left: "+(t-e.value.length),e.value.length>t?(i.innerHTML="You have exceeded the character limit",i.classList.add("text-red")):i.classList.remove("text-red"),e.addEventListener("blur",function(){i.style.display="none"}),e.addEventListener("focus",function(){i.style.display="block"})}e&&(e.addEventListener("keyup",function(){n()}),e.addEventListener("change",function(){n()}))},this.validate=function(e){var t,i=$(e),n=this.maxLength,r=i.find('[name="message[body]"]').val();return r?r.length>n&&(t={title:"Message too long",message:"Your message is "+r.length+" characters. Please limit your message to "+n+" characters or less.",id:"message-length"}):t={title:"Must include a message",message:"Please enter a message before sending.",id:"message-required"},!t||(Dribbble.EventBus.$emit("siteNotifications:add",{title:t.title,message:t.message,id:"form-error"}),Dribbble.SingleSubmit.enableForm(i),!1)},this.submitToggle=function(e){this.submitBtn=e.querySelector(".js-submit"),this.textArea=e.querySelector(".js-message-text-area"),this.formElement=e,this.textArea&&this.enableSubmit(this.textArea,this.submitBtn)},this.enableSubmit=function(e,t){0<e.value.length&&t.removeAttribute("disabled"),e.addEventListener("keyup",this.toggleDisabled.bind(this))},this.toggleDisabled=function(){this.textArea&&0<this.textArea.value.length?this.submitBtn.removeAttribute("disabled"):this.submitBtn.setAttribute("disabled","true")}},Dribbble.MessageTracking={prevTracked:[],initialize:function(){this.sendHiringShown(),this.bindEventListeners()},bindEventListeners:function(){$(".message-designers").scroll(function(){this.sendHiringShown()}.bind(this)),$(".js-select-toggle").on("click",function(e){$(e.target).toggleClass("selected-suggestion")}.bind(this))},sendHiringShown:function(){var i=$(".messages-description-of-work").offset().top,e=$("[id^='suggested-designer-']");$.each(e,function(e,t){Dribbble.MessageTracking.prevTracked.includes(t.id)||$(t).offset().top<i&&Dribbble.MessageTracking.prevTracked.push(t.id)})}},Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector),Element.prototype.closest||(Element.prototype.closest=function(e){var t=this;do{if(t.matches(e))return t;t=t.parentElement||t.parentNode}while(null!==t&&1===t.nodeType);return null}),Dribbble.uuidv4=function(){return"undefined"!=typeof crypto?([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,function(e){return(e^crypto.getRandomValues(new Uint8Array(1))[0]&15>>e/4).toString(16)}):"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})};