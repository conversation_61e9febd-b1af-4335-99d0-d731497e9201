(window.webpackJsonp=window.webpackJsonp||[]).push([[134],{1084:function(e,t,n){"use strict";n.r(t);var r=n(2),a=n.n(r),s=n(6),i=n.n(s),o=n(10);function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){u(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function h(e){return function(e){if(Array.isArray(e))return d(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return d(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return d(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function b(e,t,n,r,a,s,i){try{var o=e[s](i),c=o.value}catch(l){return void n(l)}o.done?t(c):Promise.resolve(c).then(r,a)}function f(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var s=e.apply(t,n);function i(e){b(s,r,a,i,o,"next",e)}function o(e){b(s,r,a,i,o,"throw",e)}i(void 0)}))}}function g(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var p=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n,r,s,c,u;return t=e,(n=[{key:"initialize",value:(u=f(a.a.mark((function e(t){var n,r,s,i,o,c,l,u;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.collection,r=t.perPage,s=t.placement,i=t.params,o=t.numberOfAdsOnFirstPage,c=t.numberOfAdsOnSubsequentPages,l=t.adGroupPositions,u=[n,r,s,o,c,l],this.collection=u[0],this.perPage=u[1],this.placement=u[2],this.numberOfAdsOnFirstPage=u[3],this.numberOfAdsOnSubsequentPages=u[4],this.adGroupPositions=u[5],this.setCurrentPageElements(),this.cachedItems=[],this.params=i||{},this.servedShotIds=[],this.currentPage=1,this.hasMoreThanOnePageOfShots=this.collection.length>=this.perPage,this.onScrollFunction=this.onScroll.bind(this),this.boostsOnPage={},this.collection.filter((function(e){return e instanceof Element||e instanceof HTMLDocument})),!(this.collection.length<=1)){e.next=19;break}return e.abrupt("return");case 19:return e.next=21,this.injectCurrentPage();case 21:this.bindEventListeners();case 22:case"end":return e.stop()}}),e,this)}))),function(e){return u.apply(this,arguments)})},{key:"bindEventListeners",value:function(){var e=this,t=function(){var t=Dribbble.Thumbnails.thumbnailsArray();t.length<=1||e.setAndInjectNewPage(t)};document.addEventListener("dribbble.infinitescroll.append",t),document.addEventListener("dribbble.filterShotResults",(function(){e.boostsOnPage={},t()})),Dribbble.EventBus.$on("shotFilters:updatedLocation",(function(t){var n=t.category,r=t.tag;e.params.category=n,e.params.tag=r}))}},{key:"setCurrentPageElements",value:function(){this.currentPageElements=this.collection.length==this.perPage?this.collection:this.collection.slice(-this.perPage)}},{key:"setAndInjectNewPage",value:function(e){this.currentPage++,this.collection=e,this.setCurrentPageElements(),this.addCachedItem(),this.injectCurrentPage()}},{key:"injectCurrentPage",value:(c=f(a.a.mark((function e(){var t,n=this;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.fetchBoosts();case 2:t=e.sent,this.hasMoreThanOnePageOfShots&&this.cacheExtraItems(),t.forEach((function(e){if(!e.boostPartial)return n.collection;var t;(t=n.hasMoreThanOnePageOfShots?n.currentPageElements[e.boostIndex-1]:n.collection[e.boostIndex])&&(t.insertAdjacentHTML("afterend",e.boostPartial),Dribbble.EventBus.$emit("boostedShotsAd:initialize"),n.bindBoostedAdEvents(e.boostId,e.trackingData,e.itlyTrackingData))}));case 5:case"end":return e.stop()}}),e,this)}))),function(){return c.apply(this,arguments)})},{key:"addCachedItem",value:function(){for(var e=0;e<this.getNumberOfBoosts();e++)this.cachedItems.length&&(this.currentPageElements[0].insertAdjacentElement("beforebegin",this.cachedItems[0]),this.cachedItems.shift())}},{key:"cacheExtraItems",value:function(){var e=1===this.currentPage?this.getNumberOfBoosts():2*this.getNumberOfBoosts(),t=this.collection.slice(this.collection.length-e,this.collection.length);this.cachedItems=[].concat(h(this.cachedItems),h(t)),t.forEach((function(e){return e.remove()}))}},{key:"getNewBoostIndex",value:function(e,t){var n=this.currentPageElements.length/e,r=n*t-n,a=r<2?2:r,s=n*t;return Math.floor(Math.random()*(s-a)+a)}},{key:"getNumberOfBoosts",value:function(){return this.collection.length<this.perPage?1:1===this.currentPage?this.numberOfAdsOnFirstPage:this.numberOfAdsOnSubsequentPages}},{key:"fetchBoosts",value:(s=f(a.a.mark((function e(){var t,n,r,s,o,c=this;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(t=[],n=[],this.params.q&&(this.params.keyword=this.params.q,delete this.params.q),r={placement:this.placement,provider:"Dribbble",pagePosition:null,searchRequest:JSON.stringify(this.params)},s={placement:this.placement,provider:"Dribbble",unit_type:"Boosted Shots"},o=0;o<this.getNumberOfBoosts();o++)n.push(Dribbble.uuidv4());return n.forEach((function(e){s.impression_id=e,Dribbble.Itly.adRequested(s)})),e.next=9,i.a.get("/screenshot_boost?render_in_list=true",{params:{original_params:this.params,served_shot_ids:this.servedShotIds,request_source:this.placement,number_of_boosts:this.getNumberOfBoosts(),current_page:this.currentPage}}).then((function(e){e.data.data.forEach((function(e,a){var i,o=e.searchData,u=e.viewData;i=c.hasMoreThanOnePageOfShots?c.getNewBoostIndex(8,c.adGroupPositions[a]):c.collection.length-1,c.servedShotIds.push(o.screenshotId);var h,d=l(l({},r),{},{requestId:n[a],pagePosition:i,adData:{ad_id:o.adId,ad_link:o.adLink,ad_link_type:o.adLinkType,ad_text:o.adText,has_pixel_tracking:o.has_pixel_tracking}});switch(o.adLinkType){case"shot-page":h="Shot";break;case"profile":h="Profile";break;case"custom":default:h="Custom URL"}var b=l(l({},s),{},{impression_id:n[a],ad_id:o.adId.toString(),ad_link:o.adLink,ad_link_type:h,ad_text:o.adText||"",has_cta:!1});Dribbble.Itly.adServed(b),t.push({trackingData:d,itlyTrackingData:b,boostIndex:i,boostId:o.adId,boostPartial:u})}))})).catch((function(){Dribbble.Itly.adRequestFailed(l(l({},s),{},{reason:"No boosted shots available."}))}));case 9:return e.abrupt("return",t);case 10:case"end":return e.stop()}}),e,this)}))),function(){return s.apply(this,arguments)})},{key:"onScroll",value:function(){var e=this;Object.keys(this.boostsOnPage).forEach((function(t){e.boostsOnPage[t].adElement.getBoundingClientRect().bottom<=window.innerHeight&&!e.boostsOnPage[t].hasBeenScrolledIntoView&&(e.boostsOnPage[t].hasBeenScrolledIntoView=!0,Dribbble.Itly.adImpressionViewed(e.boostsOnPage[t].itlyTrackingData))}))}},{key:"bindBoostedAdEvents",value:function(e,t,n){var r=this;this.boostsOnPage[e]||(this.boostsOnPage[e]={trackingData:t,itlyTrackingData:n,hasBeenScrolledIntoView:!1,adElement:document.querySelector('.js-ad-boosted[data-boost-id="'.concat(e,'"]'))}),this.boostsOnPage[e]&&this.boostsOnPage[e].adElement&&this.boostsOnPage[e].adElement.addEventListener("click",(function(){Dribbble.Itly.adClicked(r.boostsOnPage[e].itlyTrackingData),i.a.get("/screenshot_boost/click",{params:{id:e,request_source:r.boostsOnPage[e].adElement.dataset.requestSource}},Object(o.axiosOptions)())})),window.removeEventListener("scroll",this.onScrollFunction),window.addEventListener("scroll",this.onScrollFunction)}}])&&g(t.prototype,n),r&&g(t,r),e}();t.default=new p}}]);
//# sourceMappingURL=134-b959e2c658a07891cbf9.chunk.js.map