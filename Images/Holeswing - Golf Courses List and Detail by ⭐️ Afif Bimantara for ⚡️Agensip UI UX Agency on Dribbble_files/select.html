<!DOCTYPE html>
<!-- saved from url=(0365)https://accounts.google.com/gsi/iframe/select?client_id=***********-s6ur8ti01mh34gq2bpbufb8ujdfrpn4v.apps.googleusercontent.com&ux_mode=redirect&login_uri=https%3A%2F%2Fdribbble.com%2Fauth%2Fgoogle_one_tap%2Fcallback&ui_mode=card&as=MSSOhJo05pQcaeLeQIyEFg&channel_id=edd6bcb5625442759ffe599f7678f6c93eb08932bd698f9b5fc147b6e3efef42&origin=https%3A%2F%2Fdribbble.com -->
<html lang="en" class="mdl-js"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><title>Sign In - Google Accounts</title><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="referrer" content="no-referrer"><style nonce="">@font-face{font-family:'Roboto';font-style:normal;font-weight:400;src:url(//fonts.gstatic.com/s/roboto/v18/KFOmCnqEu92Fr1Mu4mxP.ttf)format('truetype');}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;src:url(//fonts.gstatic.com/s/roboto/v18/KFOlCnqEu92Fr1MmEU9fBBc9.ttf)format('truetype');}@font-face{font-family:'Google Sans';font-style:normal;font-weight:400;src:url(//fonts.gstatic.com/s/googlesans/v14/4UaGrENHsxJlGDuGo1OIlL3Owps.ttf)format('truetype');}@font-face{font-family:'Google Sans';font-style:normal;font-weight:500;src:url(//fonts.gstatic.com/s/googlesans/v14/4UabrENHsxJlGDuGo1OIlLU94YtzCwM.ttf)format('truetype');}</style><style nonce="">.mdl-button{background:transparent;border:none;border-radius:2px;color:rgb(0,0,0);position:relative;height:36px;margin:0;min-width:64px;padding:0 16px;display:inline-block;font-family:"Roboto","Helvetica","Arial",sans-serif;font-size:14px;font-weight:500;text-transform:uppercase;line-height:1;letter-spacing:0;overflow:hidden;will-change:box-shadow;transition:box-shadow .2s cubic-bezier(0.4, 0, 1, 1),background-color .2s cubic-bezier(0.4, 0, 0.2, 1),color .2s cubic-bezier(0.4, 0, 0.2, 1);outline:none;cursor:pointer;text-decoration:none;text-align:center;line-height:36px;vertical-align:middle}.mdl-button::-moz-focus-inner{border:0}.mdl-button:hover{background-color:rgba(158,158,158, 0.20)}.mdl-button:focus:not(:active){background-color:rgba(0,0,0, 0.12)}.mdl-button:active{background-color:rgba(158,158,158, 0.40)}.mdl-button.mdl-button--colored{color:rgb(63,81,181)}.mdl-button.mdl-button--colored:focus:not(:active){background-color:rgba(0,0,0, 0.12)}input.mdl-button[type=submit]{-webkit-appearance:none}.mdl-button--raised{background:rgba(158,158,158, 0.20);box-shadow:0 2px 2px 0 rgba(0,0,0,.14),0 3px 1px -2px rgba(0,0,0,.2),0 1px 5px 0 rgba(0,0,0,.12)}.mdl-button--raised:active{box-shadow:0 4px 5px 0 rgba(0,0,0,.14),0 1px 10px 0 rgba(0,0,0,.12),0 2px 4px -1px rgba(0,0,0,.2);background-color:rgba(158,158,158, 0.40)}.mdl-button--raised:focus:not(:active){box-shadow:0 0 8px rgba(0,0,0,.18),0 8px 16px rgba(0,0,0,.36);background-color:rgba(158,158,158, 0.40)}.mdl-button--raised.mdl-button--colored{background:rgb(63,81,181);color:rgb(255,255,255)}.mdl-button--raised.mdl-button--colored:hover{background-color:rgb(63,81,181)}.mdl-button--raised.mdl-button--colored:active{background-color:rgb(63,81,181)}.mdl-button--raised.mdl-button--colored:focus:not(:active){background-color:rgb(63,81,181)}.mdl-button--raised.mdl-button--colored .mdl-ripple{background:rgb(255,255,255)}.mdl-button--fab{border-radius:50%;font-size:24px;height:56px;margin:auto;min-width:56px;width:56px;padding:0;overflow:hidden;background:rgba(158,158,158, 0.20);box-shadow:0 1px 1.5px 0 rgba(0,0,0,.12),0 1px 1px 0 rgba(0,0,0,.24);position:relative;line-height:normal}.mdl-button--fab .material-icons{position:absolute;top:50%;left:50%;transform:translate(-12px, -12px);line-height:24px;width:24px}.mdl-button--fab.mdl-button--mini-fab{height:40px;min-width:40px;width:40px}.mdl-button--fab .mdl-button__ripple-container{border-radius:50%;-webkit-mask-image:-webkit-radial-gradient(circle, white, black)}.mdl-button--fab:active{box-shadow:0 4px 5px 0 rgba(0,0,0,.14),0 1px 10px 0 rgba(0,0,0,.12),0 2px 4px -1px rgba(0,0,0,.2);background-color:rgba(158,158,158, 0.40)}.mdl-button--fab:focus:not(:active){box-shadow:0 0 8px rgba(0,0,0,.18),0 8px 16px rgba(0,0,0,.36);background-color:rgba(158,158,158, 0.40)}.mdl-button--fab.mdl-button--colored{background:rgb(255,64,129);color:rgb(255,255,255)}.mdl-button--fab.mdl-button--colored:hover{background-color:rgb(255,64,129)}.mdl-button--fab.mdl-button--colored:focus:not(:active){background-color:rgb(255,64,129)}.mdl-button--fab.mdl-button--colored:active{background-color:rgb(255,64,129)}.mdl-button--fab.mdl-button--colored .mdl-ripple{background:rgb(255,255,255)}.mdl-button--icon{border-radius:50%;font-size:24px;height:32px;margin-left:0;margin-right:0;min-width:32px;width:32px;padding:0;overflow:hidden;color:inherit;line-height:normal}.mdl-button--icon .material-icons{position:absolute;top:50%;left:50%;transform:translate(-12px, -12px);line-height:24px;width:24px}.mdl-button--icon.mdl-button--mini-icon{height:24px;min-width:24px;width:24px}.mdl-button--icon.mdl-button--mini-icon .material-icons{top:0px;left:0px}.mdl-button--icon .mdl-button__ripple-container{border-radius:50%;-webkit-mask-image:-webkit-radial-gradient(circle, white, black)}.mdl-button__ripple-container{display:block;height:100%;left:0px;position:absolute;top:0px;width:100%;z-index:0;overflow:hidden}.mdl-button[disabled] .mdl-button__ripple-container .mdl-ripple,.mdl-button.mdl-button--disabled .mdl-button__ripple-container .mdl-ripple{background-color:transparent}.mdl-button--primary.mdl-button--primary{color:rgb(63,81,181)}.mdl-button--primary.mdl-button--primary .mdl-ripple{background:rgb(255,255,255)}.mdl-button--primary.mdl-button--primary.mdl-button--raised,.mdl-button--primary.mdl-button--primary.mdl-button--fab{color:rgb(255,255,255);background-color:rgb(63,81,181)}.mdl-button--accent.mdl-button--accent{color:rgb(255,64,129)}.mdl-button--accent.mdl-button--accent .mdl-ripple{background:rgb(255,255,255)}.mdl-button--accent.mdl-button--accent.mdl-button--raised,.mdl-button--accent.mdl-button--accent.mdl-button--fab{color:rgb(255,255,255);background-color:rgb(255,64,129)}.mdl-button[disabled][disabled],.mdl-button.mdl-button--disabled.mdl-button--disabled{color:rgba(0,0,0, 0.26);cursor:default;background-color:transparent}.mdl-button--fab[disabled][disabled],.mdl-button--fab.mdl-button--disabled.mdl-button--disabled{background-color:rgba(0,0,0, 0.12);color:rgba(0,0,0, 0.26)}.mdl-button--raised[disabled][disabled],.mdl-button--raised.mdl-button--disabled.mdl-button--disabled{background-color:rgba(0,0,0, 0.12);color:rgba(0,0,0, 0.26);box-shadow:none}.mdl-button--colored[disabled][disabled],.mdl-button--colored.mdl-button--disabled.mdl-button--disabled{color:rgba(0,0,0, 0.26)}.mdl-button .material-icons{vertical-align:middle}
.mdl-spinner{display:inline-block;position:relative;width:28px;height:28px}.mdl-spinner:not(.is-upgraded).is-active:after{content:"Loading..."}.mdl-spinner.is-upgraded.is-active{animation:mdl-spinner__container-rotate 1568.2352941176ms linear infinite}@keyframes mdl-spinner__container-rotate{to{transform:rotate(360deg)}}.mdl-spinner__layer{position:absolute;width:100%;height:100%;opacity:0}.mdl-spinner__layer-1{border-color:rgb(66,165,245)}.mdl-spinner--single-color .mdl-spinner__layer-1{border-color:rgb(63,81,181)}.mdl-spinner.is-active .mdl-spinner__layer-1{animation:mdl-spinner__fill-unfill-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both,mdl-spinner__layer-1-fade-in-out 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdl-spinner__layer-2{border-color:rgb(244,67,54)}.mdl-spinner--single-color .mdl-spinner__layer-2{border-color:rgb(63,81,181)}.mdl-spinner.is-active .mdl-spinner__layer-2{animation:mdl-spinner__fill-unfill-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both,mdl-spinner__layer-2-fade-in-out 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdl-spinner__layer-3{border-color:rgb(253,216,53)}.mdl-spinner--single-color .mdl-spinner__layer-3{border-color:rgb(63,81,181)}.mdl-spinner.is-active .mdl-spinner__layer-3{animation:mdl-spinner__fill-unfill-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both,mdl-spinner__layer-3-fade-in-out 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdl-spinner__layer-4{border-color:rgb(76,175,80)}.mdl-spinner--single-color .mdl-spinner__layer-4{border-color:rgb(63,81,181)}.mdl-spinner.is-active .mdl-spinner__layer-4{animation:mdl-spinner__fill-unfill-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both,mdl-spinner__layer-4-fade-in-out 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdl-spinner__fill-unfill-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}to{transform:rotate(1080deg)}}@keyframes mdl-spinner__layer-1-fade-in-out{from{opacity:.99}25%{opacity:.99}26%{opacity:0}89%{opacity:0}90%{opacity:.99}100%{opacity:.99}}@keyframes mdl-spinner__layer-2-fade-in-out{from{opacity:0}15%{opacity:0}25%{opacity:.99}50%{opacity:.99}51%{opacity:0}}@keyframes mdl-spinner__layer-3-fade-in-out{from{opacity:0}40%{opacity:0}50%{opacity:.99}75%{opacity:.99}76%{opacity:0}}@keyframes mdl-spinner__layer-4-fade-in-out{from{opacity:0}65%{opacity:0}75%{opacity:.99}90%{opacity:.99}100%{opacity:0}}.mdl-spinner__gap-patch{position:absolute;box-sizing:border-box;top:0;left:45%;width:10%;height:100%;overflow:hidden;border-color:inherit}.mdl-spinner__gap-patch .mdl-spinner__circle{width:1000%;left:-450%}.mdl-spinner__circle-clipper{display:inline-block;position:relative;width:50%;height:100%;overflow:hidden;border-color:inherit}.mdl-spinner__circle-clipper.mdl-spinner__left{float:left}.mdl-spinner__circle-clipper.mdl-spinner__right{float:right}.mdl-spinner__circle-clipper .mdl-spinner__circle{width:200%}.mdl-spinner__circle{box-sizing:border-box;height:100%;border-width:3px;border-style:solid;border-color:inherit;border-bottom-color:transparent !important;border-radius:50%;animation:none;position:absolute;top:0;right:0;bottom:0;left:0}.mdl-spinner__left .mdl-spinner__circle{border-right-color:transparent !important;transform:rotate(129deg)}.mdl-spinner.is-active .mdl-spinner__left .mdl-spinner__circle{animation:mdl-spinner__left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdl-spinner__right .mdl-spinner__circle{left:-100%;border-left-color:transparent !important;transform:rotate(-129deg)}.mdl-spinner.is-active .mdl-spinner__right .mdl-spinner__circle{animation:mdl-spinner__right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdl-spinner__left-spin{from{transform:rotate(130deg)}50%{transform:rotate(-5deg)}to{transform:rotate(130deg)}}@keyframes mdl-spinner__right-spin{from{transform:rotate(-130deg)}50%{transform:rotate(5deg)}to{transform:rotate(-130deg)}}
.mdl-progress{display:block;position:relative;height:4px;width:500px;max-width:100%}.mdl-progress>.bar{display:block;position:absolute;top:0;bottom:0;width:0%;transition:width .2s cubic-bezier(0.4, 0, 0.2, 1)}.mdl-progress>.progressbar{background-color:rgb(63,81,181);z-index:1;left:0}.mdl-progress>.bufferbar{background-image:linear-gradient(to right, rgba(255,255,255, 0.7), rgba(255,255,255, 0.7)),linear-gradient(to right, rgb(63,81,181), rgb(63,81,181));z-index:0;left:0}.mdl-progress>.auxbar{right:0}@supports(-webkit-appearance: none){.mdl-progress:not(.mdl-progress--indeterminate):not(.mdl-progress--indeterminate)>.auxbar,.mdl-progress:not(.mdl-progress__indeterminate):not(.mdl-progress__indeterminate)>.auxbar{background-image:linear-gradient(to right, rgba(255,255,255, 0.7), rgba(255,255,255, 0.7)),linear-gradient(to right, rgb(63,81,181), rgb(63,81,181));mask:url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIj8+Cjxzdmcgd2lkdGg9IjEyIiBoZWlnaHQ9IjQiIHZpZXdQb3J0PSIwIDAgMTIgNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgogIDxlbGxpcHNlIGN4PSIyIiBjeT0iMiIgcng9IjIiIHJ5PSIyIj4KICAgIDxhbmltYXRlIGF0dHJpYnV0ZU5hbWU9ImN4IiBmcm9tPSIyIiB0bz0iLTEwIiBkdXI9IjAuNnMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAvPgogIDwvZWxsaXBzZT4KICA8ZWxsaXBzZSBjeD0iMTQiIGN5PSIyIiByeD0iMiIgcnk9IjIiIGNsYXNzPSJsb2FkZXIiPgogICAgPGFuaW1hdGUgYXR0cmlidXRlTmFtZT0iY3giIGZyb209IjE0IiB0bz0iMiIgZHVyPSIwLjZzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgLz4KICA8L2VsbGlwc2U+Cjwvc3ZnPgo=")}}.mdl-progress:not(.mdl-progress--indeterminate)>.auxbar,.mdl-progress:not(.mdl-progress__indeterminate)>.auxbar{background-image:linear-gradient(to right, rgba(255,255,255, 0.9), rgba(255,255,255, 0.9)),linear-gradient(to right, rgb(63,81,181), rgb(63,81,181))}.mdl-progress.mdl-progress--indeterminate>.bar1,.mdl-progress.mdl-progress__indeterminate>.bar1{background-color:rgb(63,81,181);animation-name:indeterminate1;animation-duration:2s;animation-iteration-count:infinite;animation-timing-function:linear}.mdl-progress.mdl-progress--indeterminate>.bar3,.mdl-progress.mdl-progress__indeterminate>.bar3{background-image:none;background-color:rgb(63,81,181);animation-name:indeterminate2;animation-duration:2s;animation-iteration-count:infinite;animation-timing-function:linear}@keyframes indeterminate1{0%{left:0%;width:0%}50%{left:25%;width:75%}75%{left:100%;width:0%}}@keyframes indeterminate2{0%{left:0%;width:0%}50%{left:0%;width:0%}75%{left:0%;width:25%}100%{left:100%;width:0%}}
</style><style nonce="">.qJTHM{-webkit-user-select:none;color:#202124;direction:ltr;-webkit-touch-callout:none;font-family:'Roboto-Regular',arial,sans-serif;-webkit-font-smoothing:antialiased;font-weight:400;margin:0;overflow:hidden;-webkit-text-size-adjust:100%}.ynRLnc{left:-9999px;position:absolute;top:-9999px}.L6cTce{display:none}.bltWBb{word-break:break-all}.hSRGPd{color:#1a73e8;cursor:pointer;font-weight:500;text-decoration:none}.Bz112c-W3lGp{height:16px;width:16px}.Bz112c-E3DyYd{height:20px;width:20px}.Bz112c-r9oPif{height:24px;width:24px}.Bz112c-uaxL4e{-webkit-border-radius:10px;border-radius:10px}.LgbsSe-Bz112c{display:block}.S9gUrf-YoZ4jf,.S9gUrf-YoZ4jf *{border:none;margin:0;padding:0}.fFW7wc-ibnC6b>.aZ2wEe>div{border-color:#4285f4}.P1ekSe-ZMv3u>div:nth-child(1){background-color:#1a73e8!important}.P1ekSe-ZMv3u>div:nth-child(2),.P1ekSe-ZMv3u>div:nth-child(3){background-image:linear-gradient(to right,rgba(255,255,255,0.7),rgba(255,255,255,0.7)),linear-gradient(to right,#1a73e8,#1a73e8)!important}.fFW7wc,.fFW7wc-ibnC6b-r4m2rf,.fFW7wc-ibnC6b-ssJRIf,.fFW7wc-ibnC6b-K4efff,.MPu53c{-webkit-box-sizing:border-box;box-sizing:border-box}.fFW7wc-ibnC6b-sM5MNb{-webkit-transition:background-color .2s cubic-bezier(0.4,0,0.2,1);transition:background-color .2s cubic-bezier(0.4,0,0.2,1);height:60px;-webkit-tap-highlight-color:rgba(0,0,0,0)}.fFW7wc-ibnC6b-sM5MNb.wdeprb-ijUMG-mzNpsf{height:48px}.fFW7wc-ibnC6b-sM5MNb:not(.OWB6Me):hover,.fFW7wc-ibnC6b-sM5MNb:not(.OWB6Me):focus{background-color:#e8f0fe;cursor:pointer;outline:none}.fFW7wc-ibnC6b{-webkit-box-sizing:content-box;box-sizing:content-box;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-align-items:start;align-items:start;-webkit-flex-direction:row;flex-direction:row;justify-content:flex-start;height:35px;margin:0 16px;overflow:hidden;padding:14px 0 11px 0}.fFW7wc-ibnC6b-sM5MNb.wdeprb-ijUMG-mzNpsf>.fFW7wc-ibnC6b{height:23px}.fFW7wc-ibnC6b>.fFW7wc-ibnC6b-HiaYvf{-webkit-border-radius:50%;border-radius:50%;-webkit-flex-grow:0;flex-grow:0;-webkit-flex-shrink:0;flex-shrink:0;height:28px;margin-left:-28px;margin-right:12px;margin-top:2px;width:28px;z-index:400}.fFW7wc-ibnC6b>.fFW7wc-ibnC6b-HiaYvf.zTETae-mzNpsf-Bz112c{height:30px;margin-left:-29px;margin-right:11px;margin-top:1px;width:30px}.fFW7wc-ibnC6b>.fFW7wc-ibnC6b-HiaYvf.wdeprb-ijUMG-mzNpsf-Bz112c{height:20px;margin:0 17px 0 5px;width:20px}.fFW7wc-ibnC6b>.aZ2wEe{direction:ltr;height:32px;width:32px;z-index:300}.fFW7wc-ibnC6b .MPu53c{-webkit-border-radius:50%;border-radius:50%;background-color:#4285f4;height:28px;margin-left:-30px;margin-top:2px;opacity:0;padding:4px;width:28px;z-index:500}.fFW7wc-ibnC6b .MPu53c.iib5kc{-webkit-transition:opacity 250ms;transition:opacity 250ms;opacity:1}.fFW7wc-ibnC6b .MPu53c>.Bz112c{-webkit-transform:scale(0);transform:scale(0)}.fFW7wc-ibnC6b .MPu53c.iib5kc>.Bz112c{-webkit-transition:transform 250ms ease-out;transition:transform 250ms ease-out;-webkit-transform:scale(1);transform:scale(1)}.fFW7wc-ibnC6b>.fFW7wc-ibnC6b-r4m2rf{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;justify-content:center;height:35px}.fFW7wc-ibnC6b-sM5MNb.wdeprb-ijUMG-mzNpsf .fFW7wc-ibnC6b-r4m2rf{height:23px}.fFW7wc-ibnC6b>.fFW7wc-ibnC6b-r4m2rf>.fFW7wc-ibnC6b-ssJRIf{color:#3c4043;font-family:'Google Sans';font-size:14px;font-weight:500;height:20px;letter-spacing:0.25px;line-height:18px}.fFW7wc-ibnC6b>.fFW7wc-ibnC6b-r4m2rf>.fFW7wc-ibnC6b-K4efff{color:#5f6368;font-size:12px;height:16px}#animated-container .IbE0S-LgbsSe{color:#1a73e8;display:none;font-family:'Google Sans';letter-spacing:0.25px;margin-left:auto;padding:0 8px;text-transform:none}#animated-container .IbE0S-LgbsSe:disabled{color:#3c4043;opacity:.38}#animated-container .IbE0S-LgbsSe:hover:enabled,#animated-container .IbE0S-LgbsSe:active:enabled,#animated-container .IbE0S-LgbsSe:focus:enabled{background-color:#e8f0fe}#animated-container .gk6SMd .IbE0S-LgbsSe,#animated-container.XHgP6b-mKZypf-bEDTcc-LYNcwc .IbE0S-LgbsSe{display:block}#progress-bar{-webkit-transition:height .2s cubic-bezier(0.4,0,0.2,1);transition:height .2s cubic-bezier(0.4,0,0.2,1);height:0}.Uno0P #progress-bar,.XHgP6b-mKZypf-bEDTcc-LYNcwc #progress-bar{height:4px}@keyframes slide-in{0%{-webkit-transform:translateY(100%);transform:translateY(100%)}to{-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes slide-out{0%{-webkit-transform:translateY(0);transform:translateY(0)}to{-webkit-transform:translateY(100%);transform:translateY(100%)}}.cGMI2b-vOE8Lb-haAclf,.WsjYwc-haAclf .YLEHIf-haAclf,.Sx9Kwc-haAclf,.r4nke{-webkit-box-sizing:border-box;box-sizing:border-box}.WsjYwc-haAclf,.cGMI2b-vOE8Lb-haAclf{overflow:hidden}.WsjYwc-haAclf .YLEHIf-haAclf{padding:6px 8px 10px 8px}.cGMI2b-vOE8Lb-haAclf{bottom:0;position:fixed;width:100%}.cGMI2b-vOE8Lb-haAclf.hOedQd-QFlW2-HQkcwf{bottom:1px}.cGMI2b-vOE8Lb-haAclf .YLEHIf-haAclf{padding-top:8px;-webkit-transform:translateY(100%);transform:translateY(100%);will-change:transform}.WsjYwc-haAclf .YLEHIf-haAclf.xTMeO{-webkit-transition:opacity 250ms;transition:opacity 250ms;opacity:0}.cGMI2b-vOE8Lb-haAclf .YLEHIf-haAclf.MjR6uf{-webkit-transform:translateY(0);transform:translateY(0);-webkit-animation:slide-in 225ms cubic-bezier(0.0,0.0,0.2,1);animation:slide-in 225ms cubic-bezier(0.0,0.0,0.2,1)}.cGMI2b-vOE8Lb-haAclf .YLEHIf-haAclf.xTMeO{-webkit-transform:translateY(100%);transform:translateY(100%);-webkit-animation:slide-out 250ms;animation:slide-out 250ms}.YLEHIf-haAclf.FnSee .ZYIfFd-aGxpHf-FnSee{display:none}.YLEHIf-haAclf.FnSee .ti6hGc-aGxpHf-FnSee{display:block}.Sx9Kwc-haAclf{-webkit-box-shadow:0 0 0 1px #dadce0;box-shadow:0 0 0 1px #dadce0;background-color:#fff;padding-bottom:14px;-webkit-transition:border 280ms cubic-bezier(.4,0,.2,1),box-shadow 280ms cubic-bezier(.4,0,.2,1);transition:border 280ms cubic-bezier(.4,0,.2,1),box-shadow 280ms cubic-bezier(.4,0,.2,1)}.Sx9Kwc-haAclf:focus-within{-webkit-box-shadow:0px 1px 2px 0px rgba(60,64,67,.30),0px 2px 6px 2px rgba(60,64,67,.15);box-shadow:0px 1px 2px 0px rgba(60,64,67,.30),0px 2px 6px 2px rgba(60,64,67,.15);border-color:transparent;outline:none}.WsjYwc-haAclf .Sx9Kwc-haAclf{-webkit-border-radius:8px;border-radius:8px}.cGMI2b-vOE8Lb-haAclf .Sx9Kwc-haAclf{-webkit-border-top-left-radius:8px;border-top-left-radius:8px;-webkit-border-top-right-radius:8px;border-top-right-radius:8px;margin:0 -1px}.Sx9Kwc-haAclf>.r4nke{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-align-items:center;align-items:center;justify-content:flex-start;min-height:48px}.Sx9Kwc-haAclf>.r4nke .jcJzye-Bz112c{height:20px;margin-left:16px;margin-right:12px}.Sx9Kwc-haAclf>.r4nke>.r4nke-LS81yb{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-align-items:center;align-items:center;-webkit-flex-grow:1;flex-grow:1}@media (-webkit-min-device-pixel-ratio:0){.XpnDCe{outline-color:-webkit-focus-ring-color;outline-style:auto}}.Sx9Kwc-haAclf>.r4nke>.TvD9Pc-Bz112c{height:24px;margin-left:4px;padding:12px;position:relative}.Sx9Kwc-haAclf>.r4nke>.TvD9Pc-Bz112c>.Bz112c{position:relative}.CNusmb-haDnnc-JaPV2b .Sx9Kwc-haAclf>.r4nke>.TvD9Pc-Bz112c>.Bz112c{fill:#fff}.CNusmb-haDnnc-HLvlvd .Sx9Kwc-haAclf>.r4nke>.TvD9Pc-Bz112c>.Bz112c{fill:#000}.Sx9Kwc-haAclf>.r4nke>.TvD9Pc-Bz112c>.Bz112c-ZmdkE{-webkit-transition:transform .05s cubic-bezier(0.4,0,0.2,1);transition:transform .05s cubic-bezier(0.4,0,0.2,1);background-color:#202124;-webkit-border-radius:50%;border-radius:50%;height:40px;left:4px;top:4px;opacity:.04;position:absolute;-webkit-transform-origin:center;transform-origin:center;-webkit-transform:scale(0);transform:scale(0);width:40px}.Sx9Kwc-haAclf>.r4nke>.TvD9Pc-Bz112c:active,.Sx9Kwc-haAclf>.r4nke>.TvD9Pc-Bz112c:focus,.Sx9Kwc-haAclf>.r4nke>.TvD9Pc-Bz112c:hover{cursor:pointer;outline:none}.Sx9Kwc-haAclf>.r4nke>.TvD9Pc-Bz112c:active>.Bz112c-ZmdkE,.Sx9Kwc-haAclf>.r4nke>.TvD9Pc-Bz112c:focus>.Bz112c-ZmdkE,.Sx9Kwc-haAclf>.r4nke>.TvD9Pc-Bz112c:hover>.Bz112c-ZmdkE{-webkit-transform:scale(1);transform:scale(1)}.CNusmb-haDnnc .Sx9Kwc-haAclf>.r4nke>.TvD9Pc-Bz112c>.Bz112c-ZmdkE{-webkit-transition:none;transition:none;opacity:1}.CNusmb-haDnnc-JaPV2b .Sx9Kwc-haAclf>.r4nke>.TvD9Pc-Bz112c>.Bz112c-ZmdkE{background-color:#fff}.CNusmb-haDnnc-HLvlvd .Sx9Kwc-haAclf>.r4nke>.TvD9Pc-Bz112c>.Bz112c-ZmdkE{background-color:#000}.CNusmb-haDnnc .Sx9Kwc-haAclf>.r4nke>.TvD9Pc-Bz112c:focus{outline-style:auto}.CNusmb-haDnnc-JaPV2b .Sx9Kwc-haAclf>.r4nke>.TvD9Pc-Bz112c:active>.Bz112c,.CNusmb-haDnnc-JaPV2b .Sx9Kwc-haAclf>.r4nke>.TvD9Pc-Bz112c:focus>.Bz112c,.CNusmb-haDnnc-JaPV2b .Sx9Kwc-haAclf>.r4nke>.TvD9Pc-Bz112c:hover>.Bz112c{fill:#000}.CNusmb-haDnnc-HLvlvd .Sx9Kwc-haAclf>.r4nke>.TvD9Pc-Bz112c:active>.Bz112c,.CNusmb-haDnnc-HLvlvd .Sx9Kwc-haAclf>.r4nke>.TvD9Pc-Bz112c:focus>.Bz112c,.CNusmb-haDnnc-HLvlvd .Sx9Kwc-haAclf>.r4nke>.TvD9Pc-Bz112c:hover>.Bz112c{fill:#fff}.Sx9Kwc-haAclf>.r4nke .tJHJj{-webkit-flex-grow:1;flex-grow:1;color:#202124;font-family:'Google Sans';font-weight:500;font-size:14px;letter-spacing:0.25px;margin:0;padding:16px 0 15px 0}.Sx9Kwc-haAclf>.r4nke .tJHJj:focus{border:none;outline:none}.Sx9Kwc-haAclf>.k77Iif{border-top:1px solid #dadce0;padding-top:4px}.WsjYwc-haAclf .k77Iif{-webkit-transition:max-height 250ms;transition:max-height 250ms;max-height:122px;overflow:hidden;position:relative}.WsjYwc-haAclf .McfNlf .k77Iif,.WsjYwc-haAclf .lgKYGb-v0h5Oe .k77Iif{max-height:none}.cGMI2b-vOE8Lb-haAclf .k77Iif{max-height:150px;overflow-y:auto}.cGMI2b-vOE8Lb-haAclf .McfNlf .k77Iif{max-height:196px;overflow-y:auto}.cGMI2b-vOE8Lb-haAclf .lgKYGb-v0h5Oe .k77Iif{max-height:216px;overflow-y:auto}.YLEHIf-haAclf.FnSee .fFW7wc-ibnC6b-sM5MNb:not(.OWB6Me):hover,.YLEHIf-haAclf.FnSee .fFW7wc-ibnC6b-sM5MNb:not(.OWB6Me):focus,.YLEHIf-haAclf.XHgP6b-mKZypf-bEDTcc-LYNcwc .fFW7wc-ibnC6b-sM5MNb:not(.OWB6Me):hover,.YLEHIf-haAclf.XHgP6b-mKZypf-bEDTcc-LYNcwc .fFW7wc-ibnC6b-sM5MNb:not(.OWB6Me):focus{background-color:transparent;cursor:default;outline:none}.YLEHIf-haAclf .fFW7wc-ibnC6b-sM5MNb:not(.gk6SMd),.YLEHIf-haAclf .fFW7wc-ibnC6b-sM5MNb:not(.gk6SMd)>.fFW7wc-ibnC6b,.YLEHIf-haAclf .k77Iif-v3pZbf-LgbsSe,.YLEHIf-haAclf .yePe5c-sM5MNb{-webkit-transition:all .5s;transition:all .5s}.YLEHIf-haAclf.FnSee .fFW7wc-ibnC6b-sM5MNb:not(.gk6SMd),.YLEHIf-haAclf.FnSee .fFW7wc-ibnC6b-sM5MNb:not(.gk6SMd)>.fFW7wc-ibnC6b,.YLEHIf-haAclf.FnSee .k77Iif-v3pZbf-LgbsSe,.YLEHIf-haAclf.FnSee .yePe5c-sM5MNb{height:0;margin-bottom:0;margin-top:0;opacity:0;padding-bottom:0;padding-top:0}.YLEHIf-haAclf:not(.FnSee) .fFW7wc-ibnC6b-sM5MNb:nth-child(n+2){height:61px}.YLEHIf-haAclf:not(.FnSee) .fFW7wc>.fFW7wc-ibnC6b-sM5MNb:nth-child(n+2)>.fFW7wc-ibnC6b{border-top:1px solid #dadce0}.Sx9Kwc-haAclf>.yePe5c-sM5MNb{-webkit-border-bottom-left-radius:8px;border-bottom-left-radius:8px;-webkit-border-bottom-right-radius:8px;border-bottom-right-radius:8px;-webkit-transition:background-color .2s cubic-bezier(0.4,0,0.2,1);transition:background-color .2s cubic-bezier(0.4,0,0.2,1);height:42px;margin-bottom:-14px}.Sx9Kwc-haAclf>.yePe5c-sM5MNb .yePe5c{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-align-items:center;align-items:center;justify-content:center;border-top:1px solid #dadce0;color:#5f6368;font-size:12px;height:42px;margin:0 16px}.Sx9Kwc-haAclf>.yePe5c-sM5MNb:hover,.Sx9Kwc-haAclf>.yePe5c-sM5MNb:focus{background-color:#e8f0fe;cursor:pointer;outline:none}.k77Iif-v3pZbf-LgbsSe{display:-webkit-box;display:-webkit-flex;display:flex;height:36px;-webkit-align-items:center;align-items:center;-webkit-flex-direction:column;flex-direction:column;justify-content:start;margin-top:4px;padding:0 16px}.k77Iif-v3pZbf-LgbsSe>.LgbsSe{-webkit-border-radius:4px;border-radius:4px;color:#4285f4;font-family:'Google Sans';height:36px;letter-spacing:0.25px;text-transform:none}.k77Iif-v3pZbf-LgbsSe>.LgbsSe-ssJRIf{color:#fff;background-color:#1a73e8}.k77Iif-v3pZbf-LgbsSe>.LgbsSe-ssJRIf:hover,.k77Iif-v3pZbf-LgbsSe>.LgbsSe-ssJRIf:active{background-color:#1a73e8;outline:none}.k77Iif-v3pZbf-LgbsSe>.LgbsSe-ssJRIf:focus{background-color:#1a73e8;outline:none;-webkit-box-shadow:0 1px 2px 0 rgba(26,115,232,0.302),0 1px 3px 1px rgba(26,115,232,0.149);box-shadow:0 1px 2px 0 rgba(26,115,232,0.302),0 1px 3px 1px rgba(26,115,232,0.149)}.k77Iif-v3pZbf-LgbsSe .LgbsSe-MJoBVe{-webkit-transition:background-color .218s;transition:background-color .218s;background-color:#202124;bottom:0;left:0;opacity:0;position:absolute;right:0;top:0}.k77Iif-v3pZbf-LgbsSe>.LgbsSe:hover>.LgbsSe-MJoBVe{opacity:.16}.k77Iif-v3pZbf-LgbsSe>.LgbsSe:focus>.LgbsSe-MJoBVe{opacity:.24}.k77Iif-v3pZbf-LgbsSe>.LgbsSe:active>.LgbsSe-MJoBVe{opacity:.2}.k77Iif-v3pZbf-LgbsSe>.LgbsSe>.LgbsSe-bN97Pc{position:relative}.k77Iif-v3pZbf-LgbsSe>.LgbsSe-KoToPc{width:100%}.CNusmb-haDnnc-JaPV2b .k77Iif-v3pZbf-LgbsSe>.LgbsSe-ssJRIf{background-color:#000;border:2px solid #fff;color:#fff}.CNusmb-haDnnc-HLvlvd .k77Iif-v3pZbf-LgbsSe>.LgbsSe-ssJRIf{background-color:#fff;border:2px solid #000;color:#000}.CNusmb-haDnnc .k77Iif-v3pZbf-LgbsSe>.LgbsSe-ssJRIf:focus{outline-style:auto}.CNusmb-haDnnc-JaPV2b .k77Iif-v3pZbf-LgbsSe>.LgbsSe-ssJRIf:hover,.CNusmb-haDnnc-JaPV2b .k77Iif-v3pZbf-LgbsSe>.LgbsSe-ssJRIf:active,.CNusmb-haDnnc-JaPV2b .k77Iif-v3pZbf-LgbsSe>.LgbsSe-ssJRIf:focus{background-color:#fff;color:#000}.CNusmb-haDnnc-HLvlvd .k77Iif-v3pZbf-LgbsSe>.LgbsSe-ssJRIf:hover,.CNusmb-haDnnc-HLvlvd .k77Iif-v3pZbf-LgbsSe>.LgbsSe-ssJRIf:active,.CNusmb-haDnnc-HLvlvd .k77Iif-v3pZbf-LgbsSe>.LgbsSe-ssJRIf:focus{background-color:#000;color:#fff}.cGMI2b-vOE8Lb-haAclf .YLEHIf-haAclf:not(.FnSee) .k77Iif-v3pZbf-LgbsSe{height:42px}.cGMI2b-vOE8Lb-haAclf .YLEHIf-haAclf:not(.FnSee) .k77Iif-v3pZbf-LgbsSe>.LgbsSe{overflow:visible}.cGMI2b-vOE8Lb-haAclf .YLEHIf-haAclf:not(.FnSee) .k77Iif-v3pZbf-LgbsSe>.LgbsSe::after{bottom:-6px;content:' ';left:0;position:absolute;right:0;top:-6px}@media screen and (-ms-high-contrast:active){.k77Iif-v3pZbf-LgbsSe>.LgbsSe{border:2px solid windowText}}.wk4LHf-haAclf{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-flex-direction:column-reverse;flex-direction:column-reverse}.wk4LHf{color:#202124;font-size:14px;letter-spacing:0.25px;line-height:20px;margin-top:8px;padding:0 16px}.YLEHIf-haAclf.FnSee .wk4LHf{display:none}.fFW7wc .u0pjoe-Ne3sFf{color:#5f6368;font-size:14px;letter-spacing:0.25px;line-height:20px;margin-top:8px;padding:0 16px}.u0pjoe{display:-webkit-box;display:-webkit-flex;display:flex;justify-content:flex-end;margin-top:4px;padding:0 16px}.u0pjoe>.LgbsSe{-webkit-border-radius:4px;border-radius:4px;background-color:#fff;border:1px solid #dadce0;color:#1a73e8;font-family:'Google Sans';height:36px;letter-spacing:0.25px;text-transform:none}.u0pjoe>.LgbsSe-ssJRIf:hover,.u0pjoe>.LgbsSe-ssJRIf:active{background-color:#e8f0fe;outline:none}.u0pjoe>.LgbsSe-ssJRIf:focus{background-color:#e8f0fe;outline:none;-webkit-box-shadow:0 1px 2px 0 rgba(26,115,232,0.302),0 1px 3px 1px rgba(26,115,232,0.149);box-shadow:0 1px 2px 0 rgba(26,115,232,0.302),0 1px 3px 1px rgba(26,115,232,0.149)}.WsjYwc-haAclf .YLEHIf-haAclf:not(.X9G3K) .fFW7wc-ibnC6b-sM5MNb:nth-child(n+3),.WsjYwc-haAclf .YLEHIf-haAclf.X9G3K .yePe5c-sM5MNb{display:none}.WsjYwc-haAclf .YLEHIf-haAclf.X9G3K .k77Iif{max-height:274.5px;overflow-y:auto}.WsjYwc-haAclf .YLEHIf-haAclf.X9G3K.FnSee .k77Iif{overflow-y:hidden}.lgKYGb-v0h5Oe-Ne3sFf{margin:12px 0;padding:0 16px;position:relative;overflow:hidden}.lgKYGb-v0h5Oe-Ne3sFf .k77Iif-v0h5Oe{font-family:'Google Sans';font-size:20px;line-height:28px;margin:0;width:70%}.lgKYGb-v0h5Oe-Ne3sFf .u6YOj-v0h5Oe{color:#5f6368;font-size:14px;line-height:20px;margin:10px 0 0 0;width:60%}.lgKYGb-v0h5Oe-Ne3sFf .lgKYGb-v0h5Oe-HiaYvf{bottom:2px;right:28px;position:absolute}.WsjYwc-haAclf .lgKYGb-v0h5Oe-Ne3sFf .lgKYGb-v0h5Oe-HiaYvf svg{width:120px;height:74px}sentinel{}
/*# sourceURL=/_/gsi/_/ss/k=gsi.gsi.IVArq21tg7M.L.W.O/am=whU/d=1/rs=AF0KOtWieN5fFQYOaa3RmPbB3e7nPQb2KA/m=credential_server_library */</style><script nonce="">"use strict";this.default_gsi=this.default_gsi||{};(function(_){var window=this;
try{
var aa,ba,ca,da,q,ea,ha;aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}};ba="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
ca=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("a");};da=ca(this);q=function(a,b){if(b)a:{var c=da;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&ba(c,a,{configurable:!0,writable:!0,value:b})}};
q("Symbol",function(a){if(a)return a;var b=function(f,g){this.g=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.g};var c="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("b");return new b(c+(f||"")+"_"+d++,f)};return e});
q("Symbol.iterator",function(a){if(a)return a;a=Symbol("c");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=da[b[c]];"function"===typeof d&&"function"!=typeof d.prototype[a]&&ba(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ea(aa(this))}})}return a});ea=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a};
_.u=function(a){var b="undefined"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];return b?b.call(a):{next:aa(a)}};_.fa="function"==typeof Object.create?Object.create:function(a){var b=function(){};b.prototype=a;return new b};if("function"==typeof Object.setPrototypeOf)ha=Object.setPrototypeOf;else{var ia;a:{var ja={a:!0},ka={};try{ka.__proto__=ja;ia=ka.a;break a}catch(a){}ia=!1}ha=ia?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError("d`"+a);return a}:null}_.la=ha;
q("Promise",function(a){function b(){this.g=null}function c(g){return g instanceof e?g:new e(function(h){h(g)})}if(a)return a;b.prototype.h=function(g){if(null==this.g){this.g=[];var h=this;this.i(function(){h.l()})}this.g.push(g)};var d=da.setTimeout;b.prototype.i=function(g){d(g,0)};b.prototype.l=function(){for(;this.g&&this.g.length;){var g=this.g;this.g=[];for(var h=0;h<g.length;++h){var l=g[h];g[h]=null;try{l()}catch(m){this.j(m)}}}this.g=null};b.prototype.j=function(g){this.i(function(){throw g;
})};var e=function(g){this.g=0;this.i=void 0;this.h=[];this.o=!1;var h=this.j();try{g(h.resolve,h.reject)}catch(l){h.reject(l)}};e.prototype.j=function(){function g(m){return function(n){l||(l=!0,m.call(h,n))}}var h=this,l=!1;return{resolve:g(this.F),reject:g(this.l)}};e.prototype.F=function(g){if(g===this)this.l(new TypeError("e"));else if(g instanceof e)this.J(g);else{a:switch(typeof g){case "object":var h=null!=g;break a;case "function":h=!0;break a;default:h=!1}h?this.C(g):this.m(g)}};e.prototype.C=
function(g){var h=void 0;try{h=g.then}catch(l){this.l(l);return}"function"==typeof h?this.D(h,g):this.m(g)};e.prototype.l=function(g){this.s(2,g)};e.prototype.m=function(g){this.s(1,g)};e.prototype.s=function(g,h){if(0!=this.g)throw Error("f`"+g+"`"+h+"`"+this.g);this.g=g;this.i=h;2===this.g&&this.B();this.A()};e.prototype.B=function(){var g=this;d(function(){if(g.v()){var h=da.console;"undefined"!==typeof h&&h.error(g.i)}},1)};e.prototype.v=function(){if(this.o)return!1;var g=da.CustomEvent,h=da.Event,
l=da.dispatchEvent;if("undefined"===typeof l)return!0;"function"===typeof g?g=new g("unhandledrejection",{cancelable:!0}):"function"===typeof h?g=new h("unhandledrejection",{cancelable:!0}):(g=da.document.createEvent("CustomEvent"),g.initCustomEvent("unhandledrejection",!1,!0,g));g.promise=this;g.reason=this.i;return l(g)};e.prototype.A=function(){if(null!=this.h){for(var g=0;g<this.h.length;++g)f.h(this.h[g]);this.h=null}};var f=new b;e.prototype.J=function(g){var h=this.j();g.Ra(h.resolve,h.reject)};
e.prototype.D=function(g,h){var l=this.j();try{g.call(h,l.resolve,l.reject)}catch(m){l.reject(m)}};e.prototype.then=function(g,h){function l(t,r){return"function"==typeof t?function(D){try{m(t(D))}catch(H){n(H)}}:r}var m,n,p=new e(function(t,r){m=t;n=r});this.Ra(l(g,m),l(h,n));return p};e.prototype.catch=function(g){return this.then(void 0,g)};e.prototype.Ra=function(g,h){function l(){switch(m.g){case 1:g(m.i);break;case 2:h(m.i);break;default:throw Error("g`"+m.g);}}var m=this;null==this.h?f.h(l):
this.h.push(l);this.o=!0};e.resolve=c;e.reject=function(g){return new e(function(h,l){l(g)})};e.race=function(g){return new e(function(h,l){for(var m=_.u(g),n=m.next();!n.done;n=m.next())c(n.value).Ra(h,l)})};e.all=function(g){var h=_.u(g),l=h.next();return l.done?c([]):new e(function(m,n){function p(D){return function(H){t[D]=H;r--;0==r&&m(t)}}var t=[],r=0;do t.push(void 0),r++,c(l.value).Ra(p(t.length-1),n),l=h.next();while(!l.done)})};return e});
var ma=function(a,b,c){if(null==a)throw new TypeError("h`"+c);if(b instanceof RegExp)throw new TypeError("i`"+c);return a+""};q("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=ma(this,b,"startsWith"),e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var g=0;g<f&&c<e;)if(d[c++]!=b[g++])return!1;return g>=f}});
q("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,f=0;f<e;f++){var g=d[f];if(b.call(c,g,f,d)){b=g;break a}}b=void 0}return b}});var na=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};
q("WeakMap",function(a){function b(){}function c(l){var m=typeof l;return"object"===m&&null!==l||"function"===m}function d(l){if(!na(l,f)){var m=new b;ba(l,f,{value:m})}}function e(l){var m=Object[l];m&&(Object[l]=function(n){if(n instanceof b)return n;Object.isExtensible(n)&&d(n);return m(n)})}if(function(){if(!a||!Object.seal)return!1;try{var l=Object.seal({}),m=Object.seal({}),n=new a([[l,2],[m,3]]);if(2!=n.get(l)||3!=n.get(m))return!1;n.delete(l);n.set(m,4);return!n.has(l)&&4==n.get(m)}catch(p){return!1}}())return a;
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var g=0,h=function(l){this.g=(g+=Math.random()+1).toString();if(l){l=_.u(l);for(var m;!(m=l.next()).done;)m=m.value,this.set(m[0],m[1])}};h.prototype.set=function(l,m){if(!c(l))throw Error("j");d(l);if(!na(l,f))throw Error("k`"+l);l[f][this.g]=m;return this};h.prototype.get=function(l){return c(l)&&na(l,f)?l[f][this.g]:void 0};h.prototype.has=function(l){return c(l)&&na(l,f)&&na(l[f],this.g)};h.prototype.delete=function(l){return c(l)&&
na(l,f)&&na(l[f],this.g)?delete l[f][this.g]:!1};return h});
q("Map",function(a){if(function(){if(!a||"function"!=typeof a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var h=Object.seal({x:4}),l=new a(_.u([[h,"s"]]));if("s"!=l.get(h)||1!=l.size||l.get({x:4})||l.set({x:4},"t")!=l||2!=l.size)return!1;var m=l.entries(),n=m.next();if(n.done||n.value[0]!=h||"s"!=n.value[1])return!1;n=m.next();return n.done||4!=n.value[0].x||"t"!=n.value[1]||!m.next().done?!1:!0}catch(p){return!1}}())return a;var b=new WeakMap,c=function(h){this.h={};this.g=
f();this.size=0;if(h){h=_.u(h);for(var l;!(l=h.next()).done;)l=l.value,this.set(l[0],l[1])}};c.prototype.set=function(h,l){h=0===h?0:h;var m=d(this,h);m.list||(m.list=this.h[m.id]=[]);m.N?m.N.value=l:(m.N={next:this.g,da:this.g.da,head:this.g,key:h,value:l},m.list.push(m.N),this.g.da.next=m.N,this.g.da=m.N,this.size++);return this};c.prototype.delete=function(h){h=d(this,h);return h.N&&h.list?(h.list.splice(h.index,1),h.list.length||delete this.h[h.id],h.N.da.next=h.N.next,h.N.next.da=h.N.da,h.N.head=
null,this.size--,!0):!1};c.prototype.clear=function(){this.h={};this.g=this.g.da=f();this.size=0};c.prototype.has=function(h){return!!d(this,h).N};c.prototype.get=function(h){return(h=d(this,h).N)&&h.value};c.prototype.entries=function(){return e(this,function(h){return[h.key,h.value]})};c.prototype.keys=function(){return e(this,function(h){return h.key})};c.prototype.values=function(){return e(this,function(h){return h.value})};c.prototype.forEach=function(h,l){for(var m=this.entries(),n;!(n=m.next()).done;)n=
n.value,h.call(l,n[1],n[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(h,l){var m=l&&typeof l;"object"==m||"function"==m?b.has(l)?m=b.get(l):(m=""+ ++g,b.set(l,m)):m="p_"+l;var n=h.h[m];if(n&&na(h.h,m))for(h=0;h<n.length;h++){var p=n[h];if(l!==l&&p.key!==p.key||l===p.key)return{id:m,list:n,index:h,N:p}}return{id:m,list:n,index:-1,N:void 0}},e=function(h,l){var m=h.g;return ea(function(){if(m){for(;m.head!=h.g;)m=m.da;for(;m.next!=m.head;)return m=m.next,{done:!1,value:l(m)};
m=null}return{done:!0,value:void 0}})},f=function(){var h={};return h.da=h.next=h.head=h},g=0;return c});var oa=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};
q("String.prototype.endsWith",function(a){return a?a:function(b,c){var d=ma(this,b,"endsWith");void 0===c&&(c=d.length);c=Math.max(0,Math.min(c|0,d.length));for(var e=b.length;0<e&&0<c;)if(d[--c]!=b[--e])return!1;return 0>=e}});q("Array.prototype.values",function(a){return a?a:function(){return oa(this,function(b,c){return c})}});q("Array.prototype.keys",function(a){return a?a:function(){return oa(this,function(b){return b})}});
q("Array.from",function(a){return a?a:function(b,c,d){c=null!=c?c:function(h){return h};var e=[],f="undefined"!=typeof Symbol&&Symbol.iterator&&b[Symbol.iterator];if("function"==typeof f){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}});var pa="function"==typeof Object.assign?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)na(d,e)&&(a[e]=d[e])}return a};
q("Object.assign",function(a){return a||pa});
q("Set",function(a){if(function(){if(!a||"function"!=typeof a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var c=Object.seal({x:4}),d=new a(_.u([c]));if(!d.has(c)||1!=d.size||d.add(c)!=d||1!=d.size||d.add({x:4})!=d||2!=d.size)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||4!=f.value[0].x||f.value[1]!=f.value[0]?!1:e.next().done}catch(g){return!1}}())return a;var b=function(c){this.g=new Map;if(c){c=
_.u(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.g.size};b.prototype.add=function(c){c=0===c?0:c;this.g.set(c,c);this.size=this.g.size;return this};b.prototype.delete=function(c){c=this.g.delete(c);this.size=this.g.size;return c};b.prototype.clear=function(){this.g.clear();this.size=0};b.prototype.has=function(c){return this.g.has(c)};b.prototype.entries=function(){return this.g.entries()};b.prototype.values=function(){return this.g.values()};b.prototype.keys=b.prototype.values;
b.prototype[Symbol.iterator]=b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.g.forEach(function(f){return c.call(d,f,f,e)})};return b});q("Object.is",function(a){return a?a:function(b,c){return b===c?0!==b||1/b===1/c:b!==b&&c!==c}});q("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(0>c&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});
q("String.prototype.includes",function(a){return a?a:function(b,c){return-1!==ma(this,b,"includes").indexOf(b,c||0)}});

}catch(e){_._DumpException(e)}
try{
var xa,Ha,Ka,La,Sa,Qa,Ta,Pa,Ya;_.qa=function(){var a=_.v.navigator;return a&&(a=a.userAgent)?a:""};_.w=function(a){return-1!=_.qa().indexOf(a)};_.ra=function(){return _.w("Opera")};_.sa=function(){return _.w("Trident")||_.w("MSIE")};_.ta=function(){return _.w("Firefox")||_.w("FxiOS")};_.va=function(){return _.w("Safari")&&!(_.ua()||_.w("Coast")||_.ra()||_.w("Edge")||_.w("Edg/")||_.w("OPR")||_.ta()||_.w("Silk")||_.w("Android"))};_.ua=function(){return(_.w("Chrome")||_.w("CriOS"))&&!_.w("Edge")||_.w("Silk")};
_.wa=function(){return _.w("Android")&&!(_.ua()||_.ta()||_.ra()||_.w("Silk"))};xa=function(){return _.w("iPhone")&&!_.w("iPod")&&!_.w("iPad")};_.ya=function(){return xa()||_.w("iPad")||_.w("iPod")};_.Ba=function(a,b){b=(0,_.za)(a,b);var c;(c=0<=b)&&Array.prototype.splice.call(a,b,1);return c};_.Ca=function(a){var b=a.length;if(0<b){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};_.Ea=function(a){return _.Da&&null!=a&&a instanceof Uint8Array};
_.Ga=function(a,b){Object.isFrozen(a)||(Fa?a[Fa]|=b:void 0!==a.Xa?a.Xa|=b:Object.defineProperties(a,{Xa:{value:b,configurable:!0,writable:!0,enumerable:!1}}))};Ha=function(a){var b;Fa?b=a[Fa]:b=a.Xa;return null==b?0:b};_.Ia=function(a){_.Ga(a,1);return a};_.Ja=function(a){return Array.isArray(a)?!!(Ha(a)&2):!1};Ka=function(a){return null!==a&&"object"===typeof a&&!Array.isArray(a)&&a.constructor===Object};La=function(a){return{value:a,configurable:!1,writable:!1,enumerable:!1}};
_.Oa=function(a){switch(typeof a){case "number":return isFinite(a)?a:String(a);case "object":if(a&&!Array.isArray(a)){if(_.Ea(a))return _.Ma(a);if("function"==typeof _.Na&&a instanceof _.Na)return a.h()}}return a};_.Ra=function(a,b){b=void 0===b?Pa:b;return Qa(a,b)};Sa=function(a,b){if(null!=a){if(Array.isArray(a))a=Qa(a,b);else if(Ka(a)){var c={},d;for(d in a)c[d]=Sa(a[d],b);a=c}else a=b(a);return a}};
Qa=function(a,b){for(var c=a.slice(),d=0;d<c.length;d++)c[d]=Sa(c[d],b);Array.isArray(a)&&Ha(a)&1&&_.Ia(c);return c};Ta=function(a){if(a&&"object"==typeof a&&a.toJSON)return a.toJSON();a=_.Oa(a);return Array.isArray(a)?_.Ra(a,Ta):a};Pa=function(a){return _.Ea(a)?new Uint8Array(a):a};_.Ua=function(a){return a?"[GSI_LOGGER-"+a+"]: ":"[GSI_LOGGER]: "};_.x=function(a,b){try{_.Va.debug>=_.Va[_.Wa]&&window.console&&window.console.log&&window.console.log(_.Ua(b)+a)}catch(c){}};
_.y=function(a,b){try{_.Va.warn>=_.Va[_.Wa]&&window.console&&window.console.warn&&window.console.warn(_.Ua(b)+a)}catch(c){}};_.z=function(a,b){try{_.Va.error>=_.Va[_.Wa]&&window.console&&window.console.error&&window.console.error(_.Ua(b)+a)}catch(c){}};Ya=function(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<Xa.length;f++)c=Xa[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};
_.A=function(a,b){a.prototype=(0,_.fa)(b.prototype);a.prototype.constructor=a;if(_.la)(0,_.la)(a,b);else for(var c in b)if("prototype"!=c)if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.ma=b.prototype};
/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
_.Za=_.Za||{};_.v=this||self;_.$a=function(){};_.ab=function(a){var b=typeof a;b="object"!=b?b:a?Array.isArray(a)?"array":b:"null";return"array"==b||"object"==b&&"number"==typeof a.length};_.bb=function(a){var b=typeof a;return"object"==b&&null!=a||"function"==b};_.B=function(a,b){a=a.split(".");var c=_.v;a[0]in c||"undefined"==typeof c.execScript||c.execScript("var "+a[0]);for(var d;a.length&&(d=a.shift());)a.length||void 0===b?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b};
_.cb=function(a,b){function c(){}c.prototype=b.prototype;a.ma=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Xc=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};
_.db="undefined"!==typeof TextDecoder;
_.eb=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};
_.za=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if("string"===typeof a)return"string"!==typeof b||1!=b.length?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};_.fb=Array.prototype.forEach?function(a,b){Array.prototype.forEach.call(a,b,void 0)}:function(a,b){for(var c=a.length,d="string"===typeof a?a.split(""):a,e=0;e<c;e++)e in d&&b.call(void 0,d[e],e,a)};
_.gb=Array.prototype.some?function(a,b){return Array.prototype.some.call(a,b,void 0)}:function(a,b){for(var c=a.length,d="string"===typeof a?a.split(""):a,e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a))return!0;return!1};_.hb=Array.prototype.every?function(a,b){return Array.prototype.every.call(a,b,void 0)}:function(a,b){for(var c=a.length,d="string"===typeof a?a.split(""):a,e=0;e<c;e++)if(e in d&&!b.call(void 0,d[e],e,a))return!1;return!0};
var ib=function(a){ib[" "](a);return a};ib[" "]=_.$a;
var pb;_.jb=_.ra();_.kb=_.sa();_.lb=_.w("Edge");_.mb=_.w("Gecko")&&!(-1!=_.qa().toLowerCase().indexOf("webkit")&&!_.w("Edge"))&&!(_.w("Trident")||_.w("MSIE"))&&!_.w("Edge");_.nb=-1!=_.qa().toLowerCase().indexOf("webkit")&&!_.w("Edge");_.ob=_.ya();
a:{var qb="",rb=function(){var a=_.qa();if(_.mb)return/rv:([^\);]+)(\)|;)/.exec(a);if(_.lb)return/Edge\/([\d\.]+)/.exec(a);if(_.kb)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(_.nb)return/WebKit\/(\S+)/.exec(a);if(_.jb)return/(?:Version)[ \/]?(\S+)/.exec(a)}();rb&&(qb=rb?rb[1]:"");if(_.kb){var sb,tb=_.v.document;sb=tb?tb.documentMode:void 0;if(null!=sb&&sb>parseFloat(qb)){pb=String(sb);break a}}pb=qb}_.ub=pb;
_.vb=_.ta();_.wb=xa()||_.w("iPod");_.xb=_.w("iPad");_.yb=_.wa();_.zb=_.ua();_.Bb=_.va()&&!_.ya();
var Cb;Cb={};_.Db=null;_.Ma=function(a){var b;void 0===b&&(b=0);_.Eb();b=Cb[b];for(var c=Array(Math.floor(a.length/3)),d=b[64]||"",e=0,f=0;e<a.length-2;e+=3){var g=a[e],h=a[e+1],l=a[e+2],m=b[g>>2];g=b[(g&3)<<4|h>>4];h=b[(h&15)<<2|l>>6];l=b[l&63];c[f++]=m+g+h+l}m=0;l=d;switch(a.length-e){case 2:m=a[e+1],l=b[(m&15)<<2]||d;case 1:a=a[e],c[f]=b[a>>2]+b[(a&3)<<4|m>>4]+l+d}return c.join("")};
_.Eb=function(){if(!_.Db){_.Db={};for(var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"],c=0;5>c;c++){var d=a.concat(b[c].split(""));Cb[c]=d;for(var e=0;e<d.length;e++){var f=d[e];void 0===_.Db[f]&&(_.Db[f]=e)}}}};
_.Da="function"===typeof Uint8Array;
_.Fb="function"===typeof Uint8Array.prototype.slice;
var Fa="function"===typeof Symbol&&"symbol"===typeof Symbol()?Symbol(void 0):void 0;
var Ib;_.Hb=Object.freeze(_.Ia([]));Ib="undefined"!=typeof Symbol&&"undefined"!=typeof Symbol.hasInstance;
_.C=function(a,b,c){return-1===b?null:b>=a.i?a.g?a.g[b]:void 0:(void 0===c?0:c)&&a.g&&(c=a.g[b],null!=c)?c:a.H[b+a.h]};
var Kb=function(a,b,c){a||(a=_.Jb);_.Jb=null;var d=this.constructor.h;a||(a=d?[d]:[]);this.h=(d?0:-1)-(this.constructor.g||0);this.G=void 0;this.H=a;a:{d=this.H.length;a=d-1;if(d&&(d=this.H[a],Ka(d))){this.i=a-this.h;this.g=d;break a}void 0!==b&&-1<b?(this.i=Math.max(b,a+1-this.h),this.g=void 0):this.i=Number.MAX_VALUE}if(c)for(b=0;b<c.length;b++)if(a=c[b],a<this.i)a+=this.h,(d=this.H[a])?Array.isArray(d)&&_.Ia(d):this.H[a]=_.Hb;else{d=this.g||(this.g=this.H[this.i+this.h]={});var e=d[a];e?Array.isArray(e)&&
_.Ia(e):d[a]=_.Hb}};Kb.prototype.toJSON=function(){var a=this.H;return _.Gb?a:_.Ra(a,Ta)};Kb.prototype.toString=function(){return this.H.toString()};
var Lb=function(){Kb.apply(this,arguments)};_.A(Lb,Kb);var Mb=function(){var a={};Object.defineProperties(Lb,(a[Symbol.hasInstance]=La(function(){throw Error("z");}),a))};Ib&&Mb();
_.Nb=Symbol();_.Ob=Symbol();
_.E=function(){Lb.apply(this,arguments)};_.A(_.E,Lb);var Pb=function(){var a={};Object.defineProperties(_.E,(a[Symbol.hasInstance]=La(Object[Symbol.hasInstance]),a))};Ib&&Pb();
_.Va={debug:0,info:1,warn:2,error:3};_.Wa="warn";
for(var Qb=[],Rb=0;63>Rb;Rb++)Qb[Rb]=0;_.Sb=function(a){return Array.prototype.concat.apply([],arguments)}(128,Qb);
var Xa="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");
_.Ub=function(a,b){this.g=b===_.Tb?a:""};_.k=_.Ub.prototype;_.k.ca=!0;_.k.Y=function(){return this.g.toString()};_.k.qb=!0;_.k.Ua=function(){return 1};_.k.toString=function(){return this.g.toString()};_.Tb={};_.Vb=new _.Ub("about:invalid#zClosurez",_.Tb);
var $b;_.Wb={};_.Xb=function(a,b,c){this.g=c===_.Wb?a:"";this.h=b;this.ca=this.qb=!0};_.Xb.prototype.Ua=function(){return this.h};_.Xb.prototype.Y=function(){return this.g.toString()};_.Xb.prototype.toString=function(){return this.g.toString()};_.Yb=function(a){return a instanceof _.Xb&&a.constructor===_.Xb?a.g:"type_error:SafeHtml"};$b=new _.Xb(_.v.trustedTypes&&_.v.trustedTypes.emptyHTML||"",0,_.Wb);
_.ac=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}}(function(){var a=document.createElement("div"),b=document.createElement("div");b.appendChild(document.createElement("div"));a.appendChild(b);b=a.firstChild.firstChild;a.innerHTML=_.Yb($b);return!b.parentElement});
_.bc=String.prototype.repeat?function(a,b){return a.repeat(b)}:function(a,b){return Array(b+1).join(a)};
_.cc=function(){this.sa=this.sa;this.l=this.l};_.cc.prototype.sa=!1;_.cc.prototype.P=function(){this.sa||(this.sa=!0,this.U())};_.cc.prototype.U=function(){if(this.l)for(;this.l.length;)this.l.shift()()};
_.dc=function(a,b){this.type=a;this.g=this.target=b;this.defaultPrevented=this.h=!1};_.dc.prototype.stopPropagation=function(){this.h=!0};_.dc.prototype.preventDefault=function(){this.defaultPrevented=!0};
var ec=function(){if(!_.v.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{_.v.addEventListener("test",_.$a,b),_.v.removeEventListener("test",_.$a,b)}catch(c){}return a}();
var gc=function(a,b){_.dc.call(this,a?a.type:"");this.relatedTarget=this.g=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.j=this.i=0;this.key="";this.charCode=this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.R=null;if(a){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.g=b;if(b=a.relatedTarget){if(_.mb){a:{try{ib(b.nodeName);
var e=!0;break a}catch(f){}e=!1}e||(b=null)}}else"mouseover"==c?b=a.fromElement:"mouseout"==c&&(b=a.toElement);this.relatedTarget=b;d?(this.clientX=void 0!==d.clientX?d.clientX:d.pageX,this.clientY=void 0!==d.clientY?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.i=_.nb||void 0!==a.offsetX?a.offsetX:a.layerX,this.j=_.nb||void 0!==a.offsetY?a.offsetY:a.layerY,this.clientX=void 0!==a.clientX?a.clientX:a.pageX,this.clientY=void 0!==a.clientY?a.clientY:a.pageY,this.screenX=
a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||("keypress"==c?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType="string"===typeof a.pointerType?a.pointerType:fc[a.pointerType]||"";this.state=a.state;this.R=a;a.defaultPrevented&&gc.ma.preventDefault.call(this)}};_.cb(gc,_.dc);var fc={2:"touch",3:"pen",4:"mouse"};
gc.prototype.stopPropagation=function(){gc.ma.stopPropagation.call(this);this.R.stopPropagation?this.R.stopPropagation():this.R.cancelBubble=!0};gc.prototype.preventDefault=function(){gc.ma.preventDefault.call(this);var a=this.R;a.preventDefault?a.preventDefault():a.returnValue=!1};gc.prototype.Ec=function(){return this.R};
var hc;hc="closure_listenable_"+(1E6*Math.random()|0);_.ic=function(a){return!(!a||!a[hc])};
var jc=0;
var kc=function(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.handler=e;this.key=++jc;this.Fa=this.Qa=!1},lc=function(a){a.Fa=!0;a.listener=null;a.proxy=null;a.src=null;a.handler=null};
var mc=function(a){this.src=a;this.g={};this.h=0},oc;mc.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.g[f];a||(a=this.g[f]=[],this.h++);var g=nc(a,b,d,e);-1<g?(b=a[g],c||(b.Qa=!1)):(b=new kc(b,this.src,f,!!d,e),b.Qa=c,a.push(b));return b};oc=function(a,b){var c=b.type;if(!(c in a.g))return!1;var d=_.Ba(a.g[c],b);d&&(lc(b),0==a.g[c].length&&(delete a.g[c],a.h--));return d};
_.pc=function(a,b){b=b&&b.toString();var c=0,d;for(d in a.g)if(!b||d==b){for(var e=a.g[d],f=0;f<e.length;f++)++c,lc(e[f]);delete a.g[d];a.h--}};mc.prototype.Da=function(a,b,c,d){a=this.g[a.toString()];var e=-1;a&&(e=nc(a,b,c,d));return-1<e?a[e]:null};var nc=function(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.Fa&&f.listener==b&&f.capture==!!c&&f.handler==d)return e}return-1};
var qc,rc,sc,vc,xc,Ac,yc,zc,Cc;qc="closure_lm_"+(1E6*Math.random()|0);rc={};sc=0;_.F=function(a,b,c,d,e){if(d&&d.once)return _.tc(a,b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)_.F(a,b[f],c,d,e);return null}c=_.uc(c);return _.ic(a)?a.I(b,c,_.bb(d)?!!d.capture:!!d,e):vc(a,b,c,!1,d,e)};
vc=function(a,b,c,d,e,f){if(!b)throw Error("D");var g=_.bb(e)?!!e.capture:!!e,h=_.wc(a);h||(a[qc]=h=new mc(a));c=h.add(b,c,d,g,f);if(c.proxy)return c;d=xc();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)ec||(e=g),void 0===e&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(yc(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("E");sc++;return c};xc=function(){var a=zc,b=function(c){return a.call(b.src,b.listener,c)};return b};
_.tc=function(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)_.tc(a,b[f],c,d,e);return null}c=_.uc(c);return _.ic(a)?a.ub(b,c,_.bb(d)?!!d.capture:!!d,e):vc(a,b,c,!0,d,e)};Ac=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)Ac(a,b[f],c,d,e);else d=_.bb(d)?!!d.capture:!!d,c=_.uc(c),_.ic(a)?a.na(b,c,d,e):a&&(a=_.wc(a))&&(b=a.Da(b,c,d,e))&&_.Bc(b)};
_.Bc=function(a){if("number"===typeof a||!a||a.Fa)return!1;var b=a.src;if(_.ic(b))return oc(b.V,a);var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(yc(c),d):b.addListener&&b.removeListener&&b.removeListener(d);sc--;(c=_.wc(b))?(oc(c,a),0==c.h&&(c.src=null,b[qc]=null)):lc(a);return!0};yc=function(a){return a in rc?rc[a]:rc[a]="on"+a};
zc=function(a,b){if(a.Fa)a=!0;else{b=new gc(b,this);var c=a.listener,d=a.handler||a.src;a.Qa&&_.Bc(a);a=c.call(d,b)}return a};_.wc=function(a){a=a[qc];return a instanceof mc?a:null};Cc="__closure_events_fn_"+(1E9*Math.random()>>>0);_.uc=function(a){if("function"===typeof a)return a;a[Cc]||(a[Cc]=function(b){return a.handleEvent(b)});return a[Cc]};
_.Dc=function(){_.cc.call(this);this.V=new mc(this);this.ua=this;this.D=null};_.cb(_.Dc,_.cc);_.Dc.prototype[hc]=!0;_.k=_.Dc.prototype;_.k.addEventListener=function(a,b,c,d){_.F(this,a,b,c,d)};_.k.removeEventListener=function(a,b,c,d){Ac(this,a,b,c,d)};
_.k.dispatchEvent=function(a){var b,c=this.D;if(c)for(b=[];c;c=c.D)b.push(c);c=this.ua;var d=a.type||a;if("string"===typeof a)a=new _.dc(a,c);else if(a instanceof _.dc)a.target=a.target||c;else{var e=a;a=new _.dc(d,c);Ya(a,e)}e=!0;if(b)for(var f=b.length-1;!a.h&&0<=f;f--){var g=a.g=b[f];e=Ec(g,d,!0,a)&&e}a.h||(g=a.g=c,e=Ec(g,d,!0,a)&&e,a.h||(e=Ec(g,d,!1,a)&&e));if(b)for(f=0;!a.h&&f<b.length;f++)g=a.g=b[f],e=Ec(g,d,!1,a)&&e;return e};
_.k.U=function(){_.Dc.ma.U.call(this);this.V&&_.pc(this.V,void 0);this.D=null};_.k.I=function(a,b,c,d){return this.V.add(String(a),b,!1,c,d)};_.k.ub=function(a,b,c,d){return this.V.add(String(a),b,!0,c,d)};_.k.na=function(a,b,c,d){var e=this.V;a=String(a).toString();if(a in e.g){var f=e.g[a];b=nc(f,b,c,d);-1<b&&(lc(f[b]),Array.prototype.splice.call(f,b,1),0==f.length&&(delete e.g[a],e.h--))}};
var Ec=function(a,b,c,d){b=a.V.g[String(b)];if(!b)return!0;b=b.concat();for(var e=!0,f=0;f<b.length;++f){var g=b[f];if(g&&!g.Fa&&g.capture==c){var h=g.listener,l=g.handler||g.src;g.Qa&&oc(a.V,g);e=!1!==h.call(l,d)&&e}}return e&&!d.defaultPrevented};_.Dc.prototype.Da=function(a,b,c,d){return this.V.Da(String(a),b,c,d)};
var Fc=function(){};Fc.prototype.g=null;
var Hc;Hc=function(){};_.cb(Hc,Fc);_.Gc=new Hc;
var Jc;_.Ic=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");Jc=function(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(0<=d){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}};
var Pc,Rc,Zc,Sc,Uc,Tc,Xc,Vc,$c;_.Kc=function(a,b){this.g=this.o=this.h="";this.s=null;this.m=this.i="";this.l=!1;var c;a instanceof _.Kc?(this.l=void 0!==b?b:a.l,_.Lc(this,a.h),this.o=a.o,this.g=a.g,_.Mc(this,a.s),this.i=a.i,_.Nc(this,Oc(a.j)),this.m=a.m):a&&(c=String(a).match(_.Ic))?(this.l=!!b,_.Lc(this,c[1]||"",!0),this.o=Pc(c[2]||""),this.g=Pc(c[3]||"",!0),_.Mc(this,c[4]),this.i=Pc(c[5]||"",!0),_.Nc(this,c[6]||"",!0),this.m=Pc(c[7]||"")):(this.l=!!b,this.j=new _.Qc(null,this.l))};
_.Kc.prototype.toString=function(){var a=[],b=this.h;b&&a.push(Rc(b,Sc,!0),":");var c=this.g;if(c||"file"==b)a.push("//"),(b=this.o)&&a.push(Rc(b,Sc,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.s,null!=c&&a.push(":",String(c));if(c=this.i)this.g&&"/"!=c.charAt(0)&&a.push("/"),a.push(Rc(c,"/"==c.charAt(0)?Tc:Uc,!0));(c=this.j.toString())&&a.push("?",c);(c=this.m)&&a.push("#",Rc(c,Vc));return a.join("")};
_.Kc.prototype.resolve=function(a){var b=new _.Kc(this),c=!!a.h;c?_.Lc(b,a.h):c=!!a.o;c?b.o=a.o:c=!!a.g;c?b.g=a.g:c=null!=a.s;var d=a.i;if(c)_.Mc(b,a.s);else if(c=!!a.i){if("/"!=d.charAt(0))if(this.g&&!this.i)d="/"+d;else{var e=b.i.lastIndexOf("/");-1!=e&&(d=b.i.substr(0,e+1)+d)}e=d;if(".."==e||"."==e)d="";else if(-1!=e.indexOf("./")||-1!=e.indexOf("/.")){d=0==e.lastIndexOf("/",0);e=e.split("/");for(var f=[],g=0;g<e.length;){var h=e[g++];"."==h?d&&g==e.length&&f.push(""):".."==h?((1<f.length||1==
f.length&&""!=f[0])&&f.pop(),d&&g==e.length&&f.push("")):(f.push(h),d=!0)}d=f.join("/")}else d=e}c?b.i=d:c=""!==a.j.toString();c?_.Nc(b,Oc(a.j)):c=!!a.m;c&&(b.m=a.m);return b};_.Lc=function(a,b,c){a.h=c?Pc(b,!0):b;a.h&&(a.h=a.h.replace(/:$/,""))};_.Mc=function(a,b){if(b){b=Number(b);if(isNaN(b)||0>b)throw Error("K`"+b);a.s=b}else a.s=null};_.Nc=function(a,b,c){b instanceof _.Qc?(a.j=b,Wc(a.j,a.l)):(c||(b=Rc(b,Xc)),a.j=new _.Qc(b,a.l))};
_.Yc=function(a){return a instanceof _.Kc?new _.Kc(a):new _.Kc(a,void 0)};Pc=function(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""};Rc=function(a,b,c){return"string"===typeof a?(a=encodeURI(a).replace(b,Zc),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null};Zc=function(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)};Sc=/[#\/\?@]/g;Uc=/[#\?:]/g;Tc=/[#\?]/g;Xc=/[#\?@]/g;Vc=/#/g;
_.Qc=function(a,b){this.h=this.g=null;this.i=a||null;this.j=!!b};$c=function(a){a.g||(a.g=new Map,a.h=0,a.i&&Jc(a.i,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))};_.Qc.prototype.add=function(a,b){$c(this);this.i=null;a=ad(this,a);var c=this.g.get(a);c||this.g.set(a,c=[]);c.push(b);this.h+=1;return this};_.bd=function(a,b){$c(a);b=ad(a,b);a.g.has(b)&&(a.i=null,a.h-=a.g.get(b).length,a.g.delete(b))};_.Qc.prototype.Ea=function(){$c(this);return 0==this.h};
var cd=function(a,b){$c(a);b=ad(a,b);return a.g.has(b)};_.k=_.Qc.prototype;_.k.forEach=function(a,b){$c(this);this.g.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};_.k.Ca=function(){$c(this);for(var a=Array.from(this.g.values()),b=Array.from(this.g.keys()),c=[],d=0;d<b.length;d++)for(var e=a[d],f=0;f<e.length;f++)c.push(b[d]);return c};
_.k.ba=function(a){$c(this);var b=[];if("string"===typeof a)cd(this,a)&&(b=b.concat(this.g.get(ad(this,a))));else{a=Array.from(this.g.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};_.k.set=function(a,b){$c(this);this.i=null;a=ad(this,a);cd(this,a)&&(this.h-=this.g.get(a).length);this.g.set(a,[b]);this.h+=1;return this};_.k.get=function(a,b){if(!a)return b;a=this.ba(a);return 0<a.length?String(a[0]):b};
_.dd=function(a,b,c){_.bd(a,b);0<c.length&&(a.i=null,a.g.set(ad(a,b),_.Ca(c)),a.h+=c.length)};_.Qc.prototype.toString=function(){if(this.i)return this.i;if(!this.g)return"";for(var a=[],b=Array.from(this.g.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.ba(d);for(var f=0;f<d.length;f++){var g=e;""!==d[f]&&(g+="="+encodeURIComponent(String(d[f])));a.push(g)}}return this.i=a.join("&")};
var Oc=function(a){var b=new _.Qc;b.i=a.i;a.g&&(b.g=new Map(a.g),b.h=a.h);return b},ad=function(a,b){b=String(b);a.j&&(b=b.toLowerCase());return b},Wc=function(a,b){b&&!a.j&&($c(a),a.i=null,a.g.forEach(function(c,d){var e=d.toLowerCase();d!=e&&(_.bd(this,d),_.dd(this,e,c))},a));a.j=b};
_.ed=window;

}catch(e){_._DumpException(e)}
try{
var ld,sd,td,ud,vd,wd,xd,yd,zd,Ad,Bd,Cd,Dd,pd,qd;_.fd=function(a){if(!Array.isArray(a))throw Error("w");_.Ga(a,2)};
_.gd=function(){var a=_.qa(),b="";_.w("Windows")?(b=/Windows (?:NT|Phone) ([0-9.]+)/,b=(a=b.exec(a))?a[1]:"0.0"):_.ya()?(b=/(?:iPhone|iPod|iPad|CPU)\s+OS\s+(\S+)/,b=(a=b.exec(a))&&a[1].replace(/_/g,".")):_.w("Macintosh")?(b=/Mac OS X ([0-9_.]+)/,b=(a=b.exec(a))?a[1].replace(/_/g,"."):"10"):-1!=_.qa().toLowerCase().indexOf("kaios")?(b=/(?:KaiOS)\/(\S+)/i,b=(a=b.exec(a))&&a[1]):_.w("Android")?(b=/Android\s+([^\);]+)(\)|;)/,b=(a=b.exec(a))&&a[1]):_.w("CrOS")&&(b=/(?:CrOS\s+(?:i686|x86_64)\s+([0-9.]+))/,
b=(a=b.exec(a))&&a[1]);return b||""};_.hd=function(a){if(!a.startsWith(")]}'\n"))throw console.error("malformed JSON response:",a),Error("O");a=a.substr(5);return _.v.JSON.parse(a)};_.jd=function(a){if(a instanceof _.id)return a.g;throw Error("P");};ld=function(a){return new _.kd(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})};
_.rd=function(a,b,c,d){b=b(c||md,d);if(_.bb(b))if(b instanceof _.nd){if(b.ya!==od)throw Error("R");b=pd(b.toString(),b.Ta||null)}else b=qd("zSoyz");else b=qd(String(b));if((0,_.ac)())for(;a.lastChild;)a.removeChild(a.lastChild);a.innerHTML=_.Yb(b)};_.G=function(a,b,c,d,e){if(-1===c)return null;a.G||(a.G={});var f=a.G[c];if(f)return f;e=_.C(a,c,void 0===e?!1:e);if(null==e&&!d)return f;b=new b(e);_.Ja(a.H)&&_.fd(b.H);return a.G[c]=b};
sd=function(a){if(a.ba&&"function"==typeof a.ba)return a.ba();if("undefined"!==typeof Map&&a instanceof Map||"undefined"!==typeof Set&&a instanceof Set)return Array.from(a.values());if("string"===typeof a)return a.split("");if(_.ab(a)){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}b=[];c=0;for(d in a)b[c++]=a[d];return b};
td=function(a){if(a.Ca&&"function"==typeof a.Ca)return a.Ca();if(!a.ba||"function"!=typeof a.ba){if("undefined"!==typeof Map&&a instanceof Map)return Array.from(a.keys());if(!("undefined"!==typeof Set&&a instanceof Set)){if(_.ab(a)||"string"===typeof a){var b=[];a=a.length;for(var c=0;c<a;c++)b.push(c);return b}b=[];c=0;for(var d in a)b[c++]=d;return b}}};ud=function(a){return a};vd=/&/g;wd=/</g;xd=/>/g;yd=/"/g;zd=/'/g;Ad=/\x00/g;Bd=/[\x00&<>"']/;_.I=function(a,b){a=_.C(a,b);return null==a?a:!!a};
Dd={};_.Ed=function(a,b){this.g=b===Dd?a:""};_.k=_.Ed.prototype;_.k.ca=!0;_.k.Y=function(){return this.g.toString()};_.k.qb=!0;_.k.Ua=function(){return 1};_.k.toString=function(){return this.g+""};_.Fd=function(a){return a instanceof _.Ed&&a.constructor===_.Ed?a.g:"type_error:TrustedResourceUrl"};_.Gd=function(a){return a instanceof _.Ub&&a.constructor===_.Ub?a.g:"type_error:SafeUrl"};
pd=function(a,b){if(void 0===Cd){var c=null;var d=_.v.trustedTypes;if(d&&d.createPolicy){try{c=d.createPolicy("goog#html",{createHTML:ud,createScript:ud,createScriptURL:ud})}catch(e){_.v.console&&_.v.console.error(e.message)}Cd=c}else Cd=c}a=(c=Cd)?c.createHTML(a):a;return new _.Xb(a,b,_.Wb)};
qd=function(a){if(a instanceof _.Xb)return a;var b="object"==typeof a,c=null;b&&a.qb&&(c=a.Ua());a=b&&a.ca?a.Y():String(a);Bd.test(a)&&(-1!=a.indexOf("&")&&(a=a.replace(vd,"&amp;")),-1!=a.indexOf("<")&&(a=a.replace(wd,"&lt;")),-1!=a.indexOf(">")&&(a=a.replace(xd,"&gt;")),-1!=a.indexOf('"')&&(a=a.replace(yd,"&quot;")),-1!=a.indexOf("'")&&(a=a.replace(zd,"&#39;")),-1!=a.indexOf("\x00")&&(a=a.replace(Ad,"&#0;")));return pd(a,c)};
_.Hd=function(a){var b=td(a);if("undefined"==typeof b)throw Error("M");var c=new _.Qc(null,void 0);a=sd(a);for(var d=0;d<b.length;d++){var e=b[d],f=a[d];Array.isArray(f)?_.dd(c,e,f):c.add(e,f)}return c};
_.Jd=function(a){_.E.call(this,a,-1,Id)};_.A(_.Jd,_.E);var Id=[9];
_.Kd=function(){};_.Kd.prototype.Za=function(a){var b=this;this.X&&window.setTimeout(function(){b.X(a)},100)};_.Ld=function(a,b,c){void 0!=c&&(b.detail=c);a.Za(b)};_.Md=function(a,b,c){_.Ld(a,{timestamp:(new Date).getTime(),type:"error",errorType:b},c)};
var Nd;_.J=function(a){Nd.g[a]=!0;_.x("Experiment "+a+" turned on.")};_.K=function(a){return!!Nd.g[a]};Nd=new function(){this.g={}};
_.Od=function(){var a=this;this.h=this.i=null;this.g=new Promise(function(b,c){a.i=b;a.h=c})};_.Od.prototype.resolve=function(a){if(!this.i)throw Error("N");this.i(a);this.P()};_.Od.prototype.reject=function(a){if(!this.h)throw Error("N");this.h(a);this.P()};_.Od.prototype.P=function(){this.h=this.i=null};
/*

 SPDX-License-Identifier: Apache-2.0
*/
_.Pd={};
_.Qd=function(){};_.id=function(a){this.g=a};_.A(_.id,_.Qd);_.id.prototype.toString=function(){return this.g};_.Rd=new _.id("about:invalid#zTSz",_.Pd);
_.kd=function(a){this.Hc=a};_.Sd=[ld("data"),ld("http"),ld("https"),ld("mailto"),ld("ftp"),new _.kd(function(a){return/^[^:]*([/?#]|$)/.test(a)})];
/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var od;od={};_.Td={};_.Ud={};_.nd=function(){throw Error("Q");};_.nd.prototype.Ta=null;_.nd.prototype.toString=function(){return this.content};var Vd=function(){_.nd.call(this)};_.cb(Vd,_.nd);Vd.prototype.ya=od;
_.Wd=function(a,b){return null!=a&&a.ya===b};
var Xd,ke,Zd,le,Yd,me,ge,ce,de;Xd=function(a){if(null!=a)switch(a.Ta){case 1:return 1;case -1:return-1;case 0:return 0}return null};_.M=function(a){return _.Wd(a,od)?a:a instanceof _.Xb?(0,_.L)(_.Yb(a).toString(),a.Ua()):(0,_.L)(String(String(a)).replace(Yd,Zd),Xd(a))};_.L=function(a){function b(c){this.content=c}b.prototype=a.prototype;return function(c,d){c=new b(String(c));void 0!==d&&(c.Ta=d);return c}}(Vd);_.ae=function(a){return a instanceof _.nd?!!a.content:!!a};
_.be=function(a){function b(c){this.content=c}b.prototype=a.prototype;return function(c,d){c=String(c);if(!c)return"";c=new b(c);void 0!==d&&(c.Ta=d);return c}}(Vd);_.O=function(a){_.Wd(a,od)?(a=String(a.content).replace(ce,"").replace(de,"&lt;"),a=_.ee(a)):a=String(a).replace(Yd,Zd);return a};
_.je=function(a){_.Wd(a,_.Td)||_.Wd(a,_.Ud)?a=_.fe(a):a instanceof _.Ub?a=_.fe(_.Gd(a)):a instanceof _.Qd?a=_.fe(_.jd(a)):a instanceof _.Ed?a=_.fe(_.Fd(a).toString()):(a=String(a),a=ge.test(a)?a.replace(_.he,_.ie):"about:invalid#zSoyz");return a};
ke={"\x00":"&#0;","\t":"&#9;","\n":"&#10;","\v":"&#11;","\f":"&#12;","\r":"&#13;"," ":"&#32;",'"':"&quot;","&":"&amp;","'":"&#39;","-":"&#45;","/":"&#47;","<":"&lt;","=":"&#61;",">":"&gt;","`":"&#96;","\u0085":"&#133;","\u00a0":"&#160;","\u2028":"&#8232;","\u2029":"&#8233;"};Zd=function(a){return ke[a]};
le={"\x00":"%00","\u0001":"%01","\u0002":"%02","\u0003":"%03","\u0004":"%04","\u0005":"%05","\u0006":"%06","\u0007":"%07","\b":"%08","\t":"%09","\n":"%0A","\v":"%0B","\f":"%0C","\r":"%0D","\u000e":"%0E","\u000f":"%0F","\u0010":"%10","\u0011":"%11","\u0012":"%12","\u0013":"%13","\u0014":"%14","\u0015":"%15","\u0016":"%16","\u0017":"%17","\u0018":"%18","\u0019":"%19","\u001a":"%1A","\u001b":"%1B","\u001c":"%1C","\u001d":"%1D","\u001e":"%1E","\u001f":"%1F"," ":"%20",'"':"%22","'":"%27","(":"%28",")":"%29",
"<":"%3C",">":"%3E","\\":"%5C","{":"%7B","}":"%7D","\u007f":"%7F","\u0085":"%C2%85","\u00a0":"%C2%A0","\u2028":"%E2%80%A8","\u2029":"%E2%80%A9","\uff01":"%EF%BC%81","\uff03":"%EF%BC%83","\uff04":"%EF%BC%84","\uff06":"%EF%BC%86","\uff07":"%EF%BC%87","\uff08":"%EF%BC%88","\uff09":"%EF%BC%89","\uff0a":"%EF%BC%8A","\uff0b":"%EF%BC%8B","\uff0c":"%EF%BC%8C","\uff0f":"%EF%BC%8F","\uff1a":"%EF%BC%9A","\uff1b":"%EF%BC%9B","\uff1d":"%EF%BC%9D","\uff1f":"%EF%BC%9F","\uff20":"%EF%BC%A0","\uff3b":"%EF%BC%BB",
"\uff3d":"%EF%BC%BD"};_.ie=function(a){return le[a]};Yd=/[\x00\x22\x26\x27\x3c\x3e]/g;me=/[\x00\x22\x27\x3c\x3e]/g;_.he=/[\x00- \x22\x27-\x29\x3c\x3e\\\x7b\x7d\x7f\x85\xa0\u2028\u2029\uff01\uff03\uff04\uff06-\uff0c\uff0f\uff1a\uff1b\uff1d\uff1f\uff20\uff3b\uff3d]/g;ge=/^[^&:\/?#]*(?:[\/?#]|$)|^https?:|^ftp:|^data:image\/[a-z0-9+]+;base64,[a-z0-9+\/]+=*$|^blob:/i;_.ee=function(a){return String(a).replace(me,Zd)};_.fe=function(a){return String(a).replace(_.he,_.ie)};ce=/<(?:!|\/?([a-zA-Z][a-zA-Z0-9:\-]*))(?:[^>'"]|"[^"]*"|'[^']*')*>/g;
de=/</g;
/*
 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var md={};
_.ne=function(a){return(0,_.L)((a?'<svg class="'+_.O("Bz112c")+" "+_.O("Bz112c-E3DyYd")+" "+_.O("Bz112c-uaxL4e")+'" aria-hidden=true viewBox="0 0 192 192">':'<svg class="'+_.O("fFW7wc-ibnC6b-HiaYvf")+" "+_.O("zTETae-mzNpsf-Bz112c")+" "+_.O("n1UuX-DkfjY")+'" aria-hidden=true viewBox="0 0 192 192">')+'<path fill="#3185FF" d="M96 8C47.42 8 8 47.42 8 96s39.42 88 88 88 88-39.42 88-88S144.58 8 96 8z"/><path fill="#FFFFFF" d="M96 86c12.17 0 22-9.83 22-22s-9.83-22-22-22-22 9.83-22 22 9.83 22 22 22zM96 99c-26.89 0-48 13-48 25 10.17 15.64 27.97 26 48 26s37.83-10.36 48-26c0-12-21.11-25-48-25z"/></svg>')};
_.oe=function(a){return{id:_.C(a,1),Pb:_.C(a,4),displayName:_.C(a,3),la:_.C(a,6)}};

_.J("cancelable_auto_select");

_.J("enable_inline_button");

_.J("enable_intermediate_iframe");

_.J("enable_iov2_fix");

_.J("enable_samesite_none_client_cookie");

_.J("variable_initial_height");

}catch(e){_._DumpException(e)}
try{
/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var ye,ze,Be,De,Ee,Fe,Ie,Je,Ke,Le,Me,Ne,Oe,Pe,Qe,Re,Se,Te,Ue,We,Xe,Ye,we,gf,kf;_.pe=function(a){for(var b=RegExp("([A-Z][\\w ]+)/([^\\s]+)\\s*(?:\\((.*?)\\))?","g"),c=[],d;d=b.exec(a);)c.push([d[1],d[2],d[3]||void 0]);return c};_.qe=function(a){var b={};a.forEach(function(c){b[c[0]]=c[1]});return function(c){return b[c.find(function(d){return d in b})]||""}};
_.re=function(a){var b=/rv: *([\d\.]*)/.exec(a);if(b&&b[1])return b[1];b="";var c=/MSIE +([\d\.]+)/.exec(a);if(c&&c[1])if(a=/Trident\/(\d.\d)/.exec(a),"7.0"==c[1])if(a&&a[1])switch(a[1]){case "4.0":b="8.0";break;case "5.0":b="9.0";break;case "6.0":b="10.0";break;case "7.0":b="11.0"}else b="7.0";else b=c[1];return b};_.se=function(a){_.Wa=void 0===a?"warn":a};
_.te=function(a){switch(_.C(a,1)){case 1:_.z("The specified user is not signed in.");break;case 2:_.z("User has opted out of using Google Sign In.");break;case 3:_.z("The given client ID is not found.");break;case 4:_.z("The given client ID is not allowed to use Google Sign In.");break;case 5:_.z("The given origin is not allowed for the given client ID.");break;case 6:_.z("Request from the same origin is expected.");break;case 7:_.z("Google Sign In is only allowed with HTTPS.");break;case 8:_.z("Parameter "+
_.C(a,2)+" is not set correctly.");break;case 9:_.z("The browser is not supported.");break;case 12:_.z("Google Sign In does not support web view.");break;case 14:_.z("The client is restricted to accounts within its organization.");break;default:_.z("An unknown error occurred.")}};_.ue=function(a,b,c){for(var d in a)b.call(c,a[d],d,a)};_.xe=function(a){var b=new ve;b.update(a,a.length);return we(b.digest())};ye=function(a,b,c){return a.call.apply(a.bind,arguments)};
ze=function(a,b,c){if(!a)throw Error();if(2<arguments.length){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}};_.Ae=function(a,b,c){Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?_.Ae=ye:_.Ae=ze;return _.Ae.apply(null,arguments)};Be=function(a,b){return a<b?-1:a>b?1:0};
_.Ce=function(a,b){var c=0;a=(0,_.eb)(String(a)).split(".");b=(0,_.eb)(String(b)).split(".");for(var d=Math.max(a.length,b.length),e=0;0==c&&e<d;e++){var f=a[e]||"",g=b[e]||"";do{f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];g=/(\d*)(\D*)(.*)/.exec(g)||["","","",""];if(0==f[0].length&&0==g[0].length)break;c=Be(0==f[1].length?0:parseInt(f[1],10),0==g[1].length?0:parseInt(g[1],10))||Be(0==f[2].length,0==g[2].length)||Be(f[2],g[2]);f=f[3];g=g[3]}while(0==c)}return c};De={};
Ee=function(a){var b=De;return Object.prototype.hasOwnProperty.call(b,9)?b[9]:b[9]=a(9)};Fe=function(){return Ee(function(){return 0<=_.Ce(_.ub,9)})};_.Ge=function(a){if(_.Ja(a.H))throw Error("x");};_.P=function(a,b,c,d,e){d=void 0===d?!1:d;(void 0===e?0:e)||_.Ge(a);b<a.i&&!d?a.H[b+a.h]=c:(a.g||(a.g=a.H[a.i+a.h]={}))[b]=c;return a};_.He=function(a,b){b=String(b);"application/xhtml+xml"===a.contentType&&(b=b.toLowerCase());return a.createElement(b)};
Ie=function(a){if(!a.h&&"undefined"==typeof XMLHttpRequest&&"undefined"!=typeof ActiveXObject){for(var b=["MSXML2.XMLHTTP.6.0","MSXML2.XMLHTTP.3.0","MSXML2.XMLHTTP","Microsoft.XMLHTTP"],c=0;c<b.length;c++){var d=b[c];try{return new ActiveXObject(d),a.h=d}catch(e){}}throw Error("F");}return a.h};Je=function(a){var b;(b=a.g)||(b={},Ie(a)&&(b[0]=!0,b[1]=!0),b=a.g=b);return b};Ke=function(a){return(a=Ie(a))?new ActiveXObject(a):new XMLHttpRequest};
Le=function(a,b,c){if("function"===typeof a)c&&(a=(0,_.Ae)(a,c));else if(a&&"function"==typeof a.handleEvent)a=(0,_.Ae)(a.handleEvent,a);else throw Error("H");return 2147483647<Number(b)?-1:_.v.setTimeout(a,b||0)};Me=/^https?$/i;Ne=["POST","PUT"];Oe=[];Pe=function(a){a.u&&a.Cb&&(a.u.ontimeout=null);a.ab&&(_.v.clearTimeout(a.ab),a.ab=null)};Qe=function(a){return _.kb&&Fe()&&"number"===typeof a.timeout&&void 0!==a.ontimeout};Re=function(a){a.pb||(a.pb=!0,a.dispatchEvent("complete"),a.dispatchEvent("error"))};
Se=function(a,b){if(a.u){Pe(a);var c=a.u,d=a.eb[0]?_.$a:null;a.u=null;a.eb=null;b||a.dispatchEvent("ready");try{c.onreadystatechange=d}catch(e){}}};Te=function(a){a.ka=!1;a.u&&(a.qa=!0,a.u.abort(),a.qa=!1);Re(a);Se(a)};Ue=function(a){return a.u?a.u.readyState:0};
_.Ve=function(a){var b=a.Va();a:switch(b){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var c=!0;break a;default:c=!1}if(!c){if(b=0===b)a=String(a.tb).match(_.Ic)[1]||null,!a&&_.v.self&&_.v.self.location&&(a=_.v.self.location.protocol,a=a.substr(0,a.length-1)),b=!Me.test(a?a.toLowerCase():"");c=b}return c};
We=function(a){if(a.ka&&"undefined"!=typeof _.Za&&(!a.eb[1]||4!=Ue(a)||2!=a.Va()))if(a.Wa&&4==Ue(a))Le(a.Vb,0,a);else if(a.dispatchEvent("readystatechange"),4==Ue(a)){a.ka=!1;try{_.Ve(a)?(a.dispatchEvent("complete"),a.dispatchEvent("success")):Re(a)}finally{Se(a)}}};Xe=function(a,b){return{type:b,lengthComputable:a.lengthComputable,loaded:a.loaded,total:a.total}};
Ye=function(a){_.Dc.call(this);this.headers=new Map;this.fb=a||null;this.ka=!1;this.eb=this.u=null;this.tb="";this.qa=this.rb=this.Wa=this.pb=!1;this.bb=0;this.ab=null;this.Xb="";this.Cb=this.Kc=this.Db=!1;this.Ab=null};_.cb(Ye,_.Dc);_.k=Ye.prototype;_.k.yc=function(){this.P();_.Ba(Oe,this)};_.k.setTrustToken=function(a){this.Ab=a};
_.k.send=function(a,b,c,d){if(this.u)throw Error("I`"+this.tb+"`"+a);b=b?b.toUpperCase():"GET";this.tb=a;this.pb=!1;this.ka=!0;this.u=this.fb?Ke(this.fb):Ke(_.Gc);this.eb=this.fb?Je(this.fb):Je(_.Gc);this.u.onreadystatechange=(0,_.Ae)(this.Vb,this);this.Kc&&"onprogress"in this.u&&(this.u.onprogress=(0,_.Ae)(function(g){this.Ub(g,!0)},this),this.u.upload&&(this.u.upload.onprogress=(0,_.Ae)(this.Ub,this)));try{this.rb=!0,this.u.open(b,String(a),!0),this.rb=!1}catch(g){Te(this);return}a=c||"";c=new Map(this.headers);
if(d)if(Object.getPrototypeOf(d)===Object.prototype)for(var e in d)c.set(e,d[e]);else if("function"===typeof d.keys&&"function"===typeof d.get){e=_.u(d.keys());for(var f=e.next();!f.done;f=e.next())f=f.value,c.set(f,d.get(f))}else throw Error("J`"+String(d));d=Array.from(c.keys()).find(function(g){return"content-type"==g.toLowerCase()});e=_.v.FormData&&a instanceof _.v.FormData;!(0<=(0,_.za)(Ne,b))||d||e||c.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");b=_.u(c);for(d=b.next();!d.done;d=
b.next())c=_.u(d.value),d=c.next().value,c=c.next().value,this.u.setRequestHeader(d,c);this.Xb&&(this.u.responseType=this.Xb);"withCredentials"in this.u&&this.u.withCredentials!==this.Db&&(this.u.withCredentials=this.Db);if("setTrustToken"in this.u&&this.Ab)try{this.u.setTrustToken(this.Ab)}catch(g){}try{Pe(this),0<this.bb&&((this.Cb=Qe(this.u))?(this.u.timeout=this.bb,this.u.ontimeout=(0,_.Ae)(this.Zb,this)):this.ab=Le(this.Zb,this.bb,this)),this.Wa=!0,this.u.send(a),this.Wa=!1}catch(g){Te(this)}};
_.k.Zb=function(){"undefined"!=typeof _.Za&&this.u&&(this.dispatchEvent("timeout"),this.abort(8))};_.k.abort=function(){this.u&&this.ka&&(this.ka=!1,this.qa=!0,this.u.abort(),this.qa=!1,this.dispatchEvent("complete"),this.dispatchEvent("abort"),Se(this))};_.k.U=function(){this.u&&(this.ka&&(this.ka=!1,this.qa=!0,this.u.abort(),this.qa=!1),Se(this,!0));Ye.ma.U.call(this)};_.k.Vb=function(){this.sa||(this.rb||this.Wa||this.qa?We(this):this.Jc())};_.k.Jc=function(){We(this)};
_.k.Ub=function(a,b){this.dispatchEvent(Xe(a,"progress"));this.dispatchEvent(Xe(a,b?"downloadprogress":"uploadprogress"))};_.k.Va=function(){try{return 2<Ue(this)?this.u.status:-1}catch(a){return-1}};_.k.getResponseHeader=function(a){if(this.u&&4==Ue(this))return a=this.u.getResponseHeader(a),null===a?void 0:a};_.k.getAllResponseHeaders=function(){return this.u&&4==Ue(this)?this.u.getAllResponseHeaders()||"":""};_.Ze=function(a){try{return a.u?a.u.responseText:""}catch(b){return""}};
we=function(a){return Array.prototype.map.call(a,function(b){b=b.toString(16);return 1<b.length?b:"0"+b}).join("")};_.$e=function(a){_.E.call(this,a)};_.A(_.$e,_.E);
var af=function(){this.blockSize=-1},bf,cf=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,
3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],df=function(a,b){this.blockSize=-1;this.blockSize=64;this.i=_.v.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.j=this.h=0;this.g=[];this.m=a;this.l=b;this.o=_.v.Int32Array?new Int32Array(64):Array(64);void 0===bf&&(_.v.Int32Array?bf=new Int32Array(cf):bf=
cf);this.reset()};_.cb(df,af);df.prototype.reset=function(){this.j=this.h=0;this.g=_.v.Int32Array?new Int32Array(this.l):_.Ca(this.l)};
var ef=function(a){for(var b=a.i,c=a.o,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=4*d;for(b=16;64>b;b++){e=c[b-15]|0;d=c[b-2]|0;var f=(c[b-16]|0)+((e>>>7|e<<25)^(e>>>18|e<<14)^e>>>3)|0,g=(c[b-7]|0)+((d>>>17|d<<15)^(d>>>19|d<<13)^d>>>10)|0;c[b]=f+g|0}d=a.g[0]|0;e=a.g[1]|0;var h=a.g[2]|0,l=a.g[3]|0,m=a.g[4]|0,n=a.g[5]|0,p=a.g[6]|0;f=a.g[7]|0;for(b=0;64>b;b++){var t=((d>>>2|d<<30)^(d>>>13|d<<19)^(d>>>22|d<<10))+(d&e^d&h^e&h)|0;g=m&n^~m&p;f=f+((m>>>6|m<<26)^(m>>>11|m<<21)^(m>>>
25|m<<7))|0;g=g+(bf[b]|0)|0;g=f+(g+(c[b]|0)|0)|0;f=p;p=n;n=m;m=l+g|0;l=h;h=e;e=d;d=g+t|0}a.g[0]=a.g[0]+d|0;a.g[1]=a.g[1]+e|0;a.g[2]=a.g[2]+h|0;a.g[3]=a.g[3]+l|0;a.g[4]=a.g[4]+m|0;a.g[5]=a.g[5]+n|0;a.g[6]=a.g[6]+p|0;a.g[7]=a.g[7]+f|0};
df.prototype.update=function(a,b){void 0===b&&(b=a.length);var c=0,d=this.h;if("string"===typeof a)for(;c<b;)this.i[d++]=a.charCodeAt(c++),d==this.blockSize&&(ef(this),d=0);else if(_.ab(a))for(;c<b;){var e=a[c++];if(!("number"==typeof e&&0<=e&&255>=e&&e==(e|0)))throw Error("B");this.i[d++]=e;d==this.blockSize&&(ef(this),d=0)}else throw Error("C");this.h=d;this.j+=b};
df.prototype.digest=function(){var a=[],b=8*this.j;56>this.h?this.update(_.Sb,56-this.h):this.update(_.Sb,this.blockSize-(this.h-56));for(var c=63;56<=c;c--)this.i[c]=b&255,b/=256;ef(this);for(c=b=0;c<this.m;c++)for(var d=24;0<=d;d-=8)a[b++]=this.g[c]>>d&255;return a};var ff=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],ve=function(){df.call(this,8,ff)};_.cb(ve,df);_.Q=function(a){var b=document;return"string"===typeof a?b.getElementById(a):a};
gf=function(a,b,c){var d;a=c||a;if(a.querySelectorAll&&a.querySelector&&b)return a.querySelectorAll(b?"."+b:"");if(b&&a.getElementsByClassName){var e=a.getElementsByClassName(b);return e}e=a.getElementsByTagName("*");if(b){var f={};for(c=d=0;a=e[c];c++){var g=a.className,h;if(h="function"==typeof g.split)h=0<=(0,_.za)(g.split(/\s+/),b);h&&(f[d++]=a)}f.length=d;return f}return e};
_.hf=function(a,b){var c=b||document;return c.querySelectorAll&&c.querySelector?c.querySelectorAll("."+a):gf(document,a,b)};_.jf=function(a,b){var c=b||document;if(c.getElementsByClassName)a=c.getElementsByClassName(a)[0];else{c=document;var d=b||c;a=d.querySelectorAll&&d.querySelector&&a?d.querySelector(a?"."+a:""):gf(c,a,b)[0]||null}return a||null};
kf={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};
_.lf=function(a,b){_.ue(b,function(c,d){c&&"object"==typeof c&&c.ca&&(c=c.Y());"style"==d?a.style.cssText=c:"class"==d?a.className=c:"for"==d?a.htmlFor=c:kf.hasOwnProperty(d)?a.setAttribute(kf[d],c):0==d.lastIndexOf("aria-",0)||0==d.lastIndexOf("data-",0)?a.setAttribute(d,c):a[d]=c})};_.mf=function(a){for(var b;b=a.firstChild;)a.removeChild(b)};_.nf=function(a){return a&&a.parentNode?a.parentNode.removeChild(a):null};
_.of=function(a,b){if(a)if(_.ic(a))a.V&&_.pc(a.V,b);else if(a=_.wc(a)){var c=0;b=b&&b.toString();for(var d in a.g)if(!b||d==b)for(var e=a.g[d].concat(),f=0;f<e.length;++f)_.Bc(e[f])&&++c}};_.pf=function(a,b,c,d,e,f,g){var h=new Ye;Oe.push(h);b&&h.I("complete",b);h.ub("ready",h.yc);f&&(h.bb=Math.max(0,f));g&&(h.Db=g);h.send(a,c,d,e)};
var rf;_.qf=function(a){this.g=a||{cookie:""}};_.k=_.qf.prototype;
_.k.set=function(a,b,c){var d=!1;if("object"===typeof c){var e=c.xb;d=c.yb||!1;var f=c.domain||void 0;var g=c.path||void 0;var h=c.Rb}if(/[;=\s]/.test(a))throw Error("S`"+a);if(/[;\r\n]/.test(b))throw Error("T`"+b);void 0===h&&(h=-1);this.g.cookie=a+"="+b+(f?";domain="+f:"")+(g?";path="+g:"")+(0>h?"":0==h?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+1E3*h)).toUTCString())+(d?";secure":"")+(null!=e?";samesite="+e:"")};
_.k.get=function(a,b){for(var c=a+"=",d=(this.g.cookie||"").split(";"),e=0,f;e<d.length;e++){f=(0,_.eb)(d[e]);if(0==f.lastIndexOf(c,0))return f.substr(c.length);if(f==a)return""}return b};_.k.Ca=function(){return rf(this).keys};_.k.ba=function(){return rf(this).values};_.k.Ea=function(){return!this.g.cookie};
rf=function(a){a=(a.g.cookie||"").split(";");for(var b=[],c=[],d,e,f=0;f<a.length;f++)e=(0,_.eb)(a[f]),d=e.indexOf("="),-1==d?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));return{keys:b,values:c}};_.sf=new _.qf("undefined"==typeof document?null:document);

}catch(e){_._DumpException(e)}
try{
var tf,vf,wf,xf,yf,zf,Df,Ef,Ff,Gf,Jf,Kf,Lf,Mf,Hf,If,Qf,Rf,Sf,Tf,Uf,Wf,Xf,Yf,$f,ag,Bf,Cf,dg,fg,gg,hg,ig,Nf,Of,kg,lg,pg,tg,yg,zg,Ag,Bg,Dg,Eg,Fg,Hg,Mg;tf=function(a,b){return _.Oa(b)};vf=function(a,b){b.j&&(a.j=b.j.slice());var c=b.G;if(c){b=b.g;for(var d in c){var e=c[d];if(e){var f=!(!b||!b[d]),g=+d;if(Array.isArray(e)){if(e.length)for(f=_.uf(a,e[0].constructor,g,f),g=0;g<Math.min(f.length,e.length);g++)vf(f[g],e[g])}else(f=_.G(a,e.constructor,g,void 0,f))&&vf(f,e)}}}};
wf=function(a){a&&"function"==typeof a.P&&a.P()};xf=function(){throw Error("s");};yf=function(a,b){b=String.fromCharCode.apply(null,b);return null==a?b:a+b};
zf=function(a){var b=_.qa();if("Internet Explorer"===a)return _.sa()?_.re(b):"";b=_.pe(b);var c=_.qe(b);switch(a){case "Opera":if(_.ra())return c(["Version","Opera"]);if(_.w("OPR"))return c(["OPR"]);break;case "Microsoft Edge":if(_.w("Edge"))return c(["Edge"]);if(_.w("Edg/"))return c(["Edg"]);break;case "Chromium":if(_.ua())return c(["Chrome","CriOS","HeadlessChrome"])}return"Firefox"===a&&_.ta()||"Safari"===a&&_.va()||"Android Browser"===a&&_.wa()||"Silk"===a&&_.w("Silk")?(a=b[2])&&a[1]||"":""};
_.Af=function(a){a=zf(a);if(""===a)return NaN;a=a.split(".");return 0===a.length?NaN:Number(a[0])};
Df=function(a,b){if(a.constructor===Uint8Array)return a;if(a.constructor===ArrayBuffer)return new Uint8Array(a);if(a.constructor===Array)return new Uint8Array(a);if(a.constructor===String)return Bf(a);if(a.constructor===_.Na){if(!b&&(b=a.g)&&b.constructor===Uint8Array)return b;if(a.Ea())a=Cf||(Cf=new Uint8Array(0));else{b=Uint8Array;var c=a.g;c=null==c||_.Ea(c)?c:"string"===typeof c?Bf(c):null;a=a.g=c;a=new b(a)}return a}if(a instanceof Uint8Array)return new Uint8Array(a.buffer,a.byteOffset,a.byteLength);
throw Error("v");};Ef=function(a,b,c){return a[_.Nb]||(a[_.Nb]=function(d,e){return b(d,e,c)})};Ff=function(a,b,c,d){var e=c.$a;a[b]=d?function(f,g,h){return e(f,g,h,d)}:e};Gf=function(a,b,c,d,e,f,g){var h=c.$a,l=Ef(d,e,f);a[b]=function(m,n,p){return h(m,n,p,d,l,g)}};Jf=function(a){var b=a[_.Nb];if(!b){var c=Hf(a);b=function(d,e){return If(d,e,c)};a[_.Nb]=b}return b};Kf=function(a){var b=a.Yc;if(b)return Jf(b);if(b=a.ed)return Ef(a.Dc.g,b,a.dd)};
Lf=function(a){var b=Kf(a),c=a.Dc,d=a.gd.$a;return b?function(e,f){return d(e,f,c,b)}:function(e,f){return d(e,f,c)}};Mf=function(a,b,c,d,e,f){var g=c.$a,h=Jf(e);a[b]=function(l,m,n){return g(l,m,n,d,h,f)}};
Hf=function(a){var b=a[_.Ob];if(!b){b=a[_.Ob]={};var c=Mf;a=a();var d=0;a.length&&"number"!==typeof a[0]&&(b[0]=a[0],d++);for(;d<a.length;){for(var e=a[d++],f=d+1;f<a.length&&"number"!==typeof a[f];)f++;var g=a[d++];f-=d;switch(f){case 0:Ff(b,e,g);break;case 1:Ff(b,e,g,a[d++]);break;case 2:c(b,e,g,a[d++],a[d++]);break;case 3:f=a[d++];var h=a[d++],l=a[d++];Array.isArray(l)?c(b,e,g,f,h,l):Gf(b,e,g,f,h,l);break;case 4:Gf(b,e,g,a[d++],a[d++],a[d++],a[d++]);break;default:throw Error("A`"+f);}}}return b};
If=function(a,b,c){for(;Nf(b)&&4!=b.h;){var d=b.i,e=c[d];if(!e){var f=c[0];f&&(f=f[d])&&(e=c[d]=Lf(f))}if(!e||!e(b,a,d))if(e=b,d=a,f=e.j,Of(e),!e.ob){var g=e.g.i;e=e.g.g;e=f===e?Cf||(Cf=new Uint8Array(0)):_.Fb?g.slice(f,e):new Uint8Array(g.subarray(f,e));(f=d.j)?f.push(e):d.j=[e]}}return a};_.Pf=function(a,b){return{$a:a,hd:b}};Qf=function(a){return _.G(a,this.g,this.h,void 0,!0)};
Rf=function(){function a(){e[0]=1732584193;e[1]=4023233417;e[2]=2562383102;e[3]=271733878;e[4]=3285377520;n=m=0}function b(p){for(var t=g,r=0;64>r;r+=4)t[r/4]=p[r]<<24|p[r+1]<<16|p[r+2]<<8|p[r+3];for(r=16;80>r;r++)p=t[r-3]^t[r-8]^t[r-14]^t[r-16],t[r]=(p<<1|p>>>31)&4294967295;p=e[0];var D=e[1],H=e[2],N=e[3],Ab=e[4];for(r=0;80>r;r++){if(40>r)if(20>r){var Aa=N^D&(H^N);var Zb=1518500249}else Aa=D^H^N,Zb=1859775393;else 60>r?(Aa=D&H|N&(D|H),Zb=2400959708):(Aa=D^H^N,Zb=3395469782);Aa=((p<<5|p>>>27)&4294967295)+
Aa+Ab+Zb+t[r]&4294967295;Ab=N;N=H;H=(D<<30|D>>>2)&4294967295;D=p;p=Aa}e[0]=e[0]+p&4294967295;e[1]=e[1]+D&4294967295;e[2]=e[2]+H&4294967295;e[3]=e[3]+N&4294967295;e[4]=e[4]+Ab&4294967295}function c(p,t){if("string"===typeof p){p=unescape(encodeURIComponent(p));for(var r=[],D=0,H=p.length;D<H;++D)r.push(p.charCodeAt(D));p=r}t||(t=p.length);r=0;if(0==m)for(;r+64<t;)b(p.slice(r,r+64)),r+=64,n+=64;for(;r<t;)if(f[m++]=p[r++],n++,64==m)for(m=0,b(f);r+64<t;)b(p.slice(r,r+64)),r+=64,n+=64}function d(){var p=
[],t=8*n;56>m?c(h,56-m):c(h,64-(m-56));for(var r=63;56<=r;r--)f[r]=t&255,t>>>=8;b(f);for(r=t=0;5>r;r++)for(var D=24;0<=D;D-=8)p[t++]=e[r]>>D&255;return p}for(var e=[],f=[],g=[],h=[128],l=1;64>l;++l)h[l]=0;var m,n;a();return{reset:a,update:c,digest:d,Ac:function(){for(var p=d(),t="",r=0;r<p.length;r++)t+="0123456789ABCDEF".charAt(Math.floor(p[r]/16))+"0123456789ABCDEF".charAt(p[r]%16);return t}}};
Sf=function(a,b,c){_.pf(a.url,function(d){d=d.target;_.Ve(d)?b(_.Ze(d)):c(d.Va())},a.Lc,a.body,a.wb,a.zb,a.withCredentials)};Tf=function(){this.i=Sf;this.g=!1};Uf=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}};
_.Vf=function(a,b,c,d){c=void 0===c?!0:c;d=void 0===d?!1:d;var e=_.C(a,b,d);null==e&&(e=_.Hb);if(_.Ja(a.H))c&&(_.fd(e),Object.freeze(e));else if(e===_.Hb||_.Ja(e))e=_.Ia(e.slice()),_.P(a,b,e,d);return e};_.uf=function(a,b,c,d){a.G||(a.G={});var e=_.Ja(a.H),f=a.G[c];if(!f){d=_.Vf(a,c,!0,void 0===d?!1:d);f=[];e=e||_.Ja(d);for(var g=0;g<d.length;g++)f[g]=new b(d[g]),e&&_.fd(f[g].H);e&&(_.fd(f),Object.freeze(f));a.G[c]=f}return f};
Wf=function(a){_.Gb=!0;try{return JSON.stringify(a.toJSON(),tf)}finally{_.Gb=!1}};Xf=function(a){var b=_.Ra(a.H);_.Jb=b;b=new a.constructor(b);_.Jb=null;vf(b,a);return b};Yf=function(a,b){a.sa?b():(a.l||(a.l=[]),a.l.push(b))};_.Zf=function(a){_.E.call(this,a)};_.A(_.Zf,_.E);
ag=function(a,b){function c(l){for(;d<a.length;){var m=a.charAt(d++),n=_.Db[m];if(null!=n)return n;if(!/^[\s\xa0]*$/.test(m))throw Error("t`"+m);}return l}_.Eb();for(var d=0;;){var e=c(-1),f=c(0),g=c(64),h=c(64);if(64===h&&-1===e)break;b(e<<2|f>>4);64!=g&&(b(f<<4&240|g>>2),64!=h&&b(g<<6&192|h))}};
Bf=function(a){var b=a.length,c=3*b/4;c%3?c=Math.floor(c):-1!="=.".indexOf(a[b-1])&&(c=-1!="=.".indexOf(a[b-2])?c-2:c-1);var d=new Uint8Array(c),e=0;ag(a,function(f){d[e++]=f});return e!==c?d.subarray(0,e):d};_.Na=function(a){this.g=a;if(null!==a&&0===a.length)throw Error("u");};_.Na.prototype.h=function(){if(this.Ea())var a="";else a=this.g,a=this.g=null==a||"string"===typeof a?a:_.Da&&a instanceof Uint8Array?_.Ma(a):null;return a};_.Na.prototype.Ea=function(){return null==this.g};
var cg=function(a,b){b=void 0===b?{}:b;b=void 0===b.$?!1:b.$;this.i=null;this.g=this.h=this.j=0;this.$=b;a&&bg(this,a)},bg=function(a,b){a.i=Df(b,a.$);a.j=0;a.h=a.i.length;a.g=a.j};cg.prototype.reset=function(){this.g=this.j};dg=function(a){if(a.g>a.h)throw Error("r`"+a.g+"`"+a.h);};
_.eg=function(a){var b=a.i,c=b[a.g],d=c&127;if(128>c)return a.g+=1,dg(a),d;c=b[a.g+1];d|=(c&127)<<7;if(128>c)return a.g+=2,dg(a),d;c=b[a.g+2];d|=(c&127)<<14;if(128>c)return a.g+=3,dg(a),d;c=b[a.g+3];d|=(c&127)<<21;if(128>c)return a.g+=4,dg(a),d;c=b[a.g+4];a.g+=5;d|=(c&15)<<28;if(128>c)return dg(a),d;if(128<=b[a.g++]&&128<=b[a.g++]&&128<=b[a.g++]&&128<=b[a.g++]&&128<=b[a.g++])throw Error("q");dg(a);return d};
fg=function(a){for(var b=0,c=a.g,d=c+10;c<d;){var e=a.i[c++];b|=e;if(0===(e&128))return a.g=c,dg(a),!!(b&127)}throw Error("q");};gg=[];hg=[];ig=function(a){var b={},c=void 0===b.ob?!1:b.ob;this.l={$:void 0===b.$?!1:b.$};this.ob=c;b=this.l;gg.length?(c=gg.pop(),b&&(c.$=b.$),a&&bg(c,a),a=c):a=new cg(a,b);this.g=a;this.j=this.g.g;this.h=this.i=-1};ig.prototype.reset=function(){this.g.reset();this.j=this.g.g;this.h=this.i=-1};
Nf=function(a){var b=a.g;if(b.g==b.h)return!1;a.j=a.g.g;var c=_.eg(a.g)>>>0;b=c>>>3;c&=7;if(!(0<=c&&5>=c))throw Error("m`"+c+"`"+a.j);if(1>b)throw Error("n`"+b+"`"+a.j);a.i=b;a.h=c;return!0};
Of=function(a){switch(a.h){case 0:0!=a.h?Of(a):fg(a.g);break;case 1:a=a.g;a.g+=8;dg(a);break;case 2:if(2!=a.h)Of(a);else{var b=_.eg(a.g)>>>0;a=a.g;a.g+=b;dg(a)}break;case 5:a=a.g;a.g+=4;dg(a);break;case 3:b=a.i;do{if(!Nf(a))throw Error("o");if(4==a.h){if(a.i!=b)throw Error("p");break}Of(a)}while(1);break;default:throw Error("m`"+a.h+"`"+a.j);}};_.jg=function(a,b,c){var d=a.g.h,e=_.eg(a.g)>>>0,f=a.g.g+e,g=f-d;0>=g&&(a.g.h=f,c(b,a),g=f-a.g.g);if(g)throw Error("l`"+e+"`"+(e-g));a.g.g=f;a.g.h=d};
kg=function(a){var b=_.eg(a.g)>>>0;a=a.g;var c=a.g;a.g+=b;dg(a);a=a.i;var d;if(_.db)(d=$f)||(d=$f=new TextDecoder("utf-8",{fatal:!0})),d=d.decode(a.subarray(c,c+b));else{b=c+b;for(var e=[],f=null,g,h,l;c<b;)g=a[c++],128>g?e.push(g):224>g?c>=b?xf():(h=a[c++],194>g||128!==(h&192)?(c--,xf()):e.push((g&31)<<6|h&63)):240>g?c>=b-1?xf():(h=a[c++],128!==(h&192)||224===g&&160>h||237===g&&160<=h||128!==((d=a[c++])&192)?(c--,xf()):e.push((g&15)<<12|(h&63)<<6|d&63)):244>=g?c>=b-2?xf():(h=a[c++],128!==(h&192)||
0!==(g<<28)+(h-144)>>30||128!==((d=a[c++])&192)||128!==((l=a[c++])&192)?(c--,xf()):(g=(g&7)<<18|(h&63)<<12|(d&63)<<6|l&63,g-=65536,e.push((g>>10&1023)+55296,(g&1023)+56320))):xf(),8192<=e.length&&(f=yf(f,e),e.length=0);d=yf(f,e)}return d};lg=function(a,b){a=_.C(a,1);return null==a?b:a};_.mg=function(a,b,c){var d=void 0===d?!1:d;_.Ge(a);a.G||(a.G={});var e=c?c.H:c;a.G[b]=c;return _.P(a,b,e,d)};
_.ng=function(a,b,c){if(hg.length){var d=hg.pop();a&&(bg(d.g,a),d.i=-1,d.h=-1);a=d}else a=new ig(a);try{return If(new b,a,Hf(c))}finally{b=a.g,b.i=null,b.j=0,b.h=0,b.g=0,b.$=!1,a.i=-1,a.h=-1,100>hg.length&&hg.push(a)}};_.og=_.Pf(function(a,b,c){if(0!==a.h)return!1;_.P(b,c,fg(a.g));return!0},function(a,b,c){a.g(c,_.C(b,c))});_.R=_.Pf(function(a,b,c){if(2!==a.h)return!1;_.P(b,c,kg(a));return!0},function(a,b,c){a.o(c,_.C(b,c))});
pg=_.Pf(function(a,b,c){if(2!==a.h)return!1;a=kg(a);_.Ge(b);_.Vf(b,c).push(a);return!0},function(a,b,c){a.m(c,_.Vf(b,c))});_.qg=_.Pf(function(a,b,c,d,e){if(2!==a.h)return!1;var f=void 0===f?!1:f;_.Ge(b);b.G||(b.G={});var g=b.G[c];g?b=g:(g=_.C(b,c,f),d=new d(g),null==g&&_.P(b,c,d.H,f),b=b.G[c]=d);_.jg(a,b,e);return!0},function(a,b,c,d,e){a.j(c,_.G(b,d,c),e)});_.rg=_.Pf(function(a,b,c){if(0!==a.h)return!1;_.P(b,c,_.eg(a.g));return!0},function(a,b,c){a.h(c,_.C(b,c))});
_.sg=function(){return[1,_.rg,2,_.R]};tg=function(a,b){return a+Math.random()*(b-a)};_.ug=function(a){return _.jf(a,void 0)};_.vg=function(a,b){_.Dc.call(this);this.j=a||1;this.i=b||_.v;this.m=(0,_.Ae)(this.s,this);this.o=Date.now()};_.cb(_.vg,_.Dc);_.vg.prototype.h=!1;_.vg.prototype.g=null;var xg=function(a,b){a.j=b;a.g&&a.h?(_.wg(a),a.start()):a.g&&_.wg(a)};
_.vg.prototype.s=function(){if(this.h){var a=Date.now()-this.o;0<a&&a<.8*this.j?this.g=this.i.setTimeout(this.m,this.j-a):(this.g&&(this.i.clearTimeout(this.g),this.g=null),this.dispatchEvent("tick"),this.h&&(_.wg(this),this.start()))}};_.vg.prototype.start=function(){this.h=!0;this.g||(this.g=this.i.setTimeout(this.m,this.j),this.o=Date.now())};_.wg=function(a){a.h=!1;a.g&&(a.i.clearTimeout(a.g),a.g=null)};_.vg.prototype.U=function(){_.vg.ma.U.call(this);_.wg(this);delete this.i};
yg=function(a,b){if(!b)return a;var c=a.indexOf("#");0>c&&(c=a.length);var d=a.indexOf("?");if(0>d||d>c){d=c;var e=""}else e=a.substring(d+1,c);a=[a.substr(0,d),e,a.substr(c)];c=a[1];a[1]=b?c?c+"&"+b:b:c;return a[0]+(a[1]?"?"+a[1]:"")+a[2]};zg=function(a,b,c){if(Array.isArray(b))for(var d=0;d<b.length;d++)zg(a,String(b[d]),c);else null!=b&&c.push(a+(""===b?"":"="+encodeURIComponent(String(b))))};Ag=function(a,b){var c=[];for(b=b||0;b<a.length;b+=2)zg(a[b],a[b+1],c);return c.join("&")};
Bg=function(a,b){var c=2==arguments.length?Ag(arguments[1],0):Ag(arguments,1);return yg(a,c)};_.Cg=function(a,b,c){c=null!=c?"="+encodeURIComponent(String(c)):"";return yg(a,b+c)};Dg=function(a,b,c,d){for(var e=c.length;0<=(b=a.indexOf(c,b))&&b<d;){var f=a.charCodeAt(b-1);if(38==f||63==f)if(f=a.charCodeAt(b+e),!f||61==f||38==f||35==f)return b;b+=e+1}return-1};Eg=/#|$/;Fg=/[?&]($|#)/;
_.Gg=function(a,b){for(var c=a.search(Eg),d=0,e,f=[];0<=(e=Dg(a,d,b,c));)f.push(a.substring(d,e)),d=Math.min(a.indexOf("&",e)+1||c,c);f.push(a.substr(d));return f.join("").replace(Fg,"$1")};Hg=function(){return[1,_.R]};_.Ig=function(){return[1,_.R,2,_.R,3,_.R,4,_.R,5,_.R,6,_.R,7,_.og,8,_.qg,_.Zf,Hg,9,pg]};_.Jg=function(a){_.E.call(this,a)};_.A(_.Jg,_.E);_.Kg=function(){return[1,_.R,2,_.R,3,_.R]};Mg=function(a){_.E.call(this,a,-1,Lg)};_.A(Mg,_.E);var Lg=[2];
var Ng=function(a){if(!a)return"";if(/^about:(?:blank|srcdoc)$/.test(a))return window.origin||"";a=a.split("#")[0].split("?")[0];a=a.toLowerCase();0==a.indexOf("//")&&(a=window.location.protocol+a);/^[\w\-]*:\/\//.test(a)||(a=window.location.href);var b=a.substring(a.indexOf("://")+3),c=b.indexOf("/");-1!=c&&(b=b.substring(0,c));c=a.substring(0,a.indexOf("://"));if(!c)throw Error("U`"+a);if("http"!==c&&"https"!==c&&"chrome-extension"!==c&&"moz-extension"!==c&&"file"!==c&&"android-app"!==c&&"chrome-search"!==
c&&"chrome-untrusted"!==c&&"chrome"!==c&&"app"!==c&&"devtools"!==c)throw Error("V`"+c);a="";var d=b.indexOf(":");if(-1!=d){var e=b.substring(d+1);b=b.substring(0,d);if("http"===c&&"80"!==e||"https"===c&&"443"!==e)a=":"+e}return c+"://"+b+a};
_.Og=function(a){_.E.call(this,a)};_.A(_.Og,_.E);_.Pg=function(){return[1,_.R,2,_.R,3,_.R,4,_.R,5,_.R,6,_.R,7,_.R]};
var Qg=function(){};_.A(Qg,_.Kd);
/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
_.Rg=RegExp("'([{}#].*?)'","g");_.Sg=RegExp("''","g");
_.Tg=function(a,b){this.g=a;this.h=b.container;this.A=new Set;this.J=!1};_.Tg.prototype.I=function(a,b,c){a=_.F(a,b,c);this.A.add(a);return a};_.Tg.prototype.na=function(a){_.Bc(a);this.A.delete(a)};_.S=function(a,b,c){a.I(b,"click",c);a.I(b,"keydown",function(d){var e=d.charCode||d.keyCode;13!=e&&3!=e&&32!=e||c(d)})};_.Ug=function(a){a=void 0===a?50:a;return new Promise(function(b){setTimeout(b,a)})};_.Vg=function(){return new Promise(function(a){requestAnimationFrame(function(){requestAnimationFrame(function(){a()})})})};
_.Wg=function(){return _.Ug().then(function(){return _.Vg()})};_.k=_.Tg.prototype;_.k.M=function(){};_.k.close=function(){};_.k.Aa=function(){};_.k.za=function(){return this.h.offsetHeight};_.k.jb=function(){return this.h.offsetWidth};_.k.P=function(){this.J||(this.A.forEach(function(a){_.Bc(a)}),this.A.clear(),_.mf(this.h),this.J=!0)};
_.Xg=function(a){this.h=a;this.g=null};_.A(_.Xg,Qg);_.Xg.prototype.i=function(){return Promise.resolve()};_.Xg.prototype.j=function(a){this.g&&(this.g.P(),this.g=null);this.g=a;return this.g.M()};
var Zg=function(a,b,c){var d=String(_.v.location.href);return d&&a&&b?[b,Yg(Ng(d),a,c||null)].join(" "):null},Yg=function(a,b,c){var d=[],e=[];if(1==(Array.isArray(c)?2:1))return e=[b,a],_.fb(d,function(h){e.push(h)}),$g(e.join(" "));var f=[],g=[];_.fb(c,function(h){g.push(h.key);f.push(h.value)});c=Math.floor((new Date).getTime()/1E3);e=0==f.length?[c,b,a]:[f.join(":"),c,b,a];_.fb(d,function(h){e.push(h)});a=$g(e.join(" "));a=[c,a];0==g.length||a.push(g.join(""));return a.join("_")},$g=function(a){var b=
Rf();b.update(a);return b.Ac().toLowerCase()};
var ah={};
var bh=function(a){return!!ah.FPA_SAMESITE_PHASE2_MOD||!(void 0===a||!a)},ch=function(a,b,c,d){(a=_.v[a])||(a=(new _.qf(document)).get(b));return a?Zg(a,c,d):null},dh=function(a,b){b=void 0===b?!1:b;var c=Ng(String(_.v.location.href)),d=[];var e=b;e=void 0===e?!1:e;var f=_.v.__SAPISID||_.v.__APISID||_.v.__3PSAPISID||_.v.__OVERRIDE_SID;bh(e)&&(f=f||_.v.__1PSAPISID);if(f)e=!0;else{var g=new _.qf(document);f=g.get("SAPISID")||g.get("APISID")||g.get("__Secure-3PAPISID")||g.get("SID");bh(e)&&(f=f||g.get("__Secure-1PAPISID"));
e=!!f}e&&(e=(c=0==c.indexOf("https:")||0==c.indexOf("chrome-extension:")||0==c.indexOf("moz-extension:"))?_.v.__SAPISID:_.v.__APISID,e||(e=new _.qf(document),e=e.get(c?"SAPISID":"APISID")||e.get("__Secure-3PAPISID")),(e=e?Zg(e,c?"SAPISIDHASH":"APISIDHASH",a):null)&&d.push(e),c&&bh(b)&&((b=ch("__1PSAPISID","__Secure-1PAPISID","SAPISID1PHASH",a))&&d.push(b),(a=ch("__3PSAPISID","__Secure-3PAPISID","SAPISID3PHASH",a))&&d.push(a)));return 0==d.length?null:d.join(" ")};
var eh=function(a){this.h=this.g=this.i=a};eh.prototype.reset=function(){this.h=this.g=this.i};
var fh=function(a){_.E.call(this,a)};_.A(fh,_.E);var hh=function(a){_.E.call(this,a,-1,gh)};_.A(hh,_.E);var gh=[1];
var ih=function(a){_.E.call(this,a)};_.A(ih,_.E);
var kh=function(a){_.E.call(this,a,31,jh)};_.A(kh,_.E);var jh=[3,20,27];
var mh=function(a){_.E.call(this,a,17,lh)};_.A(mh,_.E);var lh=[3,5];
var oh=function(a){_.E.call(this,a,6,nh)};_.A(oh,_.E);var nh=[5];
var ph=function(a){_.E.call(this,a)};_.A(ph,_.E);
var qh=new function(a,b){this.h=a;this.g=b;this.i=Qf}(175237375,ph);
var uh=function(a,b,c,d,e,f,g,h,l,m,n){_.Dc.call(this);var p=this;this.h=[];this.ia="";this.fa=!1;this.ja=this.X=-1;this.ga=!1;this.s=this.i=null;this.o=0;this.wa=1;this.zb=0;this.K=!1;_.Dc.call(this);this.ha=b||function(){};this.m=new rh(a,f);this.ta=d;this.T=n;this.xa=Uf(tg,0,1);this.C=e||null;this.A=c||null;this.F=g||!1;this.J=l||null;this.withCredentials=!h;this.O=f||!1;this.va=!this.O&&(65<=_.Af("Chromium")||45<=_.Af("Firefox")||12<=_.Af("Safari")||_.ya()&&0<=_.Ce(_.gd(),12))&&!!window&&!!window.navigator&&
!!window.navigator.sendBeacon;a=_.P(new ih,1,1);sh(this.m,a);this.j=new eh(1E4);this.g=new _.vg(this.j.g);Yf(this,Uf(wf,this.g));m=th(this,m);_.F(this.g,"tick",m,!1,this);this.v=new _.vg(6E5);Yf(this,Uf(wf,this.v));_.F(this.v,"tick",m,!1,this);this.F||this.v.start();this.O||(_.F(document,"visibilitychange",function(){"hidden"===document.visibilityState&&p.B()}),_.F(document,"pagehide",this.B,!1,this))};_.A(uh,_.Dc);var th=function(a,b){return b?function(){b().then(function(){a.flush()})}:function(){a.flush()}};
uh.prototype.U=function(){this.B();_.Dc.prototype.U.call(this)};var vh=function(a){a.C||(a.C=.01>a.xa()?"https://www.google.com/log?format=json&hasfast=true":"https://play.google.com/log?format=json&hasfast=true");return a.C},wh=function(a,b){a.j=new eh(1>b?1:b);xg(a.g,a.j.g)};
uh.prototype.log=function(a){a=Xf(a);var b=this.wa++;_.P(a,21,b);_.C(a,1)||_.P(a,1,Date.now().toString());null!=_.C(a,15)||_.P(a,15,60*(new Date).getTimezoneOffset());this.i&&(b=Xf(this.i),_.mg(a,16,b));for(;1E3<=this.h.length;)this.h.shift(),++this.o;this.h.push(a);this.dispatchEvent(new xh(a));this.F||this.g.h||this.g.start()};
uh.prototype.flush=function(a,b){var c=this;if(0===this.h.length)a&&a();else if(this.K)yh(this);else{var d=Date.now();if(this.ja>d&&this.X<d)b&&b("throttled");else{var e=zh(this.m,this.h,this.o);d={};var f=this.ha();f&&(d.Authorization=f);var g=vh(this);this.A&&(d["X-Goog-AuthUser"]=this.A,g=_.Cg(g,"authuser",this.A));this.J&&(d["X-Goog-PageId"]=this.J,g=_.Cg(g,"pageId",this.J));if(f&&this.ia===f)b&&b("stale-auth-token");else{this.h=[];this.g.h&&_.wg(this.g);this.o=0;var h=Wf(e),l;this.s&&this.s.bd(h.length)&&
(l=this.s.Zc(h));var m={url:g,body:h,wc:1,wb:d,Lc:"POST",withCredentials:this.withCredentials,zb:this.zb},n=function(r){c.j.reset();xg(c.g,c.j.g);if(r){var D=null;try{var H=JSON.parse(r.replace(")]}'\n",""));D=new oh(H)}catch(N){}D&&(r=Number(lg(D,"-1")),0<r&&(c.X=Date.now(),c.ja=c.X+r),D=qh.i(D))&&(D=lg(D,-1),-1!=D&&(c.ga||wh(c,D)))}a&&a()},p=function(r){var D=_.uf(e,kh,3),H=c.j;H.h=Math.min(3E5,2*H.h);H.g=Math.min(3E5,H.h+Math.round(.2*(Math.random()-.5)*H.h));xg(c.g,c.j.g);401===r&&f&&(c.ia=f);
if(500<=r&&600>r||401===r||0===r)c.h=D.concat(c.h),c.F||c.g.h||c.g.start();b&&b("net-send-failed",r)},t=function(){c.T?c.T.send(m,n,p):c.ta(m,n,p)};l?l.then(function(r){m.wb["Content-Encoding"]="gzip";m.wb["Content-Type"]="application/binary";m.body=r;m.wc=2;t()},function(){t()}):t()}}}};uh.prototype.B=function(){this.fa&&yh(this);this.flush()};
var yh=function(a){Ah(a,function(b,c){b=_.Cg(b,"format","json");b=window.navigator.sendBeacon(b,Wf(c));a.K&&!b&&(a.K=!1);return b})},Ah=function(a,b){if(0!==a.h.length){var c=_.Gg(vh(a),"format");c=Bg(c,"auth",a.ha(),"authuser",a.A||"0");for(var d=0;10>d&&a.h.length;++d){var e=a.h.slice(0,32),f=zh(a.m,e,a.o);if(!b(c,f))break;a.o=0;a.h=a.h.slice(e.length)}a.g.h&&_.wg(a.g)}},xh=function(){_.dc.call(this,"event-logged",void 0)};_.A(xh,_.dc);
var rh=function(a,b){this.i=b=void 0===b?!1:b;this.h=this.locale=null;this.g=new mh;_.P(this.g,2,a);b||(this.locale=document.documentElement.getAttribute("lang"));sh(this,new ih)},sh=function(a,b){_.mg(a.g,1,b);_.C(b,1)||_.P(b,1,1);a.i||(b=Bh(a),_.C(b,5)||_.P(b,5,a.locale));a.h&&(b=Bh(a),_.G(b,hh,9)||_.mg(b,9,a.h))},Ch=function(a,b){b(window).then(function(c){a.h=c;c=Bh(a);_.mg(c,9,a.h);return!0}).catch(function(){return!1})},Bh=function(a){a=_.G(a.g,ih,1);var b=_.G(a,fh,11);b||(b=new fh,_.mg(a,11,
b));return b},zh=function(a,b,c){c=void 0===c?0:c;a=Xf(a.g);a=_.P(a,4,Date.now().toString());var d=void 0===d?!1:d;_.Ge(a);if(b){var e=_.Ia([]);for(var f=0;f<b.length;f++)e[f]=b[f].H;a.G||(a.G={});a.G[3]=b}else a.G&&(a.G[3]=void 0),e=_.Hb;b=_.P(a,3,e,d);c&&_.P(b,14,c);return b};
var Dh=function(a){_.E.call(this,a)};_.A(Dh,_.E);
var Eh=function(a){_.E.call(this,a)};_.A(Eh,_.E);
_.Fh=function(a){_.E.call(this,a)};_.A(_.Fh,_.E);
var Gh=function(a){_.E.call(this,a)};_.A(Gh,_.E);
var Hh=function(a){_.E.call(this,a)};_.A(Hh,_.E);_.Ih=function(a,b){return _.P(a,2,b)};
var Jh,Lh;Jh=function(){this.h=null;this.j=performance.now();this.g=null;this.i=!1};_.Kh=function(){_.T.i=_.K("enable_clearcut_logs")};
Jh.prototype.log=function(a){_.P(a,14,performance.now()-this.j|0);if(this.g){_.P(a,1,this.g.session_id);_.P(a,6,this.g.client_origin);_.P(a,5,this.g.client_id);_.P(a,9,this.g.ui_mode);_.P(a,17,this.g.ux_mode);_.P(a,15,this.g.context);if(this.g.button_attributes){var b=this.g.button_attributes;if(b){var c=new Eh;var d=_.P(c,1,b.ib());d=_.P(d,2,b.Ka());d=_.P(d,3,b.Ia());d=_.P(d,4,b.La());d=_.P(d,5,b.Ja());d=_.P(d,6,b.hb());_.P(d,7,b.Hb())}else c=void 0;_.mg(a,18,c);c=new _.Fh;b=_.P(c,1,b.Gb());_.mg(a,
7,b)}2===this.g.ui_mode&&screen&&screen.height&&screen.width&&_.P(a,10,screen.width>screen.height)}b=new kh;b=_.P(b,11,_.C(a,2));a=Wf(a);a=_.P(b,8,a);this.i&&(b=Lh(this),a instanceof kh?b.log(a):(c=new kh,d=Wf(a),c=_.P(c,8,d),b.log(c)),_.x("logged event: "+Wf(a)))};Jh.prototype.flush=function(){Lh(this).flush()};
_.Nh=function(a,b,c,d,e){var f=new Gh;b=_.P(f,1,b);d=_.P(b,3,d);e=_.P(d,17,e);if(c&&c.type&&("click"===c.type||"touchend"===c.type)){a:{d=new Dh;try{_.P(d,1,c.g.offsetWidth),_.P(d,2,c.g.offsetHeight),_.P(d,3,c.i),_.P(d,4,c.j)}catch(g){c=void 0;break a}c=d}_.mg(e,2,c)}a=_.Mh(8,!1,a);return _.mg(a,12,e)};_.Mh=function(a,b,c){a=_.Ih(new Hh,a);b=_.P(a,3,b);return _.P(b,13,c)};
Lh=function(a){if(a.h)return a.h;var b=new Tf;b.g=!0;b.s=!0;var c=new uh(1112,b.v?b.v:dh,"0",b.i,"https://play.google.com/log?format=json&hasfast=true",!1,!1,b.s,void 0,void 0,b.j?b.j:void 0);b.o&&sh(c.m,b.o);if(b.l){var d=b.l,e=Bh(c.m);_.P(e,7,d)}b.h&&(c.s=b.h);b.m&&((d=b.m)?(c.i||(c.i=new Mg),d=Wf(d),_.P(c.i,4,d)):c.i&&_.P(c.i,4,void 0,!1,!1));if(b.C){d=b.C;c.i||(c.i=new Mg);var f=void 0===f?!1:f;_.P(c.i,2,null==d?_.Ia([]):Array.isArray(d)?_.Ia(d):d,f)}b.g&&(c.fa=b.g&&c.va);b.A&&(f=b.A,c.ga=!0,
wh(c,f));b.F&&Ch(c.m,b.F);a.h=c;_.x("Clearcut transport initialized with (1112, 0)");return a.h};_.T=new Jh;

}catch(e){_._DumpException(e)}
try{
var ii,ki,mi,li,pi;ii=function(a,b){a.X=function(c){b.o(c)}};_.ji=_.Pf(function(a,b,c){if(0!==a.h)return!1;_.P(b,c,_.eg(a.g));return!0},function(a,b,c){a.i(c,_.C(b,c))});ki=function(a,b,c){this.j=a;this.l=b;this.i=void 0===c?"picker_iframe":c;this.m=!1;this.h=this.g=null};
mi=function(a){var b=new _.Od;if(a.m)return b.reject("Setup process has already started before."),b.g;a.m=!0;a.h=b;_.F(window,"message",function(d){a:if(d=d.R,!a.g&&a.h&&d.origin===a.j&&d.data&&"channelConnect"==d.data.type){var e=a.h;a.h=null;if("button_iframe"!==a.i){var f=d.data.nonce;if(!f){e.reject("Invalid channel setup message from client.");break a}if((f&&_.xe(f))!==a.l){e.reject("Nonce mismatch when setting up channel.");break a}}a.g=d.ports[0];_.of(window,"message");e.resolve()}},!1);var c=
{type:"readyForConnect"};"button_iframe"===a.i?c.iframeId=a.l:c.channelId=a.l;li(a,c);return b.g};li=function(a,b){"picker_popup"===a.i?window.opener.postMessage(b,a.j):window.parent.postMessage(b,a.j)};_.ni=function(a,b){var c=new _.Od;a.g?(a.g.postMessage(b),c.resolve()):c.reject("Illegal state: try to send message before message channel set up.");return c.g};
_.oi=function(a,b,c,d,e,f){b={type:"command",command:"resize",height:b};c&&d&&(b.width=c,b.iframeId=d,b.verticalMargin=e,b.horizontalMargin=f);return _.ni(a,b)};ki.prototype.o=function(a){_.ni(this,{type:"activity",activity:a})};pi=function(a,b){this.h=a;this.g=b};
_.qi=function(a,b,c,d,e){e=void 0===e?"picker_iframe":e;_.Xg.call(this,a);this.J="picker_redirect"===e||"picker_relay"===e;this.F=e;this.O=this.J?void 0:new ki(c,d,e);this.va=new pi(b,c);this.B="button_iframe"===e?d:null;this.fa=this.K=!1};_.A(_.qi,_.Xg);_.qi.prototype.i=function(){var a=this;return this.J||this.K?Promise.resolve():mi(this.O).then(function(){ii(a,a.O);a.K=!0},function(b){_.z("Failed to initialize component. "+b)})};_.U=function(a){if(a.J||!a.K)throw Error("ha");return a.O};
_.qi.prototype.l=function(a,b){if(this.J||"picker_popup"===this.F)return Promise.resolve();void 0===a&&(a=this.g?this.g.za():this.h.offsetHeight);if("picker_iframe"===this.F)return _.oi(_.U(this),a);if(!this.B)return _.z("Trying to resize iframe in the wrong context."),Promise.reject();void 0===b&&(b=this.g?this.g.jb():this.h.offsetWidth);return _.oi(_.U(this),a,b,this.B)};
_.qi.prototype.j=function(a){var b=this;return _.Xg.prototype.j.call(this,a).then(function(){if("picker_iframe"===b.F||"button_iframe"===b.F)return!b.fa&&document.fonts&&document.fonts.ready&&document.fonts.ready.then(function(){b.fa=!0;b.l()}),b.l()})};

}catch(e){_._DumpException(e)}
try{
var ri=function(a){switch(a){case 1:return"not-supported";case 2:return"not-initialized";case 3:return"initialization timed out";case 4:return"stopped";case 5:return"not-visible";case 6:return"not-activated";case 7:return"safely-visible";default:return"unknown-status"}},si=function(){this.h=null;this.j=void 0;this.s=!1;this.o=0;this.g=2;this.m=!1},ui;si.prototype.reset=function(){_.x("Reset.","VISIBILITY_OBSERVER");var a=performance.now();this.j=void 0;this.o=a};si.prototype.Va=function(){return this.g};
_.ti=function(a){a.h&&3!==a.g&&1!==a.g&&2!==a.g&&4!==a.g&&(a.g=a.s?700>performance.now()-a.o?6:7:5);return a.g};ui=function(a){a.l&&(a.l.resolve(a.i),a.l=void 0)};
_.vi=function(a){var b=new si;window.IntersectionObserver&&77<=_.Af("Chromium")?(b.h=new IntersectionObserver(function(c){a:{c=c[c.length-1];if(!b.m){b.m=!0;b.i=void 0!==c.isVisible;if(b.i)b.g=5,ui(b);else{b.h&&(_.x("Stopped.","VISIBILITY_OBSERVER"),b.h.disconnect(),b.h=null,b.g=4);b.g=1;ui(b);_.x("Not initialized. IOv2 not supported","VISIBILITY_OBSERVER");break a}_.x("Initialized","VISIBILITY_OBSERVER")}var d=c.isVisible&&c.isIntersecting;!b.s&&d&&(b.o=c.time);b.s=d;_.x("IOv2 event received. Visibility State: "+
d+". Status: "+ri(_.ti(b)),"VISIBILITY_OBSERVER")}},{threshold:1,delay:100,trackVisibility:!0}),b.h.observe(a)):(b.m=!0,b.i=!1,b.g=1,ui(b),_.x("Not intialized. Not supported.","VISIBILITY_OBSERVER"));return b};

}catch(e){_._DumpException(e)}
try{
/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
_.wi=function(){return(_.w("iPad")||_.w("iPhone"))&&!_.va()&&!_.ua()&&!_.w("Coast")&&!_.ta()&&_.w("AppleWebKit")};_.zi=function(){return![_.ua()&&!_.xi()&&!_.yi(),_.ua()&&_.w("Android"),_.w("Edge")].some(function(a){return a})};
_.xi=function(){return!_.yi()&&(_.w("iPod")||_.w("iPhone")||_.w("Android")||_.w("IEMobile"))};_.yi=function(){return _.w("iPad")||_.w("Android")&&!_.w("Mobile")||_.w("Silk")};
var Ai;Ai={};_.Bi=(Ai.enable_fedcm=["28250620661-550h2e8djhee3ri2nma0u294i6ks921r.apps.googleusercontent.com","28250620661-jplop9r4d3uj679blu2nechmlm3h89gk.apps.googleusercontent.com","721418733929-55iv503445sqh9rospct8lthb3n46f3k.apps.googleusercontent.com","817667923408-mm67cha4vukqtq6aj0faaibfofl1memo.apps.googleusercontent.com","694505692171-31closf3bcmlt59aeulg2j81ej68j6hk.apps.googleusercontent.com"],Ai);

}catch(e){_._DumpException(e)}
try{
var Ii,Ni,Oi,Pi,Ui,$i;Ii=function(a){var b=a.Fc;return(0,_.L)("<span"+((void 0===b?0:b)?' role="alert"':"")+">"+_.M(a.message)+"</span>")};_.Ji=function(a){var b=_.Q("screen-reader-live");b?(_.rd(b,Ii,{message:a,Fc:!0}),_.Vg().then(function(){b.setAttribute("hidden",!0);return _.Vg()}).then(function(){_.mf(b);b.removeAttribute("hidden")})):Promise.resolve()};_.Ki=function(){_.T.j=performance.now()};
_.Li=function(a,b,c){var d=_.T;a=_.Mh(2,void 0===b?!1:b,a);if(c){b=new _.Fh;for(var e=0,f=0;f<c.length;f++)_.I(c[f],7)&&e++;_.P(b,1,c.length);_.P(b,2,e);_.mg(a,7,b)}d.log(a)};_.Mi=function(a,b,c,d,e){var f=_.T;2!==b&&f.log(_.Nh(a,b,c,d,e))};Oi=function(){this.g=_.v.document||document};Oi.prototype.appendChild=function(a,b){a.appendChild(b)};Oi.prototype.h=_.nf;Pi=/^(?:(?:https?|mailto|ftp):|[^&:\/?#]*(?:[\/?#]|$))/i;
_.Qi=function(a){_.Wd(a,_.Td)||_.Wd(a,_.Ud)?a=_.fe(a):a instanceof _.Ub?a=_.fe(_.Gd(a)):a instanceof _.Qd?a=_.fe(_.jd(a)):a instanceof _.Ed?a=_.fe(_.Fd(a).toString()):(a=String(a),a=Pi.test(a)?a.replace(_.he,_.ie):"about:invalid#zSoyz");return a};
_.Ri=function(a){return(0,_.L)((a?'<svg class="'+_.O("Bz112c")+" "+_.O("Bz112c-E3DyYd")+" "+_.O("Bz112c-uaxL4e")+'" xmlns="https://www.w3.org/2000/svg" viewBox="0 0 48 48">':'<svg class="'+_.O("Bz112c")+" "+_.O("Bz112c-E3DyYd")+'" xmlns="https://www.w3.org/2000/svg" viewBox="0 0 48 48">')+'<path fill="#4285F4" d="M45.12 24.5c0-1.56-.14-3.06-.4-4.5H24v8.51h11.84c-.51 2.75-2.06 5.08-4.39 6.64v5.52h7.11c4.16-3.83 6.56-9.47 6.56-16.17z"/><path fill="#34A853" d="M24 46c5.94 0 10.92-1.97 14.56-5.33l-7.11-5.52c-1.97 1.32-4.49 2.1-7.45 2.1-5.73 0-10.58-3.87-12.31-9.07H4.34v5.7C7.96 41.07 15.4 46 24 46z"/><path fill="#FBBC05" d="M11.69 28.18C11.25 26.86 11 25.45 11 24s.25-2.86.69-4.18v-5.7H4.34C2.85 17.09 2 20.45 2 24c0 3.55.85 6.91 2.34 9.88l7.35-5.7z"/><path fill="#EA4335" d="M24 10.75c3.23 0 6.13 1.11 8.41 3.29l6.31-6.31C34.91 4.18 29.93 2 24 2 15.4 2 7.96 6.93 4.34 14.12l7.35 5.7c1.73-5.2 6.58-9.07 12.31-9.07z"/><path fill="none" d="M2 2h44v44H2z"/></svg>')};
_.Si=function(){return(0,_.L)("An error has occurred")};
_.Ti=function(a){var b="";switch(a){case 1:a=b;b=(0,_.L)("The Google account you selected has signed out. Please sign in to Google and try again.");b=a+b;break;case 5:a=b;b='The current origin isn\'t registered with the Google OAuth client. <a href="https://developers.google.com/identity/one-tap/web/guides/get-google-api-clientid" target="_blank" class="'+(_.O("hSRGPd")+'">Learn more</a>');b=(0,_.L)(b);b=a+b;break;case 14:a=b;b=(0,_.L)("Only accounts from the organization can access this site. Sign in with a different account or ask your administrator to update the Google OAuth client settings.");b=
a+b;break;case 15:a=b;b=(0,_.L)("Access to your account data is restricted by policies within your organization. Please contact the administrator of your organization for more information.");b=a+b;break;default:a=b,b=(0,_.L)("That's all we know."),b=a+b}return(0,_.L)(b)};Ui=null;_.Vi=function(a){var b=_.C(a,3);if(b&&(b=b.trim(),0!==b.length))return b;a=_.Yc(_.C(a,2)).g;0==a.lastIndexOf("www.",0)&&(a=a.substr(4));return a};_.Wi=function(a){switch(a){case "signup":return 2;case "use":return 3;default:return 1}};
_.Xi=function(a){if(null==Ui){var b=Ni||(Ni=new Oi);var c=b.g,d=_.He(b.g,"div");d.style.backgroundColor="rgb(1, 2, 3)";b.appendChild(c.body,d);a:{c=9==d.nodeType?d:d.ownerDocument||d.document;if(c.defaultView&&c.defaultView.getComputedStyle&&(c=c.defaultView.getComputedStyle(d,null))){c=c.backgroundColor||c.getPropertyValue("backgroundColor")||"";break a}c=""}c=c.replace(/ /g,"");c="rgb(0,0,0)"===c?"black":"rgb(255,255,255)"===c?"white":"none";b.h(d);Ui=c}b=Ui;_.x("High Contrast Mode: "+b+". Updating classes for "+
a);switch(b){case "black":a.classList.add("CNusmb-haDnnc");a.classList.add("CNusmb-haDnnc-JaPV2b");a.classList.remove("CNusmb-haDnnc-HLvlvd");break;case "white":a.classList.add("CNusmb-haDnnc");a.classList.add("CNusmb-haDnnc-HLvlvd");a.classList.remove("CNusmb-haDnnc-JaPV2b");break;default:a.classList.remove("CNusmb-haDnnc"),a.classList.remove("CNusmb-haDnnc-JaPV2b"),a.classList.remove("CNusmb-haDnnc-HLvlvd")}};_.Yi=function(a,b){this.s=this.A=null;this.C=a;this.F=b;this.g=this.l=this.j=!1};
_.Yi.prototype.start=function(){return this.j?Promise.reject("Animation has already started."):this.g?(this.A=Date.now(),this.s=this.A+this.F,this.i(),this.j=!0,this.C?_.Zi(this,!1):Promise.resolve()):Promise.reject("Animation is not yet ready.")};_.Zi=function(a,b){if(a.j){if(a.l)return Promise.resolve();if(b)return a.o(),a.l=!0,a.h&&(a.h.resolve(),clearTimeout(a.v)),Promise.resolve();a.h||(a.h=new _.Od,$i(a));return a.h.g}return Promise.reject("Animation was not started.")};
$i=function(a){var b=a.s-Date.now();0<b?a.v=setTimeout(function(){return $i(a)},Math.min(b,100)):(a.o(),a.l=!0,a.h.resolve())};_.Yi.prototype.i=function(){};_.Yi.prototype.o=function(){};
var aj=function(a,b,c){_.Yi.call(this,void 0===b?!1:b,void 0===c?950:c);this.m=a;this.g=!0};_.A(aj,_.Yi);aj.prototype.i=function(){this.m.classList.add("iib5kc")};
/*

 Copyright 2015 Google Inc. All Rights Reserved.

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
*/
_.V={$b:function(){},cb:function(){},ac:function(){},Bb:function(){},Wb:function(){},register:function(){},Ob:function(){}};
_.V=function(){function a(n,p){for(var t=0;t<l.length;t++)if(l[t].className===n)return"undefined"!==typeof p&&(l[t]=p),l[t];return!1}function b(n){n=n.getAttribute("data-upgraded");return null===n?[""]:n.split(",")}function c(n,p){return-1!==b(n).indexOf(p)}function d(n,p,t){if("CustomEvent"in window&&"function"===typeof window.CustomEvent)return new CustomEvent(n,{bubbles:p,cancelable:t});var r=document.createEvent("Events");r.initEvent(n,p,t);return r}function e(n,p){if("undefined"===typeof n&&
"undefined"===typeof p)for(n=0;n<l.length;n++)e(l[n].className,l[n].aa);else{if("undefined"===typeof p){var t=a(n);t&&(p=t.aa)}p=document.querySelectorAll("."+p);for(t=0;t<p.length;t++)f(p[t],n)}}function f(n,p){if(!("object"===typeof n&&n instanceof Element))throw Error("ja");var t=d("mdl-componentupgrading",!0,!0);n.dispatchEvent(t);if(!t.defaultPrevented){t=b(n);var r=[];if(p)c(n,p)||r.push(a(p));else{var D=n.classList;l.forEach(function($d){D.contains($d.aa)&&-1===r.indexOf($d)&&!c(n,$d.className)&&
r.push($d)})}p=0;for(var H=r.length,N;p<H;p++){if(N=r[p]){t.push(N.className);n.setAttribute("data-upgraded",t.join(","));var Ab=new N.xc(n);Ab.mdlComponentConfigInternal_=N;m.push(Ab);for(var Aa=0,Zb=N.nb.length;Aa<Zb;Aa++)N.nb[Aa](n);N.Ga&&(n[N.className]=Ab)}else throw Error("ka");N=d("mdl-componentupgraded",!0,!1);n.dispatchEvent(N)}}}function g(n){Array.isArray(n)||(n=n instanceof Element?[n]:Array.prototype.slice.call(n));for(var p=0,t=n.length,r;p<t;p++)r=n[p],r instanceof HTMLElement&&(f(r),
0<r.children.length&&g(r.children))}function h(n){if(n){m.splice(m.indexOf(n),1);var p=n.L.getAttribute("data-upgraded").split(",");p.splice(p.indexOf(n.mdlComponentConfigInternal_.Sa),1);n.L.setAttribute("data-upgraded",p.join(","));p=d("mdl-componentdowngraded",!0,!1);n.L.dispatchEvent(p)}}var l=[],m=[];return{$b:e,cb:f,ac:g,Bb:function(){for(var n=0;n<l.length;n++)e(l[n].className)},Wb:function(n,p){(n=a(n))&&n.nb.push(p)},register:function(n){var p=!0;if("undefined"!==typeof n.Ga||"undefined"!==
typeof n.widget)p=n.Ga||n.widget;var t={xc:n.constructor||n.constructor,className:n.Sa||n.classAsString,aa:n.aa||n.cssClass,Ga:p,nb:[]};l.forEach(function(r){if(r.aa===t.aa)throw Error("la`"+r.aa);if(r.className===t.className)throw Error("ma");});if(n.constructor.prototype.hasOwnProperty("mdlComponentConfigInternal_"))throw Error("na");a(n.Sa,t)||l.push(t)},Ob:function(n){var p=function(r){m.filter(function(D){return D.L===r}).forEach(h)};if(n instanceof Array||n instanceof NodeList)for(var t=0;t<
n.length;t++)p(n[t]);else if(n instanceof Node)p(n);else throw Error("oa");}}}();_.V.upgradeDom=_.V.$b;_.V.upgradeElement=_.V.cb;_.V.upgradeElements=_.V.ac;_.V.upgradeAllRegistered=_.V.Bb;_.V.registerUpgradedCallback=_.V.Wb;_.V.register=_.V.register;_.V.downgradeElements=_.V.Ob;window.componentHandler=_.V;
window.addEventListener("load",function(){"classList"in document.createElement("div")&&"querySelector"in document&&"addEventListener"in window&&Array.prototype.forEach&&(document.documentElement.classList.add("mdl-js"),_.V.Bb())});
(function(){var a=function(b){this.L=b;this.j()};window.MaterialSpinner=a;a.prototype.i={kc:4};a.prototype.g={Jb:"mdl-spinner__layer",Ib:"mdl-spinner__circle-clipper",ic:"mdl-spinner__circle",jc:"mdl-spinner__gap-patch",lc:"mdl-spinner__left",mc:"mdl-spinner__right"};a.prototype.h=function(b){var c=document.createElement("div");c.classList.add(this.g.Jb);c.classList.add(this.g.Jb+"-"+b);b=document.createElement("div");b.classList.add(this.g.Ib);b.classList.add(this.g.lc);var d=document.createElement("div");
d.classList.add(this.g.jc);var e=document.createElement("div");e.classList.add(this.g.Ib);e.classList.add(this.g.mc);for(var f=[b,d,e],g=0;g<f.length;g++){var h=document.createElement("div");h.classList.add(this.g.ic);f[g].appendChild(h)}c.appendChild(b);c.appendChild(d);c.appendChild(e);this.L.appendChild(c)};a.prototype.createLayer=a.prototype.h;a.prototype.l=function(){this.L.classList.remove("is-active")};a.prototype.stop=a.prototype.l;a.prototype.start=function(){this.L.classList.add("is-active")};
a.prototype.start=a.prototype.start;a.prototype.j=function(){if(this.L){for(var b=1;b<=this.i.kc;b++)this.h(b);this.L.classList.add("is-upgraded")}};_.V.register({constructor:a,Sa:"MaterialSpinner",aa:"mdl-js-spinner",Ga:!0})})();
(function(){var a=function(b){this.L=b;this.l()};window.MaterialProgress=a;a.prototype.j={fc:"mdl-progress__indeterminate"};a.prototype.o=function(b){this.L.classList.contains(this.j.fc)||(this.i.style.width=b+"%")};a.prototype.setProgress=a.prototype.o;a.prototype.m=function(b){this.h.style.width=b+"%";this.g.style.width=100-b+"%"};a.prototype.setBuffer=a.prototype.m;a.prototype.l=function(){if(this.L){var b=document.createElement("div");b.className="progressbar bar bar1";this.L.appendChild(b);this.i=
b;b=document.createElement("div");b.className="bufferbar bar bar2";this.L.appendChild(b);this.h=b;b=document.createElement("div");b.className="auxbar bar bar3";this.L.appendChild(b);this.g=b;this.i.style.width="0%";this.h.style.width="100%";this.g.style.width="0%";this.L.classList.add("is-upgraded")}};_.V.register({constructor:a,Sa:"MaterialProgress",aa:"mdl-js-progress",Ga:!0})})();
var bj=function(a,b,c){_.Yi.call(this,void 0===b?!1:b,void 0===c?1E3:c);this.m=a;_.V.cb(this.m);this.g=!0};_.A(bj,_.Yi);bj.prototype.i=function(){this.m.classList.add("is-active")};bj.prototype.o=function(){this.m.classList.remove("is-active")};
_.cj=function(a,b){_.Tg.call(this,a,b);this.credentials=b.sessionDataList;this.clientData=b.clientData;this.m=!1;this.F=this.i=this.o=null};_.A(_.cj,_.Tg);_.cj.prototype.X=function(){if(this.i){var a=_.T,b=_.Mh(13,!1,this.g);a.log(b);this.F=new bj(_.jf("aZ2wEe",this.i),!1,3===this.g&&_.K("cancelable_auto_select")?0:void 0);this.F.start();this.i.blur();this.i.classList.add("gk6SMd");_.dj().classList.add("FnSee");_.Ji(("Signing in as "+_.C(this.o,1)).toString())}};
_.cj.prototype.C=function(a){var b=this;a=void 0===a?{}:a;var c=void 0===a.W?!1:a.W,d=void 0===a.Z?!1:a.Z;return this.F?_.Zi(this.F,c).then(function(){var e=new aj(_.jf("MPu53c",b.i),!0);_.Ji(("Signed in as "+_.C(b.o,1)).toString());return d?Promise.resolve():e.start()}):Promise.resolve()};_.dj=function(){return _.Q("animated-container")};_.cj.prototype.l=function(){return _.hf("TAKBxb",_.dj())};

}catch(e){_._DumpException(e)}
try{
var ej=[3],fj=function(a){for(var b=ej,c=0,d=0;d<b.length;d++){var e=b[d];null!=_.C(a,e)&&(0!==c&&_.P(a,c,void 0,!1,!0),c=e)}return c},gj=function(a){_.E.call(this,a)},ij,jj,kj,lj,mj,oj,pj,Cj,Dj,Ej,Fj,Gj,Hj,Lj,Nj,Jj,Oj,Kj,Pj,Qj,Rj,Sj,Vj;_.A(gj,_.E);_.hj=function(a,b,c){return _.ni(a,{type:"response",response:b,announcement:c})};
ij=function(a){var b=null;a&&(b=_.Hd(a));a=new _.Kc("/gsi/issue");_.Nc(a,b);var c=new _.Od;_.pf(a.toString(),function(d){c.resolve(_.Ze(d.target))},"POST",null,{"X-Requested-With":"XmlHttpRequest"},void 0,!0);return c.g};
jj=function(a,b,c,d,e,f,g,h){a={user_id:b,client_id:a.h,origin:a.g,select_by:c,consent_acquired:d,token:e};f&&(a.nonce=f);g&&(a.as=g);h&&(a.ui_mode=h);var l=new _.Od;ij(a).then(function(m){m&&"null"!==m?(m=new gj(_.hd(m)),l.resolve(m)):(_.z("Issue credential returns invalid response."),l.resolve(null))},function(m){l.reject(m)});return l.g};kj=function(a,b){a.i&&(a.m=!0,(void 0===b?0:b)||a.X())};
lj=function(a,b){var c=void 0===b?{}:b;b=void 0===c.W?!1:c.W;c=void 0===c.Z?!1:c.Z;return a.m?a.C({W:b,Z:c}).then(function(){a.m=!1}):Promise.resolve()};mj=_.Pf(function(a,b,c,d,e){if(2!==a.h)return!1;var f=void 0===f?!1:f;_.Ge(b);f=_.uf(b,d,c,f);d=new d;b=_.Vf(b,c);f.push(d);b.push(d.H);_.jg(a,d,e);return!0},function(a,b,c,d,e){a.l(c,_.uf(b,d,c),e)});_.nj=function(a){var b=[];a.forEach(function(c){b.push(_.oe(c))});return b};
oj={Tc:{1E3:{other:"0K"},1E4:{other:"00K"},1E5:{other:"000K"},1E6:{other:"0M"},1E7:{other:"00M"},1E8:{other:"000M"},1E9:{other:"0B"},1E10:{other:"00B"},1E11:{other:"000B"},1E12:{other:"0T"},1E13:{other:"00T"},1E14:{other:"000T"}},Sc:{1E3:{other:"0 thousand"},1E4:{other:"00 thousand"},1E5:{other:"000 thousand"},1E6:{other:"0 million"},1E7:{other:"00 million"},1E8:{other:"000 million"},1E9:{other:"0 billion"},1E10:{other:"00 billion"},1E11:{other:"000 billion"},1E12:{other:"0 trillion"},1E13:{other:"00 trillion"},
1E14:{other:"000 trillion"}}};pj=oj;pj=oj;
var qj={AED:[2,"dh","\u062f.\u0625."],ALL:[0,"Lek","Lek"],AUD:[2,"$","AU$"],BDT:[2,"\u09f3","Tk"],BGN:[2,"lev","lev"],BRL:[2,"R$","R$"],CAD:[2,"$","C$"],CDF:[2,"FrCD","CDF"],CHF:[2,"CHF","CHF"],CLP:[0,"$","CL$"],CNY:[2,"\u00a5","RMB\u00a5"],COP:[32,"$","COL$"],CRC:[0,"\u20a1","CR\u20a1"],CZK:[50,"K\u010d","K\u010d"],DKK:[50,"kr.","kr."],DOP:[2,"RD$","RD$"],EGP:[2,"\u00a3","LE"],ETB:[2,"Birr","Birr"],EUR:[2,"\u20ac","\u20ac"],GBP:[2,"\u00a3","GB\u00a3"],HKD:[2,"$","HK$"],HRK:[2,"kn","kn"],HUF:[34,
"Ft","Ft"],IDR:[0,"Rp","Rp"],ILS:[34,"\u20aa","IL\u20aa"],INR:[2,"\u20b9","Rs"],IRR:[0,"Rial","IRR"],ISK:[0,"kr","kr"],JMD:[2,"$","JA$"],JPY:[0,"\u00a5","JP\u00a5"],KRW:[0,"\u20a9","KR\u20a9"],LKR:[2,"Rs","SLRs"],LTL:[2,"Lt","Lt"],MNT:[0,"\u20ae","MN\u20ae"],MVR:[2,"Rf","MVR"],MXN:[2,"$","Mex$"],MYR:[2,"RM","RM"],NOK:[50,"kr","NOkr"],PAB:[2,"B/.","B/."],PEN:[2,"S/.","S/."],PHP:[2,"\u20b1","PHP"],PKR:[0,"Rs","PKRs."],PLN:[50,"z\u0142","z\u0142"],RON:[2,"RON","RON"],RSD:[0,"din","RSD"],RUB:[50,"\u20bd",
"RUB"],SAR:[2,"SAR","SAR"],SEK:[50,"kr","kr"],SGD:[2,"$","S$"],THB:[2,"\u0e3f","THB"],TRY:[2,"\u20ba","TRY"],TWD:[2,"$","NT$"],TZS:[0,"TSh","TSh"],UAH:[2,"\u0433\u0440\u043d.","UAH"],USD:[2,"$","US$"],UYU:[2,"$","$U"],VND:[48,"\u20ab","VN\u20ab"],YER:[0,"Rial","Rial"],ZAR:[2,"R","ZAR"]},rj={dc:".",Fb:",",oc:"%",Lb:"0",qc:"+",Kb:"-",ec:"E",pc:"\u2030",hc:"\u221e",nc:"NaN",cc:"#,##0.###",Wc:"#E0",Vc:"#,##0%",Uc:"\u00a4#,##0.00",Eb:"USD"},W=rj;W=rj;
var sj=function(a,b){if(!a||!isFinite(a)||0==b)return a;a=String(a).split("e");return parseFloat(a[0]+"e"+(parseInt(a[1]||0,10)+b))},tj={Cc:0,Sb:"",Tb:"",prefix:"",Yb:""},vj=function(){this.o=40;this.g=1;this.h=3;this.s=this.i=0;this.D=!1;this.B=this.F="";this.A=W.Kb;this.v="";this.j=1;this.m=!1;this.l=[];this.C=this.J=!1;var a=W.cc,b=[0];this.F=uj(this,a,b);for(var c=b[0],d=-1,e=0,f=0,g=0,h=-1,l=a.length,m=!0;b[0]<l&&m;b[0]++)switch(a.charAt(b[0])){case "#":0<f?g++:e++;0<=h&&0>d&&h++;break;case "0":if(0<
g)throw Error("ca`"+a);f++;0<=h&&0>d&&h++;break;case ",":0<h&&this.l.push(h);h=0;break;case ".":if(0<=d)throw Error("da`"+a);d=e+f+g;break;case "E":if(this.C)throw Error("ea`"+a);this.C=!0;this.s=0;b[0]+1<l&&"+"==a.charAt(b[0]+1)&&(b[0]++,this.D=!0);for(;b[0]+1<l&&"0"==a.charAt(b[0]+1);)b[0]++,this.s++;if(1>e+f||1>this.s)throw Error("fa`"+a);m=!1;break;default:b[0]--,m=!1}0==f&&0<e&&0<=d&&(f=d,0==f&&f++,g=e-f,e=f-1,f=1);if(0>d&&0<g||0<=d&&(d<e||d>e+f)||0==h)throw Error("ga`"+a);g=e+f+g;this.h=0<=
d?g-d:0;0<=d&&(this.i=e+f-d,0>this.i&&(this.i=0));this.g=(0<=d?d:g)-e;this.C&&(this.o=e+this.g,0==this.h&&0==this.g&&(this.g=1));this.l.push(Math.max(0,h));this.J=0==d||d==g;c=b[0]-c;this.B=uj(this,a,b);b[0]<a.length&&";"==a.charAt(b[0])?(b[0]++,1!=this.j&&(this.m=!0),this.A=uj(this,a,b),b[0]+=c,this.v=uj(this,a,b)):(this.A+=this.F,this.v+=this.B)},wj=function(a,b,c,d){if(a.i>a.h)throw Error("$");d||(d=[]);var e=sj(b,a.h);e=Math.round(e);isFinite(e)?(b=Math.floor(sj(e,-a.h)),e=Math.floor(e-sj(b,a.h))):
e=0;var f=b,g=e,h=0<a.i||0<g||!1;e=a.i;h&&(e=a.i);var l="";for(b=f;1E20<b;)l="0"+l,b=Math.round(sj(b,-1));l=b+l;var m=W.dc;b=W.Lb.charCodeAt(0);var n=l.length,p=0;if(0<f||0<c){for(f=n;f<c;f++)d.push(String.fromCharCode(b));if(2<=a.l.length)for(c=1;c<a.l.length;c++)p+=a.l[c];c=n-p;if(0<c){f=a.l;p=n=0;for(var t,r=W.Fb,D=l.length,H=0;H<D;H++)if(d.push(String.fromCharCode(b+Number(l.charAt(H)))),1<D-H)if(t=f[p],H<c){var N=c-H;(1===t||0<t&&1===N%t)&&d.push(r)}else p<f.length&&(H===c?p+=1:t===H-c-n+1&&
(d.push(r),n+=t,p+=1))}else{c=l;l=a.l;f=W.Fb;t=c.length;r=[];for(n=l.length-1;0<=n&&0<t;n--){p=l[n];for(D=0;D<p&&0<=t-D-1;D++)r.push(String.fromCharCode(b+Number(c.charAt(t-D-1))));t-=p;0<t&&r.push(f)}d.push.apply(d,r.reverse())}}else h||d.push(String.fromCharCode(b));(a.J||h)&&d.push(m);h=String(g);g=h.split("e+");if(2==g.length){if(h=parseFloat(g[0])){m=h;if(isFinite(m)){for(c=0;1<=(m/=10);)c++;m=c}else m=0<m?m:0;m=-m-1;h=-1>m?h&&isFinite(h)?sj(Math.round(sj(h,-1)),1):h:h&&isFinite(h)?sj(Math.round(sj(h,
m)),-m):h}h=String(h);h=h.replace(".","");h+=(0,_.bc)("0",parseInt(g[1],10)-h.length+1)}a.h+1>h.length&&(h="1"+(0,_.bc)("0",a.h-h.length)+h);for(a=h.length;"0"==h.charAt(a-1)&&a>e+1;)a--;for(e=1;e<a;e++)d.push(String.fromCharCode(b+Number(h.charAt(e))))},xj=function(a,b,c){c.push(W.ec);0>b?(b=-b,c.push(W.Kb)):a.D&&c.push(W.qc);b=""+b;for(var d=W.Lb,e=b.length;e<a.s;e++)c.push(d);c.push(b)},uj=function(a,b,c){for(var d="",e=!1,f=b.length;c[0]<f;c[0]++){var g=b.charAt(c[0]);if("'"==g)c[0]+1<f&&"'"==
b.charAt(c[0]+1)?(c[0]++,d+="'"):e=!e;else if(e)d+=g;else switch(g){case "#":case "0":case ",":case ".":case ";":return d;case "\u00a4":c[0]+1<f&&"\u00a4"==b.charAt(c[0]+1)?(c[0]++,d+=W.Eb):(g=W.Eb,d+=g in qj?qj[g][1]:g);break;case "%":if(!a.m&&1!=a.j)throw Error("aa");if(a.m&&100!=a.j)throw Error("ba");a.j=100;a.m=!1;d+=W.oc;break;case "\u2030":if(!a.m&&1!=a.j)throw Error("aa");if(a.m&&1E3!=a.j)throw Error("ba");a.j=1E3;a.m=!1;d+=W.pc;break;default:d+=g}}return d},yj=function(a){return 1==a%10&&
11!=a%100?"one":2==a%10&&12!=a%100?"two":3==a%10&&13!=a%100?"few":"other"},zj=yj;zj=yj;var Aj=function(a,b){if(void 0===b){b=a+"";var c=b.indexOf(".");b=Math.min(-1===c?0:b.length-c-1,3)}return 1==(a|0)&&0==b?"one":"other"},Bj=Aj;Bj=Aj;Cj=null;Dj=null;Ej=null;Fj=/^\s*(\w+)\s*,\s*plural\s*,(?:\s*offset:(\d+))?/;Gj=/^\s*(\w+)\s*,\s*selectordinal\s*,/;Hj=/^\s*(\w+)\s*,\s*select\s*,/;_.Ij=function(a){this.i=a;this.h=this.g=this.l=null;a=W;var b=pj;if(Cj!==a||Dj!==b)Cj=a,Dj=b,Ej=new vj;this.m=Ej};
_.Mj=function(a,b){if(a.i){a.l=[];var c=Jj(a,a.i);a.h=Kj(a,c);a.i=null}if(a.h&&0!=a.h.length){a.g=_.Ca(a.l);c=[];Lj(a,a.h,b,!0,c);for(b=c.join("");0<a.g.length;)b=b.replace(a.j(a.g),a.g.pop());a=b}else a="";return a};
Lj=function(a,b,c,d,e){for(var f=0;f<b.length;f++)switch(b[f].type){case 4:e.push(b[f].value);break;case 3:var g=b[f].value,h=a,l=e,m=c[g];void 0===m?l.push("Undefined parameter - "+g):(h.g.push(m),l.push(h.j(h.g)));break;case 2:g=b[f].value;h=a;l=c;m=d;var n=e,p=g.Pa;void 0===l[p]?n.push("Undefined parameter - "+p):(p=g[l[p]],void 0===p&&(p=g.other),Lj(h,p,l,m,n));break;case 0:g=b[f].value;Nj(a,g,c,Bj,d,e);break;case 1:g=b[f].value,Nj(a,g,c,zj,d,e)}};
Nj=function(a,b,c,d,e,f){var g=b.Pa,h=b.Mb,l=+c[g];if(isNaN(l))f.push("Undefined or invalid parameter - "+g);else if(h=l-h,g=b[c[g]],void 0===g&&(d=d(Math.abs(h)),g=b[d],void 0===g&&(g=b.other)),b=[],Lj(a,g,c,e,b),c=b.join(""),e)f.push(c);else{a=a.m;e=h;if(a.i>a.h)throw Error("$");isNaN(e)?a=W.nc:(h=[],e=sj(e,-tj.Cc),(b=0>e||0==e&&0>1/e)?tj.Sb?h.push(tj.Sb):(h.push(tj.prefix),h.push(a.A)):(h.push(tj.prefix),h.push(a.F)),isFinite(e)?(e=e*(b?-1:1)*a.j,a.C?(d=e,0==d?(wj(a,d,a.g,h),xj(a,0,h)):(g=Math.floor(Math.log(d)/
Math.log(10)+2E-15),d=sj(d,-g),l=a.g,1<a.o&&a.o>a.g?(l=g%a.o,0>l&&(l=a.o+l),d=sj(d,l),g-=l,l=1):1>a.g?(g++,d=sj(d,-1)):(g-=a.g-1,d=sj(d,a.g-1)),wj(a,d,l,h),xj(a,g,h))):wj(a,e,a.g,h)):h.push(W.hc),b?tj.Tb?h.push(tj.Tb):(isFinite(e)&&h.push(tj.Yb),h.push(a.v)):(isFinite(e)&&h.push(tj.Yb),h.push(a.B)),a=h.join(""));f.push(c.replace(/#/g,a))}};Jj=function(a,b){var c=a.l,d=(0,_.Ae)(a.j,a);b=b.replace(_.Sg,function(){c.push("'");return d(c)});return b=b.replace(_.Rg,function(e,f){c.push(f);return d(c)})};
Oj=function(a){var b=0,c=[],d=[],e=/[{}]/g;e.lastIndex=0;for(var f;f=e.exec(a);){var g=f.index;"}"==f[0]?(c.pop(),0==c.length&&(f={type:1},f.value=a.substring(b,g),d.push(f),b=g+1)):(0==c.length&&(b=a.substring(b,g),""!=b&&d.push({type:0,value:b}),b=g+1),c.push("{"))}b=a.substring(b);""!=b&&d.push({type:0,value:b});return d};
Kj=function(a,b){var c=[];b=Oj(b);for(var d=0;d<b.length;d++){var e={};if(0==b[d].type)e.type=4,e.value=b[d].value;else if(1==b[d].type){var f=b[d].value;switch(Fj.test(f)?0:Gj.test(f)?1:Hj.test(f)?2:/^\s*\w+\s*/.test(f)?3:5){case 2:e.type=2;e.value=Pj(a,b[d].value);break;case 0:e.type=0;e.value=Qj(a,b[d].value);break;case 1:e.type=1;e.value=Rj(a,b[d].value);break;case 3:e.type=3,e.value=b[d].value}}c.push(e)}return c};
Pj=function(a,b){var c="";b=b.replace(Hj,function(h,l){c=l;return""});var d={};d.Pa=c;b=Oj(b);for(var e=0;e<b.length;){var f=b[e].value;e++;var g;1==b[e].type&&(g=Kj(a,b[e].value));d[f.replace(/\s/g,"")]=g;e++}return d};Qj=function(a,b){var c="",d=0;b=b.replace(Fj,function(l,m,n){c=m;n&&(d=parseInt(n,10));return""});var e={};e.Pa=c;e.Mb=d;b=Oj(b);for(var f=0;f<b.length;){var g=b[f].value;f++;var h;1==b[f].type&&(h=Kj(a,b[f].value));e[g.replace(/\s*(?:=)?(\w+)\s*/,"$1")]=h;f++}return e};
Rj=function(a,b){var c="";b=b.replace(Gj,function(h,l){c=l;return""});var d={};d.Pa=c;d.Mb=0;b=Oj(b);for(var e=0;e<b.length;){var f=b[e].value;e++;if(1==b[e].type)var g=Kj(a,b[e].value);d[f.replace(/\s*(?:=)?(\w+)\s*/,"$1")]=g;e++}return d};_.Ij.prototype.j=function(a){return"\ufddf_"+(a.length-1).toString(10)+"_"};
Sj=function(a){switch(a){case "auto":return 1;case "user_2tap":return 6;case "user_1tap":return 5;case "btn":return 7;case "btn_confirm":return 8;case "btn_add_session":return 9;case "btn_confirm_add_session":return 10;default:return 2}};
_.Tj=function(a,b,c,d){c=void 0===c?!1:c;d=void 0===d?!1:d;c='<div class="'+_.O("fFW7wc-ibnC6b-sM5MNb")+" "+_.O("TAKBxb")+" "+(c?_.O("OWB6Me"):"")+'" aria-labelledby="picker-item-label-'+_.O(b)+'"'+(c?' role="text"':' role="link" tabindex="0"')+'><div class="'+_.O("fFW7wc-ibnC6b")+'"><div class="'+_.O("aZ2wEe")+' mdl-spinner mdl-js-spinner mdl-spinner--single-color" aria-hidden=true></div><div class="'+_.O("MPu53c")+'" aria-hidden=true>';var e=(0,_.L)('<svg class="'+_.O("Bz112c")+" "+_.O("Bz112c-E3DyYd")+
'" fill="#FFFFFF" viewBox="0 0 24 24" xmlns="https://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"/><path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/></svg>');c=c+e+"</div>";a.la?(a.displayName?(c+='<img class="'+_.O("fFW7wc-ibnC6b-HiaYvf")+'" src="'+_.O(_.je(a.la))+'" alt="',e=_.O(a.displayName)+"'s profile image",c+=_.ee(e)):(c+='<img class="'+_.O("fFW7wc-ibnC6b-HiaYvf")+'" src="'+_.O(_.je(a.la))+'" alt="',e=_.O(a.id)+"'s profile image",c+=_.ee(e)),c+='" aria-hidden=true>'):
c+=_.ne();c+='<div class="'+_.O("fFW7wc-ibnC6b-r4m2rf")+'" aria-hidden=true>'+(a.displayName?'<div class="'+_.O("fFW7wc-ibnC6b-ssJRIf")+'">'+_.M(a.displayName)+'</div><div class="'+_.O("fFW7wc-ibnC6b-K4efff")+'">'+_.M(a.id)+"</div>":'<div class="'+_.O("fFW7wc-ibnC6b-ssJRIf")+'">'+_.M(a.id)+"</div>")+"</div>"+(d?'<button id="cancel-button" class="mdl-button mdl-js-button mdl-button--primary '+_.O("IbE0S-LgbsSe")+'">Cancel</button>':"")+'</div><div id="picker-item-label-'+_.O(b)+'" class="'+_.O("L6cTce")+
'">';a.la?(d=b=a.displayName,a="Google Account, "+_.M(null!=b?b:a.id)+"'s profile picture, "+_.M(a.id)+", "+_.M(null!=d?d:""),c+=a):(b=a.displayName,a="Google Account, "+_.M(a.id)+", "+_.M(null!=b?b:""),c+=a);return(0,_.L)(c+"</div></div>")};
_.Uj=function(a,b,c,d){b=void 0===b?!1:b;c=void 0===c?!1:c;d=void 0===d?!1:d;b='<div id="credentials-picker" class="'+_.O("fFW7wc")+'" tabindex="-1" aria-labelledby="'+(b?"list-collapsed-label":"list-extended-label")+'">';for(var e=a.length,f=0;f<e;f++)b+=_.Tj(a[f],f,void 0,d);d=b;c?(c='<div class="'+_.O("fFW7wc-ibnC6b-sM5MNb")+" "+_.O("wdeprb-ijUMG-mzNpsf")+'" id="use-other" tabindex="0" role="link"><div class="'+_.O("fFW7wc-ibnC6b-MJoBVe")+'"></div><div class="'+_.O("fFW7wc-ibnC6b")+'">'+(0,_.L)('<svg class="'+
_.O("fFW7wc-ibnC6b-HiaYvf")+" "+_.O("wdeprb-ijUMG-mzNpsf-Bz112c")+'" xmlns="https://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#757575" aria-hidden="true"><path d="M12,2C6.48,2,2,6.48,2,12c0,5.52,4.48,10,10,10c5.52,0,10-4.48,10-10C22,6.48,17.52,2, 12,2z M7.07,18.28 c0.43-0.9,3.05-1.78,4.93-1.78s4.51,0.88,4.93,1.78C15.57,19.36,13.86, 20,12,20S8.43,19.36,7.07,18.28z M18.36,16.83 c-1.43-1.74-4.9-2.33-6.36-2.33s-4.93, 0.59-6.36,2.33C4.62,15.49,4,13.82,4,12c0-4.41,3.59-8,8-8c4.41,0,8,3.59,8,8 C20, 13.82,19.38,15.49,18.36,16.83z"/><path d="M12,6c-1.94,0-3.5,1.56-3.5,3.5S10.06,13,12,13c1.94,0,3.5-1.56,3.5-3.5S13.94,6,12,6z M12,11c-0.83,0-1.5-0.67-1.5-1.5 C10.5,8.67,11.17,8,12,8c0.83,0,1.5,0.67,1.5,1.5C13.5, 10.33,12.83,11,12,11z"/><path fill="none" d="M0,0h24v24H0V0z"/></svg>')+
'<div class="'+_.O("fFW7wc-ibnC6b-r4m2rf")+'"><div class="'+_.O("fFW7wc-ibnC6b-ssJRIf")+'">',c=(0,_.L)(c+"Use another account</div></div></div></div>")):c="";b=d+(c+'</div><div id="list-collapsed-label" class="'+_.O("L6cTce")+'">');b=b+'Google account list with 2 items.</div><div id="list-extended-label" class="'+(_.O("L6cTce")+'">');a=_.Mj(new _.Ij("{NUM,plural,=1{Google account list with 1 item.}other{Google account list with {XXX} items.}}"),{NUM:a.length,XXX:_.M(a.length)});return(0,_.L)(b+a+
"</div>")};_.Wj=function(a){_.E.call(this,a,-1,Vj)};_.A(_.Wj,_.E);_.Xj=function(){return[1,_.qg,_.Jg,_.Kg,2,_.qg,_.$e,_.sg,3,_.qg,_.Og,_.Pg,4,mj,_.Jd,_.Ig,5,_.R,6,_.R,7,_.R,8,_.ji,9,_.R,10,_.R,11,_.R,12,_.R,13,_.R,14,_.R,15,_.og,16,_.og,17,_.rg,18,_.og,19,_.ji,20,_.og]};Vj=[4];
_.Yj=function(a,b,c){c=void 0===c?"picker_iframe":c;var d=_.G(b,_.Og,3);_.qi.call(this,a,_.C(d,1),_.C(d,2),_.C(b,5),c);this.D=b;this.clientData=d;this.credentials=_.uf(b,_.Jd,4);this.Na=_.C(b,10);this.context=_.C(b,9)||"signin";this.ga=0===_.C(b,8)?0:_.C(b,8)||-1;this.nonce=_.C(b,7)||void 0;this.A=_.G(b,_.Jg,1)&&_.C(_.G(b,_.Jg,1),1)||void 0;this.v=_.G(b,_.Jg,1)&&_.C(_.G(b,_.Jg,1),3)||void 0;a=_.Vi(this.clientData);_.P(this.clientData,3,a)};_.A(_.Yj,_.qi);_.Yj.prototype.i=function(){var a=this;return _.qi.prototype.i.call(this).then(function(){return a.ja()})};
_.Yj.prototype.C=function(){};
_.Yj.prototype.ea=function(a,b,c,d){var e=this;c=void 0===c?!1:c;var f=this.g;f&&kj(f,void 0===d?!1:d);return Zj(this,a,b,c).then(function(g){if(!g)return e.C(0),_.z("Failed to issue credential due to an unknown error"),_.Md(e,"issue_credential_failed",{cause:"Error encountered while issuing credential."}),Promise.resolve();if(null!=_.C(g,2)){var h=_.G(g,_.$e,2);e.C(_.C(h,1));_.te(h);_.Md(e,"issue_credential_failed",{cause:"Error encountered while issuing credential."});return Promise.resolve()}return(f?
lj(f,{W:"bottom_sheet"===_.C(e.D,6)&&(_.I(g,4)||!1)}):Promise.resolve()).then(function(){var l="Signed in as "+_.C(a,1),m=Sj(b),n=c,p=_.T;n=void 0===n?!1:n;var t=_.Mh(4,"auto"===b,f?f.g:void 0);m=_.P(t,8,m);n=_.P(m,4,n);p.log(n);_.T.flush();p={clientId:_.C(e.clientData,1),credential:_.C(_.G(g,_.Zf,3===fj(g)?3:-1),1),select_by:b};return e.kb(p,l)})},function(g){e.C();_.z(g)})};_.Yj.prototype.kb=function(a,b){return _.hj(_.U(this),a,b)};
var Zj=function(a,b,c,d){var e=a.va;return"auto"===c&&null!=_.C(b,8)?(c=new gj,a=_.G(a.D,_.Jg,1),_.mg(c,1,a),a=_.G(b,_.Zf,8),_.Ge(c),c.G||(c.G={}),b=a?a.H:a,c.G[3]=a,_.Ge(c),(a=fj(c))&&3!==a&&null!=b&&(c.G&&a in c.G&&(c.G[a]=void 0),_.P(c,a,void 0)),_.P(c,3,b),_.P(c,4,!0),Promise.resolve(c)):jj(e,_.C(b,2),c,d,a.Na,a.nonce,a.A,_.C(a.D,6)||void 0)};

}catch(e){_._DumpException(e)}
try{
_.Bk=function(a){var b={};if(a)for(var c=_.u(Object.keys(a)),d=c.next();!d.done;d=c.next())d=d.value,void 0!==a[d]&&""!==a[d]&&(b[d]=a[d]);return b};_.Ck=function(a,b){a=new _.Kc(a);b&&_.Nc(a,_.Hd(_.Bk(b)));return a.toString()};
_.Ek=function(a,b){var c=document.createElement("form");document.body.appendChild(c);c.method="post";a=a instanceof _.Ub?a:_.Dk(a);c.action=_.Gd(a);if(b){a=Object.keys(b);for(var d=0;d<a.length;d++){var e=a[d],f=document.createElement("input");f.type="hidden";f.name=e;f.value=b[e].toString();c.appendChild(f)}}c.submit()};_.Fk=function(a){return a instanceof _.Qd?_.jd(a):_.Gd(a)};
_.Gk=function(a){var b=void 0===b?_.Sd:b;a:{b=void 0===b?_.Sd:b;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof _.kd&&d.Hc(a)){a=new _.id(a,_.Pd);break a}}a=void 0}return a||_.Rd};_.Hk=/^(?:(?:https?|mailto|ftp):|[^:/?#]*(?:[/?#]|$))/i;_.Dk=function(a){if(a instanceof _.Ub)return a;a="object"==typeof a&&a.ca?a.Y():String(a);_.Hk.test(a)||(a="about:invalid#zClosurez");return new _.Ub(a,_.Tb)};

}catch(e){_._DumpException(e)}
try{
var cl,el;cl={};_.dl=function(a){this.g=cl===cl&&a||""};_.dl.prototype.ca=!0;_.dl.prototype.Y=function(){return this.g};el=/^data:(.*);base64,[a-z0-9+\/]+=*$/i;_.fl=function(a,b,c){_.Ld(a,{timestamp:(new Date).getTime(),type:"ui_change",uiActivityType:b},c)};
_.gl=function(a,b){var c=Math.min(500,screen.width-40);var d=Math.min(550,screen.height-40);c=["toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=no,resizable=no,copyhistory=no","width="+c,"height="+d,"top="+(screen.height/2-d/2),"left="+(screen.width/2-c/2)].join();d=a;d instanceof _.Ub||(d="object"==typeof d&&d.ca?d.Y():String(d),_.Hk.test(d)?d=new _.Ub(d,_.Tb):(d=String(d),d=d.replace(/(%0A|%0D)/g,""),d=d.match(el)?new _.Ub(d,_.Tb):null));b=b.Y();b=window.open(_.Fk(d||_.Vb),
b,c);if(!b||b.closed||"undefined"==typeof b.closed)return _.z("Failed to open popup window on url: "+a+". Maybe blocked by the browser?"),null;b.focus();return b};

}catch(e){_._DumpException(e)}
try{
var hl=function(a,b){_.Ld(a,{timestamp:(new Date).getTime(),type:"user_action",userActivityType:b},void 0)},il=function(a){var b=_.T;a=_.Ih(_.Mh(6,!1,a),6);a=_.P(a,3,!1);b.log(a)},jl=function(a,b){var c=_.T;a=_.Ih(_.Nh(a,2,b),9);c.log(a)},kl=function(a,b){return _.ni(a,{type:"command",command:"close",suppress:!!b})},ll=function(a){return _.ni(a,{type:"command",command:"cancel_protect_start"})},ml=function(a){return void 0===a.j||700>performance.now()-a.j},nl=function(a,b){if(!a||!b)return!1;if(a.contains&&
1==b.nodeType)return a==b||a.contains(b);if("undefined"!=typeof a.compareDocumentPosition)return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a},ol=function(){return"Sign in with Google"},pl=function(a){var b=a.appName;a=a.Nb;return _.Mj(new _.Ij("{CREDENTIALS_LENGTH_1,plural,=1{Sign in to {APP_NAME} with Google. Account chooser from Google Sign In. 1 account to choose from.}other{Sign in to {APP_NAME} with Google. Account chooser from Google Sign In. {CREDENTIALS_LENGTH_2} accounts to choose from.}}"),
{CREDENTIALS_LENGTH_1:a,APP_NAME:b,CREDENTIALS_LENGTH_2:""+a})},ql=function(a,b,c){var d="";switch(_.bb(c)?c.toString():c){case "signup":c=d;d="";b?(a='Create an <span class="'+(_.O("bltWBb")+'">')+_.M(a)+"</span> account with Google",d+=a):(a='Create a <span class="'+(_.O("bltWBb")+'">')+_.M(a)+"</span> account with Google",d+=a);a=(0,_.L)(d);d=c+a;break;case "use":c=d;a='Use <span class="'+(_.O("bltWBb")+'">')+_.M(a)+"</span> with Google";a=(0,_.L)(a);d=c+a;break;default:c=d,a='Sign in to <span class="'+
(_.O("bltWBb")+'">')+_.M(a)+"</span> with Google",a=(0,_.L)(a),d=c+a}return(0,_.L)(d)},rl=function(a,b,c,d){_.Yi.call(this,void 0===c?!1:c,void 0===d?500:d);this.L=a;this.m=b;this.g=!0};_.A(rl,_.Yi);rl.prototype.i=function(){this.L.classList.add(this.m)};
var sl=function(a,b,c){_.Yi.call(this,void 0===b?!1:b,void 0===c?0:c);this.m=a;_.V.cb(this.m);this.g=!0};_.A(sl,_.Yi);
var X=function(a,b){_.cj.call(this,a,b);this.v=b.uiMode;this.visibilityObserver=b.visibilityObserver;this.onCloseHandler=b.onCloseHandler;this.onInteractionHandler=b.onInteractionHandler};_.A(X,_.cj);X.prototype.M=function(){"card"===this.v?this.h.classList.add("WsjYwc-haAclf"):(this.h.classList.add("cGMI2b-vOE8Lb-haAclf"),this.h.classList.add("hOedQd-QFlW2-HQkcwf"));return Promise.resolve()};X.prototype.za=function(){var a="bottom_sheet"===this.v;return _.dj().offsetHeight+(a?2:0)};
X.prototype.P=function(){"card"==this.v?this.h.classList.remove("WsjYwc-haAclf"):(this.h.classList.remove("cGMI2b-vOE8Lb-haAclf"),this.h.classList.remove("hOedQd-QFlW2-HQkcwf"));_.cj.prototype.P.call(this)};X.prototype.C=function(a){a=void 0===a?{}:a;return tl(this,!0,{W:void 0===a.W?!1:a.W,Z:void 0===a.Z?!1:a.Z})};
var tl=function(a,b,c){var d=void 0===c?{}:c;c=void 0===d.W?!1:d.W;d=void 0===d.Z?!1:d.Z;a=(void 0===b?0:b)?_.cj.prototype.C.call(a,{W:c,Z:d}):Promise.resolve();var e=new rl(_.dj(),"xTMeO",!0,250);return a.then(function(){return e.start()})},wl=function(a){ul(a);vl(a)},ul=function(a){if(a.onCloseHandler){var b=_.Q("close");b&&(_.S(a,b,function(c){jl(a.g,c);a.onCloseHandler("User closed the embedded credentials picker")}),a.I(a.h,"keydown",function(c){27==(c.charCode||c.keyCode)&&(jl(a.g,c),a.onCloseHandler("User closed the embedded credentials picker"))}))}},
vl=function(a){a.onInteractionHandler&&(a.I(a.h,"click",function(){a.onInteractionHandler()}),a.I(a.h,"touchend",function(){a.onInteractionHandler()}),a.I(a.h,"focusin",function(b){var c=b;b.Ec&&(c=b.R);if(nl(a.h,c.relatedTarget))a.onInteractionHandler()}))};X.prototype.l=function(){return _.hf("TAKBxb",_.dj())};
var xl=function(a){return"card"===a.v},zl=function(a){if(_.ob){var b=_.Q("close");b&&a.onCloseHandler&&a.I(b,"touchend",function(c){jl(a.g,c);a.onCloseHandler("User closed the embedded credentials picker")});yl(a)}return _.Ug(50).then(function(){return(new rl(_.dj(),"MjR6uf",!0,250)).start()})},yl=function(a){if(2>=a.credentials.length)a.I(document,"touchmove",function(h){h.preventDefault()});else{var b=_.Q("credentials-picker"),c=60*(a.credentials.length-2.5),d=null,e=null,f=function(){e&&a.na(e);
e=a.I(document,"touchmove",function(h){var l=h.R;d&&l.touches&&(l=l.touches[0].clientY-d,0<l&&0==b.scrollTop?h.preventDefault():0>l&&b.scrollTop>=c?h.preventDefault():g())})},g=function(){e&&a.na(e);d=null};a.I(b,"touchstart",function(h){h.stopPropagation();h=h.R;h.touches&&(0==b.scrollTop||b.scrollTop>=c)&&(d=h.touches[0].clientY,f())});a.I(document,"touchend",g);a.I(document,"touchcancel",g);a.I(document,"touchstart",function(h){h.preventDefault()})}};
var Al=function(a){a='<div class="'+_.O("r4nke")+'" id="picker-header"><div class="'+_.O("r4nke-LS81yb")+'" id="picker-title-section"><div class="'+_.O("jcJzye-Bz112c")+'" aria-hidden=true>'+_.Ri()+'</div><h1 class="'+_.O("tJHJj")+" "+_.O("ZYIfFd-aGxpHf-FnSee")+'" id="picker-title"><span role="text">'+_.M(a)+'</span></h1><div class="'+_.O("tJHJj")+" "+_.O("L6cTce")+" "+_.O("ti6hGc-aGxpHf-FnSee")+'" id="signing-in">'+_.M("Verifying...")+"</div></div>";a+='<div id="close" class="'+_.O("TvD9Pc-Bz112c")+
" "+_.O("ZYIfFd-aGxpHf-FnSee")+'" role="button" aria-label="';a+=_.ee("Close");var b='" tabindex="0"><div class="'+_.O("Bz112c-ZmdkE")+'"></div>';var c=(0,_.L)('<svg class="'+_.O("Bz112c")+" "+_.O("Bz112c-r9oPif")+'" xmlns="https://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#5f6368"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/><path fill="none" d="M0 0h24v24H0z"/></svg>');return(0,_.L)(a+(b+c+"</div></div>"))},Bl=function(){return(0,_.L)('<div id="progress-bar" class="'+
_.O("P1ekSe-ZMv3u")+' mdl-progress mdl-js-progress mdl-progress__indeterminate"></div>')};
var Cl=function(a){var b=a.context,c=a.credentials,d=a.Ic,e=a.appName,f=a.Oa,g=a.Oc,h=a.cancelable;h=void 0===h?!1:h;a=_.L;b='<div id="animated-container" class="'+_.O("YLEHIf-haAclf")+" "+_.O("fFW7wc-LYNcwc")+'"><div id="credentials-picker-container" class="'+_.O("Sx9Kwc-haAclf")+'" role="dialog" tabindex="-1" aria-label="'+_.O(ol())+'">'+Al((0,_.be)(""+ql(e,f,b)))+Bl()+'<div class="'+_.O("k77Iif")+'">'+_.Uj(c,g,void 0,h)+"</div>";g?(d=c.length-d,c='<div class="'+_.O("yePe5c-sM5MNb")+'" role="button" id="show-more-accounts" tabindex="0" aria-labelledby="footer-label"><div class="'+
_.O("yePe5c")+'">',g=_.Mj(new _.Ij("{CREDENTIALS_COUNT_1,plural,=0{No other account}=1{1 more account}other{{CREDENTIALS_COUNT_2} more accounts}}"),{CREDENTIALS_COUNT_1:d,CREDENTIALS_COUNT_2:_.M(d)}),c=c+g+('</div><div id="footer-label" class="'+_.O("L6cTce")+'">'),d=_.Mj(new _.Ij("{CREDENTIALS_COUNT_1,plural,=1{Check 1 more account}other{Check {CREDENTIALS_COUNT_2} more accounts}}"),{CREDENTIALS_COUNT_1:d,CREDENTIALS_COUNT_2:_.M(d)}),c=c+d+"</div></div>",c=(0,_.L)(c)):c="";return a(b+c+'</div></div><div id="screen-reader-live" class="'+
_.O("ynRLnc")+'"></div>')},Dl=function(a){var b=a.context,c=a.credential,d=a.appName,e=a.Oa,f=a.zc,g=a.vb,h=a.Rc,l=a.Bc,m=a.cancelable;m=void 0===m?!1:m;a=_.L;b='<div id="animated-container" class="'+_.O("YLEHIf-haAclf")+" "+_.O("fFW7wc-LYNcwc")+'"><div id="credentials-picker-container" class="'+_.O("Sx9Kwc-haAclf")+" "+(l?_.O("McfNlf"):"")+'" role="dialog" tabindex="-1" aria-label="'+_.O(ol())+'">'+Al((0,_.be)(""+ql(d,e,b)))+Bl()+'<div class="'+_.O("k77Iif")+'">'+_.Tj(c,0,!0,m)+'<div class="'+_.O("wk4LHf-haAclf")+
'">';l?(l='<div class="'+_.O("wk4LHf")+'" id="gdpr-message" tabindex="-1">',c="To create your account, Google will share your name, email address, and profile picture with "+_.M(d)+".",l=l+c+" ",_.ae(g)&&_.ae(h)?(g='<a href="'+_.O(_.Qi(g)),g=g+'" target="_blank" class="'+_.O("hSRGPd"),g+='" id="privacy-policy-link" title="',c=_.O(d)+"'s Privacy Policy",g+=_.ee(c),g+='">',h='<a href="'+_.O(_.Qi(h)),h=h+'" target="_blank" class="'+_.O("hSRGPd"),h+='" id="terms-of-service-link" title="',c=_.O(d)+"'s Terms of Service",
h+=_.ee(c),h+='">',d="See "+_.M(d)+"'s "+g+"privacy policy</a> and "+h+"terms of service</a>.",l+=d):g?(h='<a href="'+_.O(_.Qi(g)),h=h+'" target="_blank" class="'+_.O("hSRGPd"),h+='" id="privacy-policy-link" title="',g=_.O(d)+"'s Privacy Policy",h+=_.ee(g),h+='">',d="See "+_.M(d)+"'s "+h+"privacy policy</a> and terms of service.",l+=d):h&&(h='<a href="'+_.O(_.Qi(h)),h=h+'" target="_blank" class="'+_.O("hSRGPd"),h+='" id="terms-of-service-link" title="',g=_.O(d)+"'s Terms of Service",h+=_.ee(g),h+=
'">',d="See "+_.M(d)+"'s privacy policy and "+h+"terms of service</a>.",l+=d),d=(0,_.L)(l+"</div>")):d="";d=b+d;b='<div class="'+_.O("k77Iif-v3pZbf-LgbsSe")+'"><button class="'+_.O("LgbsSe")+" "+_.O("LgbsSe-ssJRIf")+" "+_.O("LgbsSe-KoToPc")+' mdl-button mdl-js-button" id="continue-as"><div class="'+_.O("LgbsSe-MJoBVe")+'"></div><div class="'+_.O("LgbsSe-bN97Pc")+'">';f="Continue as "+_.M(f);f=(0,_.L)(b+f+"</div></button></div>");return a(d+f+'</div></div></div></div><div id="screen-reader-live" class="'+
_.O("ynRLnc")+'"></div>')},El=function(a){var b=a.credential;a=a.cancelable;a=void 0===a?!1:a;return(0,_.L)('<div id="animated-container" class="'+_.O("YLEHIf-haAclf")+" "+_.O("XHgP6b-mKZypf-bEDTcc-LYNcwc")+'"><div id="credentials-picker-container" class="'+_.O("Sx9Kwc-haAclf")+'" role="dialog" tabindex="-1" aria-label="'+_.O(ol())+'"><div class="'+_.O("r4nke")+'"><div class="'+_.O("jcJzye-Bz112c")+'" aria-hidden=true>'+_.Ri()+'</div><div class="'+_.O("tJHJj")+'" id="signing-in">'+(a?_.M("Signing you in"):
_.M("Verifying..."))+"</div></div>"+Bl()+'<div class="'+_.O("k77Iif")+'"><div id="credentials-picker" class="'+_.O("fFW7wc")+'">'+_.Tj(b,0,void 0,a)+'</div></div></div><div id="screen-reader-live" class="'+_.O("ynRLnc")+'"></div></div>')},Fl=function(a){var b=a.appName;a='<div id="animated-container" class="'+_.O("YLEHIf-haAclf")+'"><div id="credentials-picker-container" class="'+_.O("Sx9Kwc-haAclf")+" "+_.O("lgKYGb-v0h5Oe")+'" role="dialog" tabindex="-1" aria-label="'+_.O(ol())+'">'+Al((0,_.be)(""+
_.M("Sign in with Google")))+'<div class="'+_.O("k77Iif")+'"><div class="'+_.O("lgKYGb-v0h5Oe-Ne3sFf")+'"><p class="'+_.O("k77Iif-v0h5Oe")+'">';b="Use your Google Account to sign in to "+_.M(b);a=a+b+('</p><p class="'+_.O("u6YOj-v0h5Oe")+'">');a+="No more passwords to remember. Signing in is fast, simple and secure.";b='</p><div class="'+_.O("lgKYGb-v0h5Oe-HiaYvf")+'">'+(0,_.L)('<svg xmlns="http://www.w3.org/2000/svg" width="109" height="68" viewBox="0 0 109 68" fill="none"><path d="M76.9939 45.6097C83.6315 45.6097 89.0123 40.2587 89.0123 33.6579C89.0123 27.0571 83.6315 21.7061 76.9939 21.7061C70.3563 21.7061 64.9754 27.0571 64.9754 33.6579C64.9754 40.2587 70.3563 45.6097 76.9939 45.6097Z" fill="#F1F3F4"/><path opacity="0.04" d="M66.2924 28.242C68.28 28.0286 70.1827 27.3253 71.8279 26.1957C73.4731 25.0662 74.8088 23.5461 75.7139 21.7734C73.7264 21.9874 71.824 22.6909 70.1788 23.8204C68.5337 24.9499 67.1979 26.4696 66.2924 28.242Z" fill="#202124"/><path d="M89.0123 62.3417C94.3211 62.3417 98.6246 58.062 98.6246 52.7827C98.6246 47.5034 94.3211 43.2236 89.0123 43.2236C83.7036 43.2236 79.4 47.5034 79.4 52.7827C79.4 58.062 83.7036 62.3417 89.0123 62.3417Z" fill="#F8F9FA"/><path d="M5.74464 34.4539C8.84083 34.4539 11.3508 31.9578 11.3508 28.8788C11.3508 25.7998 8.84083 23.3037 5.74464 23.3037C2.64845 23.3037 0.138489 25.7998 0.138489 28.8788C0.138489 31.9578 2.64845 34.4539 5.74464 34.4539Z" fill="#F8F9FA"/><path d="M40.9385 64.7347C47.5761 64.7347 52.957 59.3837 52.957 52.7829C52.957 46.1821 47.5761 40.8311 40.9385 40.8311C34.3009 40.8311 28.92 46.1821 28.92 52.7829C28.92 59.3837 34.3009 64.7347 40.9385 64.7347Z" fill="#F8F9FA"/><path opacity="0.04" d="M40.9385 40.8311C39.1467 40.8323 37.378 41.2339 35.7631 42.006C36.5909 45.7269 38.6702 49.0556 41.6577 51.4423C44.6451 53.829 48.3618 55.1309 52.1939 55.1329H52.7231C52.8792 54.3633 52.9575 53.5802 52.957 52.7951C52.9586 51.2246 52.6489 49.6691 52.0457 48.2176C51.4424 46.7661 50.5574 45.4471 49.4412 44.336C48.325 43.2249 46.9996 42.3434 45.5406 41.742C44.0817 41.1406 42.5178 40.8311 40.9385 40.8311Z" fill="#202124"/><path opacity="0.04" d="M29.9908 47.8623C32.207 47.7738 34.3544 47.0715 36.1908 45.8345C38.0272 44.5976 39.4797 42.8751 40.3846 40.8613C38.1714 40.9616 36.0292 41.6686 34.1949 42.9041C32.3606 44.1397 30.9056 45.8557 29.9908 47.8623Z" fill="#202124"/><path opacity="0.6" d="M16.9015 24.099C22.2137 24.099 26.52 19.8166 26.52 14.5339C26.52 9.25121 22.2137 4.96875 16.9015 4.96875C11.5894 4.96875 7.28308 9.25121 7.28308 14.5339C7.28308 19.8166 11.5894 24.099 16.9015 24.099Z" fill="white" stroke="#F1F3F4" stroke-width="0.63" stroke-miterlimit="10"/><path d="M64.9754 26.4857C71.613 26.4857 76.9939 21.1347 76.9939 14.5339C76.9939 7.93305 71.613 2.58203 64.9754 2.58203C58.3378 2.58203 52.957 7.93305 52.957 14.5339C52.957 21.1347 58.3378 26.4857 64.9754 26.4857Z" fill="#F8F9FA"/><path opacity="0.6" d="M88.9877 24.038C94.2965 24.038 98.6 19.7582 98.6 14.4789C98.6 9.19964 94.2965 4.91992 88.9877 4.91992C83.679 4.91992 79.3754 9.19964 79.3754 14.4789C79.3754 19.7582 83.679 24.038 88.9877 24.038Z" fill="white" stroke="#F1F3F4" stroke-width="0.63" stroke-miterlimit="10"/><path d="M28.92 45.6097C35.5576 45.6097 40.9385 40.2587 40.9385 33.6579C40.9385 27.0571 35.5576 21.7061 28.92 21.7061C22.2824 21.7061 16.9016 27.0571 16.9016 33.6579C16.9016 40.2587 22.2824 45.6097 28.92 45.6097Z" fill="#F1F3F4"/><path opacity="0.04" d="M40.6985 31.259C40.1383 28.563 38.6613 26.1412 36.5161 24.4013C34.3708 22.6614 31.6883 21.7096 28.92 21.7061C28.4037 21.7076 27.8878 21.7403 27.3754 21.804C28.1409 23.9026 29.3876 25.7949 31.0166 27.3309C32.6456 28.867 34.612 30.0044 36.76 30.6531C35.3692 34.5212 35.4172 38.7568 36.8954 42.5927C38.1677 41.4735 39.1861 40.0981 39.8832 38.5577C40.5802 37.0173 40.94 35.3471 40.9385 33.6579C40.937 32.8524 40.8566 32.049 40.6985 31.259Z" fill="#202124"/><path d="M16.9015 67.1268C24.868 67.1268 31.3262 60.7045 31.3262 52.7822C31.3262 44.8598 24.868 38.4375 16.9015 38.4375C8.93505 38.4375 2.47693 44.8598 2.47693 52.7822C2.47693 60.7045 8.93505 67.1268 16.9015 67.1268Z" fill="#FEEFC3"/><path d="M64.9754 67.1268C72.9419 67.1268 79.4 60.7045 79.4 52.7822C79.4 44.8598 72.9419 38.4375 64.9754 38.4375C57.0089 38.4375 50.5508 44.8598 50.5508 52.7822C50.5508 60.7045 57.0089 67.1268 64.9754 67.1268Z" fill="#CEEAD6"/><path d="M70.4892 58.7066H62.4893L70.4892 50.751V58.7066Z" fill="#81C995"/><path d="M40.9385 28.8788C48.905 28.8788 55.3631 22.4564 55.3631 14.5341C55.3631 6.61177 48.905 0.189453 40.9385 0.189453C32.972 0.189453 26.5139 6.61177 26.5139 14.5341C26.5139 22.4564 32.972 28.8788 40.9385 28.8788Z" fill="#D2E3FC"/><path d="M35.4 14.455C35.4 12.9943 35.9835 11.5934 37.0222 10.5605C38.0609 9.52755 39.4696 8.94727 40.9385 8.94727C42.4074 8.94727 43.8161 9.52755 44.8548 10.5605C45.8934 11.5934 46.477 12.9943 46.477 14.455H35.4Z" fill="#8AB4F8"/><path opacity="0.2" d="M69.6646 39.2216C67.4927 38.4762 65.1731 38.258 62.8991 38.5852C60.6252 38.9124 58.4629 39.7755 56.5925 41.1026C54.7222 42.4296 53.1979 44.1821 52.147 46.2139C51.096 48.2458 50.5487 50.498 50.5508 52.7829C50.5508 53.1745 50.5508 53.5601 50.6062 53.9456C51.381 54.0534 52.1623 54.1086 52.9446 54.1109C57.0857 54.1103 61.081 52.591 64.166 49.8439C67.2509 47.0967 69.2087 43.3146 69.6646 39.2216Z" fill="#34A853"/><path d="M52.9569 50.3895C62.2489 50.3895 69.7815 42.8986 69.7815 33.6581C69.7815 24.4176 62.2489 16.9268 52.9569 16.9268C43.665 16.9268 36.1323 24.4176 36.1323 33.6581C36.1323 42.8986 43.665 50.3895 52.9569 50.3895Z" fill="white"/><path d="M21.8431 57.0357H11.96L16.8831 48.5293L21.8431 57.0357Z" fill="#FDD663"/><path opacity="0.04" d="M97.7878 48.8841C97.2557 47.6921 96.4835 46.6214 95.5187 45.738C94.554 44.8547 93.4173 44.1776 92.1789 43.7486C90.9405 43.3195 89.6268 43.1477 88.319 43.2436C87.0112 43.3396 85.7371 43.7014 84.5754 44.3065C85.7871 45.8468 87.3356 47.0926 89.1034 47.9493C90.8712 48.8061 92.812 49.2513 94.7785 49.2513C95.7927 49.2483 96.8029 49.125 97.7878 48.8841Z" fill="#202124"/><path opacity="0.04" d="M81.8001 36.3386C81.7993 38.8522 82.5372 41.3111 83.9231 43.4131C85.2478 42.4879 86.3705 41.3057 87.2236 39.9377C88.0766 38.5696 88.6424 37.044 88.8867 35.4527C89.1311 33.8614 89.049 32.2373 88.6455 30.6784C88.2419 29.1195 87.5252 27.658 86.5385 26.3818C85.0559 27.5884 83.8617 29.1077 83.0421 30.8298C82.2226 32.5518 81.7984 34.4335 81.8001 36.3386Z" fill="#202124"/><path d="M95.5292 46.938C102.697 46.938 108.508 41.1596 108.508 34.0315C108.508 26.9034 102.697 21.125 95.5292 21.125C88.3614 21.125 82.5508 26.9034 82.5508 34.0315C82.5508 41.1596 88.3614 46.938 95.5292 46.938Z" fill="#FAD2CF"/><path d="M99.1446 34.3067L95.2544 30.4381C94.3316 29.5203 92.8364 29.5194 91.9147 30.4359C90.9931 31.3524 90.9941 32.8394 91.9169 33.7571L95.8071 37.6257C96.7299 38.5434 98.2252 38.5444 99.1468 37.6279C100.068 36.7113 100.067 35.2244 99.1446 34.3067Z" fill="#F28B82"/><path d="M64.2985 32.5868C63.8976 33.1763 63.2859 33.5917 62.5879 33.7483C61.8898 33.905 61.158 33.7911 60.5414 33.4299C59.9248 33.0687 59.4699 32.4874 59.2693 31.8043C59.0686 31.1212 59.1374 30.3878 59.4615 29.7534C59.5673 29.5314 59.7041 29.3254 59.8677 29.1414L56.8954 26.8281L51.7077 33.4374L61.5539 41.0993L66.7415 34.4962L64.2985 32.5868Z" fill="#FBBC04"/><path d="M63.85 40.8551L43.3286 24.8994L40.0347 29.089L60.5562 45.0447L63.85 40.8551Z" fill="#FCC934"/><path d="M43.3323 24.9014L40.0339 29.0934L42.2308 30.8069C43.0696 30.174 43.8244 29.4378 44.4769 28.616C44.9416 28.0268 45.3535 27.3981 45.7077 26.7373L43.3323 24.9014Z" fill="#FBBC04"/><path d="M40.6924 13.4635C39.1195 12.2373 37.2162 11.5027 35.2233 11.3528C33.2304 11.2029 31.2376 11.6445 29.4972 12.6216C27.7568 13.5987 26.3471 15.0674 25.4465 16.8417C24.546 18.616 24.1951 20.6161 24.4383 22.5889C24.6816 24.5616 25.5079 26.4182 26.8129 27.9236C28.1178 29.429 29.8425 30.5155 31.7687 31.0455C33.6949 31.5755 35.736 31.5253 37.6334 30.901C39.5309 30.2768 41.1994 29.1067 42.4277 27.5389C44.0706 25.4421 44.8105 22.783 44.4852 20.1446C44.1599 17.5061 42.7959 15.1036 40.6924 13.4635ZM38.2985 24.3628C37.707 25.1149 36.9046 25.6757 35.9927 25.9744C35.0807 26.273 34.1002 26.2961 33.1751 26.0407C32.25 25.7853 31.4217 25.2629 30.7951 24.5395C30.1685 23.8161 29.7716 22.9241 29.6547 21.9765C29.5377 21.0288 29.7059 20.0679 30.138 19.2152C30.57 18.3626 31.2466 17.6565 32.0822 17.1862C32.9177 16.7158 33.8748 16.5024 34.8323 16.5729C35.7898 16.6434 36.7048 16.9946 37.4616 17.5821C38.4759 18.3715 39.1337 19.529 39.2906 20.8003C39.4475 22.0716 39.0907 23.3528 38.2985 24.3628Z" fill="#FCC934"/><path d="M27.4615 22.2383C27.1971 20.0957 27.5832 17.9234 28.5702 16.0007C29.5571 14.0781 31.0999 12.493 33 11.4492C30.343 11.7738 27.9244 13.1348 26.2764 15.2328C24.6284 17.3307 23.8859 19.9938 24.2123 22.6361C24.5387 25.2784 25.9073 27.6836 28.0169 29.3225C30.1266 30.9613 32.8045 31.6997 35.4616 31.3751C33.3636 30.8197 31.4807 29.6528 30.0551 28.0246C28.6294 26.3963 27.7262 24.3812 27.4615 22.2383Z" fill="#FBBC04"/></svg>')+
'</div></div><div class="'+_.O("wk4LHf-haAclf")+'">';var c='<div class="'+_.O("k77Iif-v3pZbf-LgbsSe")+'"><button class="'+_.O("LgbsSe")+" "+_.O("LgbsSe-ssJRIf")+" "+_.O("LgbsSe-KoToPc")+' mdl-button mdl-js-button" id="continue"><div class="'+_.O("LgbsSe-MJoBVe")+'"></div><div class="'+_.O("LgbsSe-bN97Pc")+'">';c=(0,_.L)(c+"Continue</div></button></div>");a+=b+c+'</div></div></div></div><div id="screen-reader-live" class="'+_.O("ynRLnc")+'"></div>';return(0,_.L)(a)},Gl=function(a){var b=a.errorType;
a=_.L;b='<div id="animated-container" class="'+_.O("YLEHIf-haAclf")+" "+_.O("XHgP6b-mKZypf-bEDTcc-LYNcwc")+'"><div id="credentials-picker-container" class="'+_.O("Sx9Kwc-haAclf")+'" role="dialog" tabindex="-1" aria-label="'+_.O(ol())+'">'+Al((0,_.be)(""+_.Si()))+'<div class="'+_.O("k77Iif")+'"><div id="credentials-picker" class="'+_.O("fFW7wc")+'"><div class="'+_.O("u0pjoe-Ne3sFf")+'">'+_.Ti(b)+"</div>";var c='<div class="'+_.O("u0pjoe")+'"><button class="'+_.O("LgbsSe")+" "+_.O("LgbsSe-ssJRIf")+
' mdl-button mdl-js-button" id="error-close">';c=(0,_.L)(c+"Close</button></div>");return a(b+c+'</div></div></div><div id="screen-reader-live" class="'+_.O("ynRLnc")+'"></div></div>')};
var Hl=function(a){a.sessionDataList=[a.sessionData];X.call(this,3,a);this.j=a.sessionData;this.K=a.onAutoSelectedHandler;this.T=a.onAutoSelectCanceledHandler;this.B=null;this.O=!1;this.D=_.K("cancelable_auto_select")||_.K("rp_cancelable_auto_select")};_.A(Hl,X);Hl.prototype.M=function(){X.prototype.M.call(this);_.rd(this.h,El,{credential:_.oe(this.j),cancelable:this.D});var a=_.Wg();xl(this)||(a=zl(this));this.D?Il(this):Jl(this);return a};var Jl=function(a){var b=a.l()[0];a.o=a.j;a.i=b;a.K&&a.K(a.j)};
Hl.prototype.s=function(){_.Ji(("Signing in as "+_.C(this.j,1)).toString())};var Il=function(a){if(a.onInteractionHandler)a.onInteractionHandler();Kl(a);Kl(a).then(function(){var b=_.Q("cancel-button");b&&b.setAttribute("disabled","");a.O?a.T():Jl(a)});Ll(a)},Ll=function(a){var b=_.Q("cancel-button");b&&_.S(a,b,function(c){_.Mi(a.g,12,c);a.O=!0;_.Zi(a.B,!0)})},Kl=function(a){var b=_.Q("progress-bar");a.B=new sl(b,!0,3E3);return a.B.start()};
var Ml=function(a){a.sessionDataList=[a.sessionData];X.call(this,2,a);this.j=a.sessionData;this.K=a.context;this.B=a.onCredentialConfirmedHandler;this.D=a.showGdpr};_.A(Ml,X);
Ml.prototype.M=function(){X.prototype.M.call(this);var a=_.C(this.clientData,3);_.rd(this.h,Dl,{context:this.K,credential:_.oe(this.j),appName:a,Oa:/^[aeiouy].+/i.test(a),zc:_.C(this.j,4)||_.C(this.j,3)||_.C(this.j,1),vb:_.C(this.clientData,5),Rc:_.C(this.clientData,6),Bc:this.D,cancelable:!1});Nl(this);return xl(this)?_.Wg():zl(this)};
var Nl=function(a){var b=_.Q("continue-as");_.S(a,b,function(c){var d=_.ti(a.visibilityObserver),e=ml(a.visibilityObserver);_.Mi(a.g,3,c,e,d);e?_.y("Click ignored. Clicked too soon, wait 700ms."):(c=a.l()[0],a.o=a.j,a.i=c,a.B&&a.B(a.j,d))});(b=_.Q("privacy-policy-link"))&&_.S(a,b,function(c){_.Mi(a.g,6,c)});(b=_.Q("terms-of-service-link"))&&_.S(a,b,function(c){_.Mi(a.g,7,c)});wl(a)};Ml.prototype.Aa=function(){var a=_.Q("gdpr-message");a||(a=_.Q("continue-as"));a&&a.focus()};
Ml.prototype.s=function(){var a=pl({appName:_.C(this.clientData,3),Nb:this.credentials.length});_.Ji(a.toString())};
var Ol=function(a){X.call(this,5,a);this.j=a.errorType};_.A(Ol,X);Ol.prototype.M=function(){X.prototype.M.call(this);_.rd(this.h,Gl,{errorType:this.j});Pl(this);var a=_.Wg();xl(this)||(a=zl(this));return a};var Pl=function(a){if(a.onCloseHandler){var b=_.Q("error-close");b&&_.S(a,b,function(c){jl(a.g,c);a.onCloseHandler("User closed the error message")})}};Ol.prototype.s=function(){var a=_.Si();_.Ji(a.toString())};
var Ql=function(a){X.call(this,1,a);this.K=a.context;this.j=a.onCredentialSelectedHandler;this.B=a.onBeforeExtendHandler;this.D=a.onExtendHandler};_.A(Ql,X);Ql.prototype.M=function(){X.prototype.M.call(this);var a=this.h,b=this.K,c=_.C(this.clientData,3),d=xl(this)&&2<this.credentials.length;var e=void 0===e?!1:e;var f=_.nj(this.credentials);_.rd(a,Cl,{context:b,credentials:f,Ic:2,appName:c,Oa:/^[aeiouy].+/i.test(c),Oc:d,cancelable:e});Rl(this);return xl(this)?_.Wg():zl(this)};
var Rl=function(a){var b=a.l();_.fb(b,function(c,d){var e=a.credentials[d];_.S(a,c,function(f){if(a.m)_.y("Click ignored. A credential is already being processed.");else{var g=_.ti(a.visibilityObserver),h=ml(a.visibilityObserver),l=_.T;f=_.Nh(a.g,1,f,h);f=_.P(f,11,d);l.log(f);h?_.y("Click ignored. Clicked too soon, wait 700ms."):(a.o=e,a.i=c,a.j&&a.j(e,g))}})});(b=_.Q("show-more-accounts"))&&_.S(a,b,function(c){_.Mi(a.g,8,c);Sl(a)});wl(a)};
Ql.prototype.s=function(){var a=pl({appName:_.C(this.clientData,3),Nb:this.credentials.length});_.Ji(a.toString())};var Sl=function(a){var b=new rl(_.dj(),"X9G3K",!0,250);(a.B?a.B():Promise.resolve()).then(function(){return b.start().then(function(){a.D&&a.D();a.l()[2].focus();_.lf(_.Q("credentials-picker"),{"aria-labelledBy":"list-extended-label"})})})};
var Tl=function(a){X.call(this,9,a);this.context=a.context;this.onContinueHandler=a.onContinueHandler};_.A(Tl,X);Tl.prototype.M=function(){X.prototype.M.call(this);var a=this.clientData;_.rd(this.h,Fl,{context:this.context,appName:_.C(a,3),Oa:/^[aeiouy].+/i.test(_.C(a,3))});Ul(this);return xl(this)?_.Wg():zl(this)};var Ul=function(a){var b=_.Q("continue");_.S(a,b,function(){_.Mi(a.g,13);if(a.onContinueHandler)a.onContinueHandler()});wl(a)};Tl.prototype.Aa=function(){var a=_.Q("continue");a&&a.focus()};
Tl.prototype.s=function(){_.Ji(("Sign in to "+_.C(this.clientData,3)+" with Google.").toString())};
var Vl=new _.dl("gisPopupWindow"),Wl=function(a,b){_.Yj.call(this,a,b);this.s=_.C(b,6)||"card";this.m=_.vi(a);this.T=null!=_.C(b,16)&&!0===_.I(b,16);this.o=null;_.K("enable_iov2_fix")&&(a.style.maxHeight="100vh",a.style.maxWidth="100vw")};_.A(Wl,_.Yj);Wl.prototype.i=function(){var a=this;return _.Yj.prototype.i.call(this).then(function(){var b=a.m,c=new _.Od;if(void 0!==b.i)c.resolve(b.i);else{if(b.l)throw Error("ia");b.l=c}return c.g})};
Wl.prototype.ja=function(){if("undefined"===typeof this.ga)var a=null;else a=this.ga,a=0<=a&&a<this.credentials.length?this.credentials[a]:null;return a&&_.I(a,7)?(_.Ki(),_.Li(void 0,!0,this.credentials),Xl(this,a)):1<this.credentials.length?(_.Ki(),_.Li(void 0,!1,this.credentials),Yl(this,this.credentials)):1==this.credentials.length?(_.Ki(),_.Li(void 0,!1,this.credentials),Zl(this,this.credentials[0])):_.K("enable_onetap_on_itp")?$l(this):kl(_.U(this))};
var Yl=function(a,b,c){return am(a,new Ql({container:a.h,sessionDataList:b,clientData:a.clientData,uiMode:a.s,visibilityObserver:a.m,onCloseHandler:function(d){return void bm(a,d)},onInteractionHandler:function(){return void ll(_.U(a))},context:a.context,onCredentialSelectedHandler:function(d,e){hl(a,"select_credential");cm(a,d,"user",e)},onBeforeExtendHandler:function(){var d=a.g;return d?a.l(d.za()+150):Promise.resolve()},onExtendHandler:function(){return void a.l()}}),void 0===c?!1:c)},Zl=function(a,
b,c){return am(a,new Ml({container:a.h,sessionData:b,clientData:a.clientData,uiMode:a.s,visibilityObserver:a.m,onCloseHandler:function(d){return void bm(a,d)},onInteractionHandler:function(){return void ll(_.U(a))},context:a.context,showGdpr:!_.I(b,7),onCredentialConfirmedHandler:function(d,e){hl(a,"confirm_credential");e=void 0===e?5:e;a.g&&(ll(_.U(a)),_.I(d,7)||7===e?a.ea(d,_.I(d,7)?"user":"user_1tap",!_.I(d,7)):dm(a,d))}}),void 0===c?!1:c)},Xl=function(a,b){return am(a,new Hl({container:a.h,sessionData:b,
clientData:a.clientData,uiMode:a.s,visibilityObserver:a.m,onCloseHandler:function(c){return void bm(a,c)},onInteractionHandler:function(){return void ll(_.U(a))},onAutoSelectedHandler:function(c){return void cm(a,c,"auto")},onAutoSelectCanceledHandler:function(){1<a.credentials.length?Yl(a,a.credentials,!0):Zl(a,a.credentials[0],!0);var c=_.U(a);_.ni(c,{type:"command",command:"cancel_auto_select"})}}),!1).then(function(){var c=_.U(a);_.ni(c,{type:"command",command:"start_auto_select"})})},$l=function(a){return am(a,
new Tl({container:a.h,sessionDataList:a.credentials,clientData:a.clientData,uiMode:a.s,visibilityObserver:a.m,onCloseHandler:function(b){return void bm(a,b)},onInteractionHandler:function(){return void ll(_.U(a))},context:a.context,onContinueHandler:function(){var b=_.C(a.clientData,1),c=_.C(a.clientData,2);var d=a.A;var e=void 0===e?1:e;var f=new _.Kc(location.protocol+"//"+location.host+"/gsi/select");b=_.Hd({client_id:b,origin:c,ux_mode:"popup",relay_method:2,scaft:e});d&&b.add("as",d);"warn"!==
_.Wa&&b.add("log_level",_.Wa);_.Nc(f,b);d=f.toString();em(a);a.o=_.gl(d,Vl)}}))};Wl.prototype.C=function(a){var b=this;am(this,new Ol({container:this.h,sessionDataList:this.credentials,clientData:this.clientData,uiMode:this.s,onCloseHandler:function(){return void fm(b)},onInteractionHandler:function(){return void ll(_.U(b))},errorType:void 0===a?0:a}))};
var gm=function(a,b){if("bottom_sheet"!==a.s||!_.K("variable_initial_height"))return 360;switch(b.g){case 9:return 254;case 1:return 3>_.uf(a.D,_.Jd,4).length?198:227;case 2:return b.D?271:183;case 3:return 141;default:return 360}},am=function(a,b,c){c=void 0===c?!1:c;return a.l(gm(a,b)).then(function(){a.m.reset();return _.Yj.prototype.j.call(a,b)}).then(function(){_.Li(b.g);var d=a.m;d.j=performance.now();_.x("Render time set to "+d.j,"VISIBILITY_OBSERVER");c?b.Aa():(b.s(),a.T||(d=_.Q("credentials-picker-container"))&&
d.focus(),b instanceof Hl||(d=_.U(a),_.ni(d,{type:"command",command:"cancel_protect_end"})));_.Xi(a.h)})},fm=function(a){_.x("Closing current page.");return a.g?tl(a.g).then(function(){a.g&&(a.g.P(),a.g=null)}):Promise.resolve()},cm=function(a,b,c,d){d=void 0===d?5:d;a.g&&(ll(_.U(a)),_.I(b,7)?a.ea(b,c):1===d?dm(a,b):Zl(a,b,!0))},bm=function(a,b){_.x("Canceled by user: ("+b+", true).");a.g&&(il(a.g.g),_.T.flush());hm(a);return fm(a).then(function(){kl(_.U(a),!0)})};
Wl.prototype.ea=function(a,b,c,d){c=void 0===c?!1:c;d=void 0===d?!1:d;hm(this);return _.Yj.prototype.ea.call(this,a,b,c,d)};
var dm=function(a,b){if(_.I(b,7))a.ea(b,"user");else{var c=a.nonce,d=a.A,e=location.protocol+"//"+location.host+"/gsi/confirm?";e+="client_id="+_.C(a.clientData,1)+"&origin="+_.C(a.clientData,2)+"&user_id="+_.C(b,2)+"&context="+a.context;"warn"!==_.Wa&&(e+="&log_level="+_.Wa);c&&(e+="&nonce="+c);d&&(e+="&as="+d);b=e;em(a);a.o=_.gl(b,Vl);_.fl(a,"dialog_displayed")}},hm=function(a){if(a.o){em(a);if(!a.o.closed)try{a.o.close(),_.fl(a,"dialog_closed")}catch(b){}a.o=null}},em=function(a){(a=a.o)&&a.gis&&
a.gis.provider&&a.gis.provider.popup&&a.gis.provider.popup.unlistenToPageHide&&a.gis.provider.popup.unlistenToPageHide()};
_.B("gis.provider.bootstrap",function(a,b){b=void 0===b?"container":b;if(a&&"null"!==a)if(a=_.ng(a,_.Wj,_.Xj),null!=_.C(a,2))_.te(_.G(a,_.$e,2));else{var c=_.G(a,_.Jg,1);c&&_.C(c,2)&&_.se(_.C(c,2));var d=document.getElementById(b);if(d)if(b=_.G(a,_.Og,3))if(_.C(a,5)){var e=(_.va()||_.wi()||_.ya()&&0<=_.Ce(_.gd(),"14.4"))&&_.K("enable_onetap_on_itp"),f=_.uf(a,_.Jd,4);if(!e){if(!f){_.z("Failed to display picker. No list of sessions received from the server.");return}if(0===f.length){_.z("Failed to display picker. The list of sessions received from the server was empty. Perhaps there are no active sessions on the browser.");
return}}e="bottom_sheet"===_.C(a,6)?"bottom_sheet":"card";var g=_.C(a,9);g||(g="signin");!_.C(a,10)&&f&&0<f.length?_.z("Failed to display picker. No XSRF token received from the server."):(c=c&&_.C(c,1)||void 0,_.Kh(),_.T.g={session_id:c,client_origin:_.C(b,2),client_id:_.C(b,1),context:_.Wi(g),ui_mode:"card"===e?1:2},a=new Wl(d,a),window.__G_CREDENTIAL_PROVIDER__=a,a.i())}else _.z("Failed to display picker. No channel id received from the server.");else _.z("Failed to display picker. No client data received from the server.");
else _.z("Failed to display picker. Cannot find container element with the provided id: "+b+".")}else _.z("Failed to display picker. No credential picker data received from server.")});_.B("gis.provider.onPopupConfirm",function(a){var b=window.__G_CREDENTIAL_PROVIDER__;if(b){_.x("Popup confirmed");hl(b,"confirm_dialog");a:{for(var c=0;c<b.credentials.length;c++)if(_.C(b.credentials[c],2)===a){a=b.credentials[c];break a}a=null}b.ea(a,"user_2tap",!0)}else _.z("Picker component instance not found.")});
_.B("gis.provider.onPopupCancel",function(){var a=window.__G_CREDENTIAL_PROVIDER__;a?(_.x("Popup canceled"),hl(a,"cancel_dialog"),hm(a)):_.z("Picker component instance not found.")});_.B("gis.provider.onPopupClose",function(){var a=window.__G_CREDENTIAL_PROVIDER__;a?(_.x("Popup closed"),hl(a,"close_dialog")):_.z("Picker component instance not found.")});_.B("gis.provider.onPublishActivity",function(a){var b=window.__G_CREDENTIAL_PROVIDER__;b?b.Za(a):_.z("Picker component instance not found.")});
_.B("gis.provider.relayCredentialResponse",function(a){var b=window.__G_CREDENTIAL_PROVIDER__;b?(console.log("credential relayed"),b.kb(a),hm(b)):_.z("Picker component instance not found.")});

}catch(e){_._DumpException(e)}
}).call(this,this.default_gsi);
// Google Inc.

//# sourceURL=/_/gsi/_/js/k=gsi.gsi.en.tGHvPBfW0kc.O/am=whU/d=1/rs=AF0KOtX61KnPGiQYfSUEm0kTfaRXpxzyuw/m=credential_server_library</script></head><body class="qJTHM"><div id="container" class="haAclf WsjYwc-haAclf" style="max-height: 100vh; max-width: 100vw;"><div id="animated-container" class="YLEHIf-haAclf fFW7wc-LYNcwc"><div id="credentials-picker-container" class="Sx9Kwc-haAclf" role="dialog" tabindex="-1" aria-label="Sign in with Google"><div class="r4nke" id="picker-header"><div class="r4nke-LS81yb" id="picker-title-section"><div class="jcJzye-Bz112c" aria-hidden="true"><svg class="Bz112c Bz112c-E3DyYd" xmlns="https://www.w3.org/2000/svg" viewBox="0 0 48 48"><path fill="#4285F4" d="M45.12 24.5c0-1.56-.14-3.06-.4-4.5H24v8.51h11.84c-.51 2.75-2.06 5.08-4.39 6.64v5.52h7.11c4.16-3.83 6.56-9.47 6.56-16.17z"></path><path fill="#34A853" d="M24 46c5.94 0 10.92-1.97 14.56-5.33l-7.11-5.52c-1.97 1.32-4.49 2.1-7.45 2.1-5.73 0-10.58-3.87-12.31-9.07H4.34v5.7C7.96 41.07 15.4 46 24 46z"></path><path fill="#FBBC05" d="M11.69 28.18C11.25 26.86 11 25.45 11 24s.25-2.86.69-4.18v-5.7H4.34C2.85 17.09 2 20.45 2 24c0 3.55.85 6.91 2.34 9.88l7.35-5.7z"></path><path fill="#EA4335" d="M24 10.75c3.23 0 6.13 1.11 8.41 3.29l6.31-6.31C34.91 4.18 29.93 2 24 2 15.4 2 7.96 6.93 4.34 14.12l7.35 5.7c1.73-5.2 6.58-9.07 12.31-9.07z"></path><path fill="none" d="M2 2h44v44H2z"></path></svg></div><h1 class="tJHJj ZYIfFd-aGxpHf-FnSee" id="picker-title"><span role="text">Sign in to <span class="bltWBb">Dribbble</span> with Google</span></h1><div class="tJHJj L6cTce ti6hGc-aGxpHf-FnSee" id="signing-in">Verifying...</div></div><div id="close" class="TvD9Pc-Bz112c ZYIfFd-aGxpHf-FnSee" role="button" aria-label="Close" tabindex="0"><div class="Bz112c-ZmdkE"></div><svg class="Bz112c Bz112c-r9oPif" xmlns="https://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#5f6368"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"></path><path fill="none" d="M0 0h24v24H0z"></path></svg></div></div><div id="progress-bar" class="P1ekSe-ZMv3u mdl-progress mdl-js-progress mdl-progress__indeterminate is-upgraded" data-upgraded=",MaterialProgress"><div class="progressbar bar bar1" style="width: 0%;"></div><div class="bufferbar bar bar2" style="width: 100%;"></div><div class="auxbar bar bar3" style="width: 0%;"></div></div><div class="k77Iif"><div id="credentials-picker" class="fFW7wc" tabindex="-1" aria-labelledby="list-extended-label"><div class="fFW7wc-ibnC6b-sM5MNb TAKBxb " aria-labelledby="picker-item-label-0" role="link" tabindex="0"><div class="fFW7wc-ibnC6b"><div class="aZ2wEe mdl-spinner mdl-js-spinner mdl-spinner--single-color is-upgraded" aria-hidden="true" data-upgraded=",MaterialSpinner"><div class="mdl-spinner__layer mdl-spinner__layer-1"><div class="mdl-spinner__circle-clipper mdl-spinner__left"><div class="mdl-spinner__circle"></div></div><div class="mdl-spinner__gap-patch"><div class="mdl-spinner__circle"></div></div><div class="mdl-spinner__circle-clipper mdl-spinner__right"><div class="mdl-spinner__circle"></div></div></div><div class="mdl-spinner__layer mdl-spinner__layer-2"><div class="mdl-spinner__circle-clipper mdl-spinner__left"><div class="mdl-spinner__circle"></div></div><div class="mdl-spinner__gap-patch"><div class="mdl-spinner__circle"></div></div><div class="mdl-spinner__circle-clipper mdl-spinner__right"><div class="mdl-spinner__circle"></div></div></div><div class="mdl-spinner__layer mdl-spinner__layer-3"><div class="mdl-spinner__circle-clipper mdl-spinner__left"><div class="mdl-spinner__circle"></div></div><div class="mdl-spinner__gap-patch"><div class="mdl-spinner__circle"></div></div><div class="mdl-spinner__circle-clipper mdl-spinner__right"><div class="mdl-spinner__circle"></div></div></div><div class="mdl-spinner__layer mdl-spinner__layer-4"><div class="mdl-spinner__circle-clipper mdl-spinner__left"><div class="mdl-spinner__circle"></div></div><div class="mdl-spinner__gap-patch"><div class="mdl-spinner__circle"></div></div><div class="mdl-spinner__circle-clipper mdl-spinner__right"><div class="mdl-spinner__circle"></div></div></div></div><div class="MPu53c" aria-hidden="true"><svg class="Bz112c Bz112c-E3DyYd" fill="#FFFFFF" viewBox="0 0 24 24" xmlns="https://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"></path></svg></div><img class="fFW7wc-ibnC6b-HiaYvf" src="./unnamed.jpg" alt="Rizwan khan&#39;s profile image" aria-hidden="true"><div class="fFW7wc-ibnC6b-r4m2rf" aria-hidden="true"><div class="fFW7wc-ibnC6b-ssJRIf">Rizwan khan</div><div class="fFW7wc-ibnC6b-K4efff"><EMAIL></div></div></div><div id="picker-item-label-0" class="L6cTce">Google Account, Rizwan khan's profile picture, <EMAIL>, Rizwan khan</div></div><div class="fFW7wc-ibnC6b-sM5MNb TAKBxb " aria-labelledby="picker-item-label-1" role="link" tabindex="0"><div class="fFW7wc-ibnC6b"><div class="aZ2wEe mdl-spinner mdl-js-spinner mdl-spinner--single-color is-upgraded" aria-hidden="true" data-upgraded=",MaterialSpinner"><div class="mdl-spinner__layer mdl-spinner__layer-1"><div class="mdl-spinner__circle-clipper mdl-spinner__left"><div class="mdl-spinner__circle"></div></div><div class="mdl-spinner__gap-patch"><div class="mdl-spinner__circle"></div></div><div class="mdl-spinner__circle-clipper mdl-spinner__right"><div class="mdl-spinner__circle"></div></div></div><div class="mdl-spinner__layer mdl-spinner__layer-2"><div class="mdl-spinner__circle-clipper mdl-spinner__left"><div class="mdl-spinner__circle"></div></div><div class="mdl-spinner__gap-patch"><div class="mdl-spinner__circle"></div></div><div class="mdl-spinner__circle-clipper mdl-spinner__right"><div class="mdl-spinner__circle"></div></div></div><div class="mdl-spinner__layer mdl-spinner__layer-3"><div class="mdl-spinner__circle-clipper mdl-spinner__left"><div class="mdl-spinner__circle"></div></div><div class="mdl-spinner__gap-patch"><div class="mdl-spinner__circle"></div></div><div class="mdl-spinner__circle-clipper mdl-spinner__right"><div class="mdl-spinner__circle"></div></div></div><div class="mdl-spinner__layer mdl-spinner__layer-4"><div class="mdl-spinner__circle-clipper mdl-spinner__left"><div class="mdl-spinner__circle"></div></div><div class="mdl-spinner__gap-patch"><div class="mdl-spinner__circle"></div></div><div class="mdl-spinner__circle-clipper mdl-spinner__right"><div class="mdl-spinner__circle"></div></div></div></div><div class="MPu53c" aria-hidden="true"><svg class="Bz112c Bz112c-E3DyYd" fill="#FFFFFF" viewBox="0 0 24 24" xmlns="https://www.w3.org/2000/svg"><path fill="none" d="M0 0h24v24H0z"></path><path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"></path></svg></div><img class="fFW7wc-ibnC6b-HiaYvf" src="./unnamed(1).jpg" alt="Rizwan Khan&#39;s profile image" aria-hidden="true"><div class="fFW7wc-ibnC6b-r4m2rf" aria-hidden="true"><div class="fFW7wc-ibnC6b-ssJRIf">Rizwan Khan</div><div class="fFW7wc-ibnC6b-K4efff"><EMAIL></div></div></div><div id="picker-item-label-1" class="L6cTce">Google Account, Rizwan Khan's profile picture, <EMAIL>, Rizwan Khan</div></div></div><div id="list-collapsed-label" class="L6cTce">Google account list with 2 items.</div><div id="list-extended-label" class="L6cTce">Google account list with 2 items.</div></div></div></div><div id="screen-reader-live" class="ynRLnc"></div></div><script nonce="">gis.provider.bootstrap('***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\x3d');</script></body></html>