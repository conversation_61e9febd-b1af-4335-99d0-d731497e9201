(window.webpackJsonp=window.webpackJsonp||[]).push([[95,3,132],{1025:function(t,e,s){"use strict";s(979)},1026:function(t,e,s){"use strict";s(980)},1027:function(t,e,s){"use strict";s(981)},1028:function(t,e,s){"use strict";s(982)},1088:function(t,e,s){"use strict";s.r(e);var a=s(2),o=s.n(a),n=s(12),i=s(353),r=s(972),l=s(577),c=s(126),d=s(1024),u=s.n(d),h=s(3),p={currentUser:{},shotId:0,showSidebar:!1,showSidebarFromServer:!1,showShareModal:!1,showShotDetailsModal:!1,comments:[],commentsCount:0,commentsTotalPages:1,commentsPage:1,commentsDisabled:!1,commentsLoading:!1,initialCommentsLoaded:!1,isFromModal:!1,isOwnedByCurrentUser:!1,shotUser:{},featureShotUrl:"",shareUtms:{},isLiked:!1,isLikeProcessing:!1,isSaved:!1,boost:{},likesCount:0,shotMediaPreview:{},isBoostCancellationModalVisible:!1,canCurrentUserComment:!1,tags:[],viewsCount:0,savesCount:0,postedOn:"",canCurrentUserRebound:!1,reboundPath:"",shotHasRebounds:!1},m={getShotId:function(t){return t.shotId},getHasComments:function(t){return t.commentsCount>0},getAreCommentsDisabled:function(t){return!t.canCurrentUserComment||t.commentsDisabled},getIsUserLoggedIn:function(t){return t.currentUser&&t.currentUser.isLoggedIn||!1},getCommentsReachedEnd:function(t){return t.commentsPage>t.commentsTotalPages}};function v(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,a)}return s}function f(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?v(Object(s),!0).forEach((function(e){b(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):v(Object(s)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}function b(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}function g(t){return function(t){if(Array.isArray(t))return w(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return w(t,e);var s=Object.prototype.toString.call(t).slice(8,-1);"Object"===s&&t.constructor&&(s=t.constructor.name);if("Map"===s||"Set"===s)return Array.from(t);if("Arguments"===s||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s))return w(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function w(t,e){(null==e||e>t.length)&&(e=t.length);for(var s=0,a=new Array(e);s<e;s++)a[s]=t[s];return a}var y={setState:function(t,e){Object.assign(t,e)},setCurrentUser:function(t,e){t.currentUser=e},setShotId:function(t,e){t.shotId=e},setShowSidebar:function(t,e){t.showSidebar=e},setShowShareModal:function(t,e){t.showShareModal=e},setShowShotDetailsModal:function(t,e){t.showShotDetailsModal=e},setCommentsCount:function(t,e){t.commentsCount=e},setCommentsLoading:function(t,e){t.commentsLoading=e},setInitialCommentsLoaded:function(t,e){t.initialCommentsLoaded=e},setCommentsTotalPages:function(t,e){t.commentsTotalPages=e},setCommentsPage:function(t,e){t.commentsPage=e},setCommentsDisabled:function(t,e){t.commentsDisabled=e},setComments:function(t,e){t.comments=e},addComment:function(t,e){t.comments.unshift(e)},addComments:function(t,e){var s;(s=t.comments).push.apply(s,g(e))},editComment:function(t,e){var s=t.comments.findIndex((function(t){return t.id===e.id})),a=f(f({},t.comments[s]),e);t.comments.splice(s,1,a)},reportComment:function(t,e){var s=e.commentId,a=t.comments.findIndex((function(t){return t.id===s})),o=f(f({},t.comments[a]),{isFlagged:!0});t.comments.splice(a,1,o)},likeCommentToggle:function(t,e){var s=e.commentId,a=e.isLikedByUser,o=e.likeId,n=void 0===o?null:o,i=t.comments.findIndex((function(t){return t.id===s})),r=f(f({},t.comments[i]),{isLikedByUser:a,likeId:n});t.comments.splice(i,1,r)},deleteComment:function(t,e){var s=e.commentId,a=t.comments.findIndex((function(t){return t.id===s}));t.comments.splice(a,1)},setIsFromModal:function(t,e){t.isFromModal=e},setShotUser:function(t,e){t.shotUser=e},setFeatureShotUrl:function(t,e){t.featureShotUrl=e},setIsLiked:function(t,e){t.isLiked=e},setIsLikeProcessing:function(t,e){t.isLikeProcessing=e},setIsSaved:function(t,e){t.isSaved=e},setIsBoostCancellationModalVisible:function(t,e){t.isBoostCancellationModalVisible=e},resetState:function(t){t.shotId=0,t.showSidebar=!1,t.showShareModal=!1,t.comments=[],t.commentsCount=0,t.commentsTotalPages=1,t.commentsPage=1,t.commentsDisabled=!1,t.commentsLoading=!1,t.initialCommentsLoaded=!1,t.isFromModal=!1,t.shotUser={},t.shareUtms={},t.isLiked=!1,t.isLikeProcessing=!1,t.isSaved=!1,t.boost={},t.likesCount=0,t.shotMediaPreview={},t.showSidebarFromServer=!1,t.tags=[],t.viewsCount=0,t.savesCount=0,t.postedOn="",t.canCurrentUserRebound=!1,t.reboundPath="",t.shotHasRebounds=!1}},C=s(6),x=s.n(C),S=s(96),O=s(10),_=s(554),k={setCurrentUser:function(t){var e=t.commit,s=Object(S.a)(["JsConfig"]);s&&s.user&&e("setCurrentUser",s.user)},setState:function(t,e){var s=t.dispatch;(0,t.commit)("setState",e),e.showSidebarFromServer&&0!==e.commentsCount&&s("loadComments")},openShotSidebar:function(t){var e=t.state,s=t.dispatch;(0,t.commit)("setShowSidebar",!0),s("syncSidebarState"),e.comments.length||s("loadComments")},closeShotSidebar:function(t){var e=t.commit,s=t.dispatch;e("setShowSidebar",!1),s("syncSidebarState")},syncSidebarState:function(t){var e=t.state;Object(_.b)("comments_sidebar_open",e.showSidebar)},loadComments:function(t){var e=t.state,s=t.commit,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"recent";return s("setCommentsLoading",!0),new Promise((function(t,o){var n="/shots/".concat(e.shotId,"/comments"),i=e.commentsPage,r={page:i,sort:a,format:"json"};x.a.get(n,{params:r},Object(O.axiosOptions)()).then((function(e){if(e&&e.data){var a=e.data,o=a.comments,n=a.commentsCount,r=a.disabled,l=a.totalPages;1===i?(s("setComments",o),s("setCommentsCount",n),s("setCommentsDisabled",r),s("setCommentsTotalPages",l),s("setInitialCommentsLoaded",!0)):s("addComments",o),s("setCommentsLoading",!1),s("setCommentsPage",i+1),t()}})).catch((function(t){s("setCommentsLoading",!1),o(t)}))}))},addComment:function(t,e){var s=t.state,a=t.commit,o=e.text,n=e.mentionedUsers;return new Promise((function(t,e){var i="/shots/".concat(s.shotId,"/comments");x.a.post(i,{text:o,mentionedUsers:n},Object(O.axiosOptions)()).then((function(e){e&&e.data&&(a("addComment",e.data),a("setCommentsCount",s.commentsCount+1),Dribbble.Itly.trackShotEvent({eventName:"shotCommented",shotId:s.shotId,additionalData:{comment:o}}),t(e.data))})).catch((function(t){Dribbble.EventBus.$emit("siteNotifications:add",{title:"Error posting feedback",message:"Please try again later",id:"comment-add-error-".concat((new Date).getTime())}),e(t)}))}))},editComment:function(t,e){var s=t.state,a=t.commit,o=e.commentId,n=e.text,i=e.mentionedUsers;return new Promise((function(t,e){var r="/shots/".concat(s.shotId,"/comments/").concat(o);x.a.put(r,{text:n,mentionedUsers:i},Object(O.axiosOptions)()).then((function(e){e&&e.data&&(a("editComment",e.data),t())})).catch((function(t){Dribbble.EventBus.$emit("siteNotifications:add",{title:"Error editing feedback",message:"Please try again later",id:"comment-edit-error-".concat((new Date).getTime())}),e(t)}))}))},deleteComment:function(t,e){var s=t.state,a=t.commit,o=e.commentId;return new Promise((function(t,e){var n="/shots/".concat(s.shotId,"/comments/").concat(o);x.a.delete(n,{headers:Object(O.axiosOptions)().headers}).then((function(){a("deleteComment",{commentId:o}),a("setCommentsCount",s.commentsCount-1),t()})).catch((function(t){Dribbble.EventBus.$emit("siteNotifications:add",{title:"Error deleting feedback",message:"Please try again later",id:"comment-delete-error-".concat((new Date).getTime())}),e(t)}))}))},reportComment:function(t,e){var s=t.commit,a=e.commentId;return new Promise((function(t,e){var o="/comments/".concat(a,"/flags");x.a.post(o,{},Object(O.axiosOptions)()).then((function(){s("reportComment",{commentId:a}),Dribbble.EventBus.$emit("siteNotifications:add",{title:"Comment Reported",id:"comment-report",type:"success"}),t()})).catch((function(t){Dribbble.EventBus.$emit("siteNotifications:add",{title:"Error reporting feedback",message:"Please try again later",id:"comment-report-error"}),e(t)}))}))},likeComment:function(t,e){var s=t.commit,a=e.commentId,o="/comments/".concat(a,"/likes");x.a.post(o,{},Object(O.axiosOptions)()).then((function(t){if(t&&t.data){var e=t.data.likeId;s("likeCommentToggle",{isLikedByUser:!0,commentId:a,likeId:e})}})).catch((function(){Dribbble.EventBus.$emit("siteNotifications:add",{title:"Error liking comment!",message:"Please try again later",id:"comment-like-error"})}))},unLikeComment:function(t,e){var s=t.commit,a=e.commentId,o=e.likeId,n="/comments/".concat(a,"/likes/").concat(o);x.a.delete(n,{headers:Object(O.axiosOptions)().headers}).then((function(){s("likeCommentToggle",{isLikedByUser:!1,likeId:null,commentId:a})})).catch((function(){Dribbble.EventBus.$emit("siteNotifications:add",{title:"Error unliking comment!",message:"Please try again later",id:"uncomment-like-error"})}))},unReboundComment:function(t,e){var s=t.commit,a=t.state,o=e.commentId,n="/shots/".concat(a.shotId,"/rebounds/").concat(o);x.a.delete(n,{headers:Object(O.axiosOptions)().headers}).then((function(){s("deleteComment",{commentId:o}),s("setCommentsCount",a.commentsCount-1),Dribbble.EventBus.$emit("siteNotifications:add",{title:"Rebound unlinked",message:"",type:"success",id:"comment-unlinking-rebound-success-".concat((new Date).getTime())})})).catch((function(){Dribbble.EventBus.$emit("siteNotifications:add",{title:"Error unlinking rebound",message:"Please try again later",id:"comment-unlinking-rebound-error-".concat((new Date).getTime())})}))},openBoostedShotPurchaseModal:function(t,e){var s=t.state;e.preventDefault();var a={shotId:s.shotId,mediaType:s.shotMediaPreview.mediaType,shotImageUrl:s.shotMediaPreview.shotImageUrl,shotGifUrl:s.shotMediaPreview.shotGifUrl,shotVideoUrl:s.shotMediaPreview.shotVideoUrl,userDisplayName:s.currentUser.name,userAvatar:s.currentUser.avatarUrl,commentsCount:s.commentsCount,likesCount:s.likesCount};Dribbble.EventBus.$emit("openBoostedShotPurchaseModal",{shotData:a,isTriggeredByAdmin:!1,shouldShowSelectShotStep:!1,entryPoint:"Own shot details page",entryCtaText:"Boost shot icon",referrer:e.currentTarget.dataset.referrer})}};n.default.use(h.b);var j=new h.b.Store({state:p,getters:m,mutations:y,actions:k});function D(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,a)}return s}function B(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?D(Object(s),!0).forEach((function(e){M(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):D(Object(s)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}function M(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}var L={name:"ShotSidebar",components:{ShotSidebarContent:function(){return Promise.all([s.e(129),s.e(102)]).then(s.bind(null,1089))}},data:function(){return{showContent:!1}},computed:B(B(B({},Object(h.f)(["showSidebar","commentsLoading","showSidebarFromServer"])),Object(h.d)(["getHasComments","getCommentsReachedEnd"])),{},{infiniteScrollEnabled:function(){return this.getHasComments&&!this.commentsLoading&&this.showSidebar&&!this.getCommentsReachedEnd},shouldShowSidebarContent:function(){return this.showContent||this.showSidebarFromServer}}),methods:B(B({},Object(h.c)(["loadComments","closeShotSidebar"])),{},{onLoadMore:function(){this.infiniteScrollEnabled&&this.loadComments()},closeSidebar:function(){var t=this;setTimeout((function(){t.closeShotSidebar()}),150),this.showContent=!1},onEnterAnimation:function(){var t=this;document.body.classList.add("shot-sidebar-open"),document.querySelector(".js-media-content").classList.add("sidebar-open"),Dribbble.EventBus.$emit("shot-sidebar:opening");var e=document.querySelector(".js-shot-overlay");e&&Dribbble.isMobile()&&e.scrollTo(0,0),setTimeout((function(){t.showContent=!0}),20)},onLeaveAnimation:function(){Dribbble.EventBus.$emit("shot-sidebar:closing"),document.body.classList.remove("shot-sidebar-open"),document.querySelector(".js-media-content").classList.remove("sidebar-open")},onAfterEnterAnimation:function(){Dribbble.EventBus.$emit("shot-sidebar:opened")},onAfterLeaveAnimation:function(){Dribbble.EventBus.$emit("shot-sidebar:closed")}})},A=(s(1025),s(0)),z=Object(A.a)(L,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("transition",{attrs:{name:"slide-sidebar"},on:{enter:t.onEnterAnimation,leave:t.onLeaveAnimation,"after-enter":t.onAfterEnterAnimation,"after-leave":t.onAfterLeaveAnimation}},[s("div",{directives:[{name:"show",rawName:"v-show",value:t.showSidebar,expression:"showSidebar"}],staticClass:"shot-sidebar",class:{"sidebar-open":t.showSidebar}},[s("div",{staticClass:"shot-sidebar-content"},[s("transition",{attrs:{name:"show-close"}},[t.shouldShowSidebarContent?s("a",{staticClass:"close-sidebar",attrs:{href:"#"},on:{click:function(e){return e.preventDefault(),t.closeSidebar.apply(null,arguments)}}},[s("svg",{class:"close-icon-desktop",attrs:{viewBox:"0 0 22 22",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M7.229 5.815a1 1 0 00-1.415 1.414L9.586 11l-3.772 3.771a1 1 0 101.415 1.414l3.77-3.77 3.772 3.77a1 1 0 101.414-1.414L12.414 11l3.771-3.771a1 1 0 00-1.414-1.414L11 9.585l-3.771-3.77z",fill:"currentColor"}})]),t._v(" "),s("svg",{class:"close-icon-mobile fill-current",attrs:{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M2.751 19.526c-.63.632-1.65.632-2.279 0a1.62 1.62 0 010-2.286L7.688 10 .472 2.76a1.62 1.62 0 010-2.286 1.608 1.608 0 012.28 0l8.331 8.36.024.022c.206.208.345.457.416.721l.005.02a1.62 1.62 0 01-.452 1.577L2.75 19.526z",fill:"currentColor"}})])]):t._e()]),t._v(" "),s("transition",{attrs:{name:"slide-sidebar-content"}},[s("div",{directives:[{name:"show",rawName:"v-show",value:t.shouldShowSidebarContent,expression:"shouldShowSidebarContent"},{name:"infinite-scroll",rawName:"v-infinite-scroll",value:t.onLoadMore,expression:"onLoadMore"}],staticClass:"sidebar-scrolling-container",attrs:{"infinite-scroll-disabled":t.infiniteScrollEnabled,"infinite-scroll-distance":10,"infinite-scroll-immediate-check":!1,"infinite-scroll-listen-for-event":"infiniteScroll"}},[t.showSidebar?s("shot-sidebar-content",{attrs:{"is-loading-comments":t.commentsLoading}}):t._e()],1)])],1)])])}),[],!1,null,"77407238",null).exports,P=s(952);function E(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,a)}return s}function I(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}var U={name:"ActionButtonComments",props:{commentsDisabled:{type:Boolean,required:!0},commentsCount:{type:Number,required:!0}},methods:function(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?E(Object(s),!0).forEach((function(e){I(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):E(Object(s)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}({},Object(h.c)(["openShotSidebar"]))},T=Object(A.a)(U,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("button",{staticClass:"btn-icon tertiary with-indicator comments-action",on:{click:function(e){return e.preventDefault(),t.openShotSidebar.apply(null,arguments)}}},[s("svg",{class:"icon-16",attrs:{viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M10 0H6a5.998 5.998 0 00-1.333 11.846V16l4.666-4H10a6 6 0 100-12z",fill:"currentColor"}})]),t._v(" "),s("span",{staticClass:"accessibility-text"},[t._v("\n    View comments\n  ")]),t._v(" "),t.commentsCount?s("span",{staticClass:"count-indicator"},[t._v("\n    "+t._s(t.commentsCount)+"\n  ")]):t._e()])}),[],!1,null,null,null).exports,q=s(975),H=s(976),F=s(977),V=s(978);function N(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,a)}return s}function R(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}var $={name:"ActionButtonBoost",methods:function(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?N(Object(s),!0).forEach((function(e){R(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):N(Object(s)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}({},Object(h.c)(["openBoostedShotPurchaseModal"]))},G=Object(A.a)($,(function(){var t=this.$createElement,e=this._self._c||t;return e("button",{staticClass:"btn-icon tertiary js-boost-shot-button",attrs:{"data-referrer":"ShotSidebar"},on:{click:this.openBoostedShotPurchaseModal}},[e("svg",{class:"icon-16",attrs:{width:"17",height:"16",viewBox:"0 0 17 16",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[e("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M9.665 3.702c0 .572.464 1.036 1.036 1.036h2.263L8.448 9.254a.622.622 0 01-.88 0L6.43 8.114a1.867 1.867 0 00-2.64 0L.803 11.1a1.036 1.036 0 001.465 1.464l2.4-2.401a.622.622 0 01.88 0l1.14 1.14c.73.728 1.911.728 2.64 0l5.1-5.1v2.263a1.036 1.036 0 102.072 0V3.289a.622.622 0 00-.622-.623H10.7c-.572 0-1.036.464-1.036 1.036z"}})])])}),[],!1,null,null,null).exports;function Y(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,a)}return s}function Z(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?Y(Object(s),!0).forEach((function(e){J(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):Y(Object(s)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}function J(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}var Q={name:"ActionButtonBoosted",components:{ProgressRing:s(568).a},computed:Z({},Object(h.f)(["boost"])),methods:Z({},Object(h.c)(["openBoostedShotPurchaseModal"]))},W=Object(A.a)(Q,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("button",{staticClass:"btn-icon tertiary btn-custom-icon-size",class:{"js-boost-shot-button":"rejected"===t.boost.status},attrs:{"data-referrer":"rejected"===t.boost.status?"ShotSidebar":""},on:{click:function(e){"rejected"===t.boost.status&&t.openBoostedShotPurchaseModal(e)}}},["approved"===t.boost.status?s("div",{staticClass:"shot-action-button-boost-approved display-flex"},[s("progress-ring",{attrs:{"stroke-color":"#77E0B5",radius:14,progress:t.boost.impressionsCount/t.boost.totalImpressions*100,stroke:2,"has-base-circle":!0,"rotate-degrees":270}}),t._v(" "),s("svg",{class:"boost-approved-icon icon-12",attrs:{width:"17",height:"16",viewBox:"0 0 17 16",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M9.665 3.702c0 .572.464 1.036 1.036 1.036h2.263L8.448 9.254a.622.622 0 01-.88 0L6.43 8.114a1.867 1.867 0 00-2.64 0L.803 11.1a1.036 1.036 0 001.465 1.464l2.4-2.401a.622.622 0 01.88 0l1.14 1.14c.73.728 1.911.728 2.64 0l5.1-5.1v2.263a1.036 1.036 0 102.072 0V3.289a.622.622 0 00-.622-.623H10.7c-.572 0-1.036.464-1.036 1.036z"}})])],1):t._e(),t._v(" "),"pending"===t.boost.status?s("div",{staticClass:"color-deep-blue-sea-light-60 display-flex"},[s("svg",{class:"icon-16",attrs:{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M.001 12c0-6.627 5.373-12 12-12s12 5.373 12 12-5.373 12-12 12-12-5.373-12-12zm5 0a1.5 1.5 0 103.001-.001A1.5 1.5 0 005.001 12zm5.5 0a1.5 1.5 0 103.001-.001 1.5 1.5 0 00-3.001.001zm7 1.5a1.5 1.5 0 11.001-3.001 1.5 1.5 0 01-.001 3.001z",fill:"currentColor"}})])]):t._e(),t._v(" "),"rejected"===t.boost.status?s("div",{staticClass:"color-lobster display-flex"},[s("svg",{class:"icon-16 fill-current",attrs:{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M23.512 17.5L15.178 2.852c-1.405-2.47-4.951-2.47-6.356 0L.488 17.5C-.907 19.951.855 23 3.666 23h16.668c2.811 0 4.573-3.049 3.178-5.5zM11 6.999a1 1 0 012 0V14a1 1 0 01-2 0zM12 20a1.5 1.5 0 11.001-3.001A1.5 1.5 0 0112 20z"}})])]):t._e()])}),[],!1,null,null,null).exports,X=s(46);function K(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,a)}return s}function tt(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?K(Object(s),!0).forEach((function(e){et(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):K(Object(s)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}function et(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}var st={name:"BoostedHoverCard",computed:tt(tt({},Object(h.f)(["boost"])),{},{numberOfImpressions:function(){return Object(X.b)("impression",this.boost.impressionsCount)},numberOfClicks:function(){return Object(X.b)("click",this.boost.clicksCount)}}),methods:tt(tt(tt({},Object(h.e)(["setIsBoostCancellationModalVisible"])),Object(h.c)(["openBoostedShotPurchaseModal"])),{},{onClickCancelBoost:function(){this.setIsBoostCancellationModalVisible(!0)}})},at=Object(A.a)(st,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",["approved"===t.boost.status?s("div",[s("div",{staticClass:"flex justify-space-between color-seafoam-dream"},[s("div",{staticClass:"font-sublabel-bold color-deep-blue-sea"},[t._v("\n        Boosted stats\n      ")]),t._v(" "),s("svg",{class:"icon-20",attrs:{width:"20",height:"21",viewBox:"0 0 20 21",fill:"none",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M10 20.27c5.523 0 10-4.478 10-10 0-5.523-4.477-10-10-10s-10 4.477-10 10c0 5.522 4.477 10 10 10zm1.617-12.114a.693.693 0 010-1.386h3.467c.23 0 .416.186.416.416v3.466a.693.693 0 11-1.386 0V9.137l-3.415 3.415a1.25 1.25 0 01-1.768 0l-.763-.763a.417.417 0 00-.59 0l-1.607 1.607a.693.693 0 11-.98-.98l1.999-1.999a1.25 1.25 0 011.767 0l.763.763a.417.417 0 00.59 0l3.023-3.024h-1.515z",fill:"currentColor"}})])]),t._v(" "),s("div",{staticClass:"margin-t-8 flex align-center font-body-small color-deep-blue-sea-light-20"},[s("svg",{class:"icon-16 margin-r-8 fill-current",attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 14 12",fill:"none","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M7 3.958a2.042 2.042 0 100 4.084 2.042 2.042 0 000-4.084zM7 .75C3.134.75 0 5.125 0 6s3.134 5.25 7 5.25S14 6.875 14 6 10.866.75 7 .75zm0 8.458a3.208 3.208 0 110-6.416 3.208 3.208 0 010 6.416z",fill:"#9E9EA7"}})]),t._v("\n      "+t._s(t.numberOfImpressions)+"\n    ")]),t._v(" "),s("div",{staticClass:"margin-t-8 flex align-center font-body-small color-deep-blue-sea-light-20"},[s("svg",{class:"icon-16 margin-r-8 fill-current",attrs:{width:"17",height:"16",viewBox:"0 0 17 16",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M9.202.156c.465.04.81.45.769.916l-.148 1.685a.846.846 0 01-1.685-.147L8.285.925a.846.846 0 01.917-.77zm-4.419 6.83a.846.846 0 01-.916.77l-1.685-.148a.846.846 0 11.147-1.686l1.685.148c.466.***********.916zM6.13 4.402a.846.846 0 01-1.192-.104L3.85 3.002a.846.846 0 011.295-1.087L6.233 3.21c.3.357.254.891-.104 1.191zm5.362.47a.846.846 0 01.105-1.192l1.296-1.088a.846.846 0 111.087 1.296l-1.296 1.088a.846.846 0 01-1.192-.105zm-8.319 5.876a.846.846 0 001.088 1.296l1.295-1.088A.846.846 0 004.468 9.66l-1.296 1.088zm4.472-4.305a.5.5 0 01.744-.534l7.894 4.668a.5.5 0 01-.127.914l-3.423.909a.5.5 0 00-.286.203l-2.01 2.97a.5.5 0 01-.903-.176l-1.89-8.954z"}})]),t._v("\n      "+t._s(t.numberOfClicks)+"\n    ")]),t._v(" "),s("div",{staticClass:"color-deep-blue-sea-light-60 margin-t-8 font-body-small"},[t._v("\n      All stats attributed to boosting\n    ")])]):t._e(),t._v(" "),"pending"===t.boost.status?s("div",[s("div",{staticClass:"flex justify-space-between"},[s("div",{staticClass:"font-sublabel-bold color-deep-blue-sea"},[t._v("\n        Awaiting approval\n      ")]),t._v(" "),s("div",{staticClass:"color-deep-blue-sea-light-60"},[s("svg",{class:"icon-24",attrs:{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M.001 12c0-6.627 5.373-12 12-12s12 5.373 12 12-5.373 12-12 12-12-5.373-12-12zm5 0a1.5 1.5 0 103.001-.001A1.5 1.5 0 005.001 12zm5.5 0a1.5 1.5 0 103.001-.001 1.5 1.5 0 00-3.001.001zm7 1.5a1.5 1.5 0 11.001-3.001 1.5 1.5 0 01-.001 3.001z",fill:"currentColor"}})])])]),t._v(" "),s("div",{staticClass:"margin-t-8 font-body-small color-deep-blue-sea-light-20"},[t._v("\n      Your shot is queued for review. You will be notified as soon as it\u2019s approved.\n    ")]),t._v(" "),s("button",{staticClass:"form-btn margin-t-16",on:{click:t.onClickCancelBoost}},[t._v("\n      Cancel boost\n    ")])]):t._e(),t._v(" "),"rejected"===t.boost.status?s("div",[s("div",{staticClass:"flex justify-space-between"},[s("div",{staticClass:"font-sublabel-bold color-deep-blue-sea"},[t._v("\n        Boost denied\n      ")]),t._v(" "),s("div",{staticClass:"color-lobster"},[s("svg",{class:"icon-24 fill-current",attrs:{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M23.512 17.5L15.178 2.852c-1.405-2.47-4.951-2.47-6.356 0L.488 17.5C-.907 19.951.855 23 3.666 23h16.668c2.811 0 4.573-3.049 3.178-5.5zM11 6.999a1 1 0 012 0V14a1 1 0 01-2 0zM12 20a1.5 1.5 0 11.001-3.001A1.5 1.5 0 0112 20z"}})])])]),t._v(" "),s("div",{staticClass:"margin-t-8 font-body-small color-deep-blue-sea-light-20"},["general"===t.boost.statusReason?s("span",[t._v("\n        Your shot boost was denied. You can resubmit your your shot boosting below.\n      ")]):s("span",[t._v("Your shot boost was denied because "+t._s(t.boost.statusReasonSentence)+". Fix the issue by boosting below.")])]),t._v(" "),s("a",{staticClass:"margin-t-16 display-flex align-center",attrs:{href:"#"},on:{click:t.openBoostedShotPurchaseModal}},[s("span",{staticClass:"color-deep-blue-sea-light-60 margin-r-8"},[s("svg",{class:"icon-16 fill-current",attrs:{width:"17",height:"16",viewBox:"0 0 17 16",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M9.665 3.702c0 .572.464 1.036 1.036 1.036h2.263L8.448 9.254a.622.622 0 01-.88 0L6.43 8.114a1.867 1.867 0 00-2.64 0L.803 11.1a1.036 1.036 0 001.465 1.464l2.4-2.401a.622.622 0 01.88 0l1.14 1.14c.73.728 1.911.728 2.64 0l5.1-5.1v2.263a1.036 1.036 0 102.072 0V3.289a.622.622 0 00-.622-.623H10.7c-.572 0-1.036.464-1.036 1.036z"}})])]),t._v(" "),s("span",{staticClass:"color-deep-blue-sea-light-60 font-body"},[t._v("Boost Shot")])])]):t._e()])}),[],!1,null,null,null).exports;function ot(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,a)}return s}function nt(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?ot(Object(s),!0).forEach((function(e){it(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):ot(Object(s)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}function it(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}var rt={name:"ShotActionsToolbar",components:{ActionButtonComments:T,ActionButtonShare:q.a,ActionButtonShotDetails:H.a,ActionButtonCollection:F.a,ActionButtonLike:V.a,ActionButtonBoost:G,ActionButtonBoosted:W,Avatar:P.a,BoostedHoverCard:at},data:function(){return{buttonsTippyOptions:{placement:"left",distance:16,arrow:!0,arrowTransform:"scale(0.75)"},scrollingEl:null,showOnScroll:!1}},computed:nt(nt({},Object(h.f)(["shotId","shotUser","isFromModal","showSidebar","commentsCount","commentsDisabled","isLiked","isSaved","isLikeProcessing","boost","isOwnedByCurrentUser","isMultiShot"])),{},{isToolbarVisible:function(){return"small"===this.$mq||!this.showSidebar},canUserBoostShot:function(){return!this.boost.hasBoostedShots||this.boost.hasBoostedShots&&!["pending","approved","rejected"].includes(this.boost.status)},editPath:function(){return this.isMultiShot?"/uploads/".concat(this.shotId,"/edit"):"/shots/".concat(this.shotId,"/edit")}}),watch:{$mq:function(t){"small"===t&&this.scrollingEl&&this.removeCustomEvents(),"small"===t||this.scrollingEl||this.addCustomEvents()}},created:function(){this.addCustomEvents()},mounted:function(){"small"!==this.$mq&&this.checkShowOnScroll()},destroyed:function(){this.removeCustomEvents()},methods:{addCustomEvents:function(){"small"!==this.$mq&&(this.scrollingEl=this.isFromModal?document.querySelector(".js-shot-overlay .overlay-content"):window,this.scrollingEl.addEventListener("scroll",this.handleScroll))},removeCustomEvents:function(){this.scrollingEl.removeEventListener("scroll",this.handleScroll),this.scrollingEl=null},handleScroll:function(){this.checkShowOnScroll()},checkShowOnScroll:function(){var t=document.querySelector(".js-shot-header").getBoundingClientRect().bottom;this.showOnScroll=t<=0}}};s(1027);function lt(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,a)}return s}function ct(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?lt(Object(s),!0).forEach((function(e){dt(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):lt(Object(s)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}function dt(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}var ut={name:"ShotSidebarApp",components:{ShotSidebar:z,ShotActionsToolbar:Object(A.a)(rt,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"shot-actions-toolbar-wrapper"},[s("transition",{attrs:{name:"action-toolbar"}},[t.isToolbarVisible?s("div",{staticClass:"shot-actions-toolbar"},[s("designer-hover-card",{attrs:{id:"shot-actions-toolbar-avatar",username:t.shotUser.username,"update-shot-header":!0}},[s("avatar",{attrs:{"user-id":t.shotUser.id,"image-url":t.shotUser.avatarUrl,path:t.shotUser.url,name:t.shotUser.name,size:"medium"}})],1),t._v(" "),s("transition-group",{staticClass:"shot-actions-toolbar-buttons",attrs:{name:"button-group",tag:"div"}},[s("action-button-comments",{directives:[{name:"tippy",rawName:"v-tippy",value:t.buttonsTippyOptions,expression:"buttonsTippyOptions"}],key:"actionButtonComments",attrs:{title:"Feedback","comments-disabled":t.commentsDisabled,"comments-count":t.commentsCount}}),t._v(" "),s("action-button-share",{directives:[{name:"tippy",rawName:"v-tippy",value:t.buttonsTippyOptions,expression:"buttonsTippyOptions"}],key:"actionButtonShare",attrs:{title:"Share"}}),t._v(" "),s("action-button-shot-details",{directives:[{name:"tippy",rawName:"v-tippy",value:t.buttonsTippyOptions,expression:"buttonsTippyOptions"}],key:"actionButtonShotDetails",attrs:{title:"Shot details"}}),t._v(" "),t.showOnScroll?s("action-button-collection",{directives:[{name:"tippy",rawName:"v-tippy",value:t.buttonsTippyOptions,expression:"buttonsTippyOptions"}],key:"actionButtonCollection",attrs:{title:"Save","is-saved":t.isSaved}}):t._e(),t._v(" "),t.showOnScroll?s("action-button-like",{directives:[{name:"tippy",rawName:"v-tippy",value:t.buttonsTippyOptions,expression:"buttonsTippyOptions"}],key:"actionButtonLike",attrs:{title:t.isLiked?"Unlike":"Like","is-liked":t.isLiked,"is-like-processing":t.isLikeProcessing}}):t._e(),t._v(" "),t.isOwnedByCurrentUser?[s("div",{key:"actionButtonsDivider",staticClass:"action-buttons-divider"}),t._v(" "),s("a",{directives:[{name:"tippy",rawName:"v-tippy",value:t.buttonsTippyOptions,expression:"buttonsTippyOptions"}],key:"actionButtonEdit",staticClass:"btn-icon tertiary",attrs:{href:t.editPath,title:"Edit"}},[s("svg",{attrs:{viewBox:"0 0 24 24",width:"24",height:"24","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M2.526 15.046L0 24l8.954-2.526 10.035-10.035-6.428-6.428zM22.668 1.332A4.512 4.512 0 0019.454 0a4.513 4.513 0 00-3.214 1.332l-2.262 2.262 6.428 6.428 2.262-2.262c.859-.859 1.332-2 1.332-3.214a4.512 4.512 0 00-1.332-3.214z"}})])]),t._v(" "),t.canUserBoostShot?s("action-button-boost",{directives:[{name:"tippy",rawName:"v-tippy",value:t.buttonsTippyOptions,expression:"buttonsTippyOptions"}],key:"actionButtonBoost",attrs:{title:"Promote this shot"}}):s("action-button-boosted",{directives:[{name:"tippy",rawName:"v-tippy",value:{html:"#shot-actions-toolbar-boosted",reactive:!0,interactive:!0,theme:"hover-card",distance:16,delay:[200,null],placement:"left-start"},expression:"{\n              html: '#shot-actions-toolbar-boosted',\n              reactive: true,\n              interactive: true,\n              theme: 'hover-card',\n              distance: 16,\n              delay: [200, null],\n              placement: 'left-start'\n            }"}],key:"actionButtonBoosted"})]:t._e()],2),t._v(" "),t.boost.canBoostShot?s("boosted-hover-card",{attrs:{id:"shot-actions-toolbar-boosted"}}):t._e()],1):t._e()])],1)}),[],!1,null,"553ba33e",null).exports},props:{shotData:{type:Object,required:!0}},computed:ct({},Object(h.f)(["showSidebar"])),mounted:function(){var t=this;Dribbble.EventBus.$on("shot-overlay:navigate",(function(){t.destroyApp()})),Dribbble.EventBus.$on("shot-overlay:hide",(function(){t.destroyApp()})),Dribbble.EventBus.$on("shotLike:processing",(function(){t.setIsLikeProcessing(!0)})),Dribbble.EventBus.$on("shotLike:done",(function(e){var s=e.isLike;t.setIsLikeProcessing(!1),t.setIsLiked(s)})),Dribbble.EventBus.$on("shotCollection:done",(function(e){var s=e.isSaved;t.setIsSaved(s)})),Dribbble.EventBus.$on("shot-sidebar:show",(function(){t.openShotSidebar()})),Dribbble.EventBus.$on("shot-sidebar:hide",(function(){t.closeShotSidebar()}))},created:function(){if(this.setCurrentUser(),this.shotData.showSidebarFromServer){var t="small"!==this.$mq;this.shotData.showSidebarFromServer=t,this.shotData.showSidebar=t}this.setState(this.shotData)},beforeDestroy:function(){Dribbble.EventBus.$off("shot-overlay:navigate"),Dribbble.EventBus.$off("shot-overlay:hide"),Dribbble.EventBus.$off("shotLike:processing"),Dribbble.EventBus.$off("shotLike:done"),Dribbble.EventBus.$off("shotCollection:done"),Dribbble.EventBus.$off("shot-sidebar:show"),Dribbble.EventBus.$off("shot-sidebar:hide")},methods:ct(ct(ct({},Object(h.c)(["setState","setCurrentUser","openShotSidebar","closeShotSidebar"])),Object(h.e)(["resetState","setIsLiked","setIsLikeProcessing","setIsSaved"])),{},{destroyApp:function(){this.showSidebar&&document.body.classList.remove("shot-sidebar-open"),this.resetState(),this.$destroy()}})},ht=(s(1028),Object(A.a)(ut,(function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"shot-sidebar-app",attrs:{id:"shot-sidebar-app"}},["small"!==this.$mq?e("shot-actions-toolbar"):this._e(),this._v(" "),e("shot-sidebar")],1)}),[],!1,null,"4d07bdbe",null).exports);function pt(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,a)}return s}function mt(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?pt(Object(s),!0).forEach((function(e){vt(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):pt(Object(s)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}function vt(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}var ft={name:"ShotActionsMobileApp",components:{ActionButtonComments:T,ActionButtonShare:q.a,ActionButtonBoost:G,ActionButtonBoosted:W,BoostedHoverCard:at,ActionButtonLike:V.a,ActionButtonCollection:F.a,ActionButtonShotDetails:H.a},props:{shotData:{type:Object,required:!0}},computed:mt(mt({},Object(h.f)(["showSidebar","commentsCount","commentsDisabled","boost","isOwnedByCurrentUser","isLiked","isSaved","isLikeProcessing"])),{},{canUserBoostShot:function(){return!this.boost.hasBoostedShots||this.boost.hasBoostedShots&&!["pending","approved","rejected"].includes(this.boost.status)}}),mounted:function(){var t=this;Dribbble.EventBus.$on("shot-overlay:navigate",(function(){t.destroyApp()})),Dribbble.EventBus.$on("shot-overlay:hide",(function(){t.destroyApp()}))},beforeDestroy:function(){Dribbble.EventBus.$off("shot-overlay:navigate"),Dribbble.EventBus.$off("shot-overlay:hide")},methods:{destroyApp:function(){this.$destroy()}}},bt=Object(A.a)(ft,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return"small"===t.$mq?s("div",{staticClass:"shot-actions-mobile-app",attrs:{id:"shot-actions-mobile-app"}},[s("div",{staticClass:"display-flex"},[s("action-button-collection",{attrs:{"is-saved":t.isSaved}}),t._v(" "),s("action-button-like",{attrs:{"is-liked":t.isLiked,"is-like-processing":t.isLikeProcessing}})],1),t._v(" "),s("div",{staticClass:"display-flex"},[s("action-button-share"),t._v(" "),s("action-button-comments",{attrs:{"comments-disabled":t.commentsDisabled,"comments-count":t.commentsCount}}),t._v(" "),s("action-button-shot-details"),t._v(" "),t.isOwnedByCurrentUser?s("div",{staticClass:"boost-buttons"},[t.canUserBoostShot?s("action-button-boost"):s("action-button-boosted",{directives:[{name:"tippy",rawName:"v-tippy",value:{html:"#shot-actions-toolbar-boosted",reactive:!0,interactive:!0,theme:"hover-card-dynamic-width",distance:16,delay:[200,null],placement:"bottom-end",maxWidth:"320px"},expression:"{\n          html: '#shot-actions-toolbar-boosted',\n          reactive: true,\n          interactive: true,\n          theme: 'hover-card-dynamic-width',\n          distance: 16,\n          delay: [200, null],\n          placement: 'bottom-end',\n          maxWidth: '320px'\n        }"}]})],1):t._e(),t._v(" "),t.boost.canBoostShot?s("boosted-hover-card",{attrs:{id:"shot-actions-toolbar-boosted"}}):t._e()],1)]):t._e()}),[],!1,null,null,null).exports,gt=s(973),wt=s(974),yt=s(82),Ct=s(567);function xt(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,a)}return s}function St(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?xt(Object(s),!0).forEach((function(e){Ot(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):xt(Object(s)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}function Ot(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}var _t={name:"BoostCancellationModal",components:{Modal:yt.a,DribbbleLoader:Ct.a},data:function(){return{isCancelBoostLoading:!1}},computed:St({},Object(h.f)(["isBoostCancellationModalVisible","boost"])),methods:St(St({},Object(h.e)(["setIsBoostCancellationModalVisible"])),{},{onCloseModal:function(){this.setIsBoostCancellationModalVisible(!1)},onConfirmCancel:function(){var t=this;this.isCancelBoostLoading=!0,x.a.put("/client_app/screenshot_boosts/".concat(this.boost.id,"/cancel"),{},Object(O.axiosOptions)()).then((function(){window.location.reload()})).catch((function(){Dribbble.EventBus.$emit("siteNotifications:add",{title:"Sorry, there was a problem cancelling your boosted shot.",id:"cancel-boosted-shot-notification",isCloseable:!0,willAutoClose:!0,autoCloseDuration:5e3}),t.isCancelBoostLoading=!1}))}})};function kt(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,a)}return s}function jt(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?kt(Object(s),!0).forEach((function(e){Dt(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):kt(Object(s)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}function Dt(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}var Bt={name:"ShotModalsApp",components:{BoostCancellationModal:Object(A.a)(_t,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("modal",{attrs:{id:"cancel-boost-confirmation-modal","show-modal":t.isBoostCancellationModalVisible,"max-width":"600px","should-close-on-esc":""},on:{"modal-close":t.onCloseModal}},[s("h4",[t._v("Are you sure you want to cancel this boost?")]),t._v(" "),s("div",{staticClass:"margin-t-16 font-body"},[t._v("\n    By cancelling this boost your shot will be taken out of the approval queue and you will not be charged. You can boost this shot again at anytime.\n  ")]),t._v(" "),s("div",{staticClass:"margin-t-24"},[s("button",{staticClass:"form-sub margin-r-8",on:{click:t.onCloseModal}},[t._v("\n      Nevermind\n    ")]),t._v(" "),s("button",{staticClass:"form-btn ",class:{loading:t.isCancelBoostLoading},on:{click:t.onConfirmCancel}},[t.isCancelBoostLoading?s("dribbble-loader"):s("div",[t._v("\n        Yes, I'm sure\n      ")])],1)])])}),[],!1,null,null,null).exports,ShareModal:wt.a,DetailsModal:gt.a},props:{shotData:{type:Object,required:!0}},computed:jt(jt({},Object(h.f)(["showShotDetailsModal","tags","viewsCount","likesCount","savesCount","postedOn","commentsCount","canCurrentUserRebound","reboundPath","shotId","showSidebar","shotHasRebounds","showShareModal","featureShotUrl","shareUtms","shotUser","currentUser"])),{},{isBoostFooterVisible:function(){return(this.currentUser.isPlayer||this.currentUser.hasPro)&&this.shotData.isOwnedByCurrentUser}}),mounted:function(){var t=this;Dribbble.EventBus.$on("shot-overlay:navigate",(function(){t.destroyApp()})),Dribbble.EventBus.$on("shot-overlay:hide",(function(){t.destroyApp()}))},beforeDestroy:function(){Dribbble.EventBus.$off("shot-overlay:navigate"),Dribbble.EventBus.$off("shot-overlay:hide")},methods:jt(jt(jt({},Object(h.c)(["openShotSidebar","openBoostedShotPurchaseModal"])),Object(h.e)(["setShowShotDetailsModal","setShowShareModal"])),{},{destroyApp:function(){this.$destroy()},onClickComments:function(){var t=this;this.setShowShotDetailsModal(!1),this.showSidebar||setTimeout((function(){t.openShotSidebar()}),300)},onClickBoost:function(t){this.setShowShareModal(!1),this.openBoostedShotPurchaseModal(t)}})},Mt=Object(A.a)(Bt,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"js-shot-modal-content",attrs:{id:"shot-modals-app"}},[s("boost-cancellation-modal"),t._v(" "),s("share-modal",{attrs:{subject:"shot","subject-data":t.shotData,"is-modal-open":t.showShareModal,"feature-image-url":t.featureShotUrl,"share-utms":t.shareUtms,"subject-id":t.shotId,"subject-user":t.shotUser,"is-boost-footer-visible":t.isBoostFooterVisible},on:{"close-modal":function(e){return t.setShowShareModal(!1)},"click-boost":t.onClickBoost}}),t._v(" "),s("details-modal",{attrs:{subject:"shot","should-link-tags":"","is-modal-open":t.showShotDetailsModal,tags:t.tags,"views-count":t.viewsCount,"likes-count":t.likesCount,"saves-count":t.savesCount,"posted-on":t.postedOn,"comments-count":t.commentsCount,"can-current-user-rebound":t.canCurrentUserRebound,"rebound-path":t.reboundPath,"subject-id":t.shotId,"does-shot-have-rebounds":t.shotHasRebounds},on:{"close-modal":function(e){return t.setShowShotDetailsModal(!1)},"click-comments":t.onClickComments}})],1)}),[],!1,null,null,null).exports;n.default.use(c.a),n.default.use(i.a,{breakpoints:{small:920,medium:1200,large:1/0},defaultBreakpoint:"small"}),n.default.use(l.a),n.default.use(r.a),n.default.use(u.a);var Lt={initialize:function(t){this.initApps(t)},initApps:function(t){this.shotSidebarApp=new n.default({el:"#shot-sidebar-app",name:"ShotSidebarAppMain",render:function(e){return e(ht,{props:{shotData:t}})},store:j}),this.shotActionsToolbarApp=new n.default({el:"#shot-actions-mobile-app",name:"ShotActionsMobileApp",render:function(e){return e(bt,{props:{shotData:t}})},store:j}),this.shotModalsApp=new n.default({el:"#shot-modals-app",name:"ShotModalsApp",render:function(e){return e(Mt,{props:{shotData:t}})},store:j})}},At=s(393),zt=(s(408),s(155)),Pt=s.n(zt);function Et(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,a)}return s}function It(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?Et(Object(s),!0).forEach((function(e){Ut(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):Et(Object(s)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}function Ut(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}function Tt(t,e,s,a,o,n,i){try{var r=t[n](i),l=r.value}catch(c){return void s(c)}r.done?e(l):Promise.resolve(l).then(a,o)}function qt(t,e){for(var s=0;s<e.length;s++){var a=e[s];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,a.key,a)}}var Ht=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}var e,a,n,i,r;return e=t,a=[{key:"initialize",value:(i=o.a.mark((function e(a){return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.shotData=a,Lt.initialize(this.shotData),!document.querySelector("#ssr-app")){e.next=9;break}return e.next=6,s.e(107).then(s.bind(null,1086));case 6:(0,e.sent.initClientHydration)();case 9:this.similarShotSection=document.querySelector(".js-similar-work"),this.bindEventListeners(),t.handleShotTags(),t.handleGoodCard(),document.querySelectorAll(".js-actions-menu").forEach((function(t){new At.default(t).initialize()})),document.querySelector(".js-homespun-abs-unit")&&this.initializeHomespunAds(),Pt.a.polyfill();case 16:case"end":return e.stop()}}),e,this)})),r=function(){var t=this,e=arguments;return new Promise((function(s,a){var o=i.apply(t,e);function n(t){Tt(o,s,a,n,r,"next",t)}function r(t){Tt(o,s,a,n,r,"throw",t)}n(void 0)}))},function(t){return r.apply(this,arguments)})},{key:"bindEventListeners",value:function(){this.similarShotSection&&this.similarShotSection.addEventListener("lazyincluded",this.onSimilarShotsLoaded.bind(this))}},{key:"initializeHomespunAds",value:function(){var t={impression_id:Dribbble.uuidv4(),placement:this.shotData.isFromModal?"Shot Modal":"Shot Page",provider:"Dribbble",unit_type:"Display"};Dribbble.Itly.adRequested(t);var e=[{id:"hotjar-ad-red-20210801",adLink:"https://www.hotjar.com/?utm_source=Dribbble&utm_medium=cpc&utm_campaign=HJ-DribbbleNative-Worldwide&utm_content=Image-logo-red-202103",imageUrl:"https://cdn.dribbble.com/uploads/20446/original/1dfe7c5658a77e90c9d5111d9f710b94.png?**********"},{id:"hotjar-ad-pink-20210801",adLink:"https://www.hotjar.com/?utm_source=Dribbble&utm_medium=cpc&utm_campaign=HJ-DribbbleNative-Worldwide&utm_content=Image-logo-pink-202103 ",imageUrl:"https://cdn.dribbble.com/uploads/20445/original/6f9d5a96c2f92310517e28983f96090e.png?**********"},{id:"hotjar-ad-purple-20210801",adLink:"https://www.hotjar.com/?utm_source=Dribbble&utm_medium=cpc&utm_campaign=HJ-DribbbleNative-Worldwide&utm_content=Image-logo-purple-202103 ",imageUrl:"https://cdn.dribbble.com/uploads/21795/original/972c0550f3889843617f75d2cf5a9d1c.png?**********"}],s=e[Math.floor(Math.random()*e.length)],a=document.querySelectorAll(".js-abs-link");document.querySelector(".js-abs-image").src=s.imageUrl,a.forEach((function(t){t.href=s.adLink})),document.querySelector(".js-homespun-abs-unit").classList.add("homespun-abs-unit-visible");var o={ad_id:s.id,ad_link:s.adLink,ad_link_type:"Custom URL",ad_text:document.querySelector(".js-abs-text").innerHTML,cta_text:document.querySelector(".js-cta-text").innerHTML,has_cta:!0};Dribbble.Itly.adServed(It(It({},t),o)),Dribbble.Itly.adImpressionViewed(It(It({},t),o)),a.forEach((function(e){e.addEventListener("click",(function(){Dribbble.Itly.adClicked(It(It({},t),o))}))}))}},{key:"onSimilarShotsLoaded",value:function(){this.shotData.shouldDisplayAds&&Dribbble.AdNetworks.Boost.load({context:"shot",placement:Dribbble.JsConfig.screenshot_boost.targetPlacements.similar_work,requestId:Dribbble.uuidv4()})}}],n=[{key:"handleShotTags",value:function(){var t=document.querySelector(".js-shot-tags-expanding-wrapper"),e=document.querySelector(".js-shot-tags-container"),s=document.querySelector(".js-expand-tags-button");e&&e.clientHeight>86&&(t.classList.add("shot-tags-expanding-wrapper"),s.classList.add("expand-tags-button-visible")),s&&s.addEventListener("click",(function(){t.classList.add("shot-tags-wrapper-expanded"),s.classList.remove("expand-tags-button-visible")}))}},{key:"handleGoodCard",value:function(){var t=document.querySelector(".js-caret-down"),e=document.querySelector(".js-caret-up"),s=document.querySelector(".js-shot-container"),a=document.querySelector(".js-good-card-content-expanded"),o=document.querySelector(".js-good-card-content-collapsed");t&&t.addEventListener("click",(function(){s.classList.add("good-card-collapsed"),a.classList.add("hide"),o.classList.remove("hide")})),e&&e.addEventListener("click",(function(){s.classList.remove("good-card-collapsed"),o.classList.add("hide"),a.classList.remove("hide")}))}}],a&&qt(e.prototype,a),n&&qt(e,n),t}();e.default=new Ht},17:function(t,e,s){"use strict";var a={name:"Icon",props:{type:{type:String,required:!0,validator:function(t){return["apple","bold","caretDown","caretLeft","caretLeftCircle","caretRight","caretRightCircle","centerAlign","check","chevronLeft","chevronRight","close","compress","copy","cross","exclamation","expand","eye","heart","imageLine","info","italic","leftAlign","lineArrowDown","lineArrowUp","lineTrash","link","minus","multishot","paypal","plus","repeat","rightAlign","singleShotLine","share","text","underline","videoLine","volumeHigh","volumeMute","thinArrowLeft","thinArrowRight","mail","calendar","closeCircle","markdown","creativeMarketLogo","creativeMarketIcon","slack","connections","mentorship","actionable"].includes(t)}},svgClass:{type:String,default:""},alt:{type:String,default:""}}},o=s(0),n=Object(o.a)(a,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("span",{staticClass:"display-inline-flex"},["lineArrowUp"===t.type?s("svg",{class:t.svgClass,attrs:{width:"15",height:"15",viewBox:"0 0 15 15",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M7.537.557A.748.748 0 006.72.72l-6.5 6.5a.75.75 0 101.06 1.06L6.5 3.06v11.19a.75.75 0 001.5 0V3.06l5.22 5.22a.75.75 0 101.06-1.06L7.78.72M7.538.557a.747.747 0 01.238.158L7.537.557z"}})]):t._e(),t._v(" "),"lineArrowDown"===t.type?s("svg",{class:t.svgClass,attrs:{width:"15",height:"15",viewBox:"0 0 15 15",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M8.037 14.943a.747.747 0 01-.817-.163l-6.5-6.5a.75.75 0 111.06-1.06L7 12.44V1.25a.75.75 0 011.5 0v11.19l5.22-5.22a.75.75 0 111.06 1.06l-6.5 6.5m-.243.163a.748.748 0 00.238-.158l-.238.158z"}})]):t._e(),t._v(" "),"copy"===t.type?s("svg",{class:t.svgClass,attrs:{width:"15",height:"15",viewBox:"0 0 15 15",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M1.798 2.161A.55.55 0 012.186 2h5.85a.55.55 0 01.55.55v.65a.75.75 0 001.5 0v-.65A2.05 2.05 0 008.037.5h-5.85a2.05 2.05 0 00-2.05 2.05V8.4a2.05 2.05 0 002.05 2.05h.65a.75.75 0 000-1.5h-.65a.55.55 0 01-.55-.55V2.55a.55.55 0 01.162-.389zM6.187 7.1a.55.55 0 01.55-.55h5.85a.55.55 0 01.55.55v5.85a.55.55 0 01-.55.55h-5.85a.55.55 0 01-.55-.55V7.1zm.55-2.05a2.05 2.05 0 00-2.05 2.05v5.85A2.05 2.05 0 006.737 15h5.85a2.05 2.05 0 002.05-2.05V7.1a2.05 2.05 0 00-2.05-2.05h-5.85z"}})]):t._e(),t._v(" "),"repeat"===t.type?s("svg",{class:t.svgClass,attrs:{width:"13",height:"15",viewBox:"0 0 13 15",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M9.553.72a.75.75 0 10-1.06 1.06l1.083 1.084H3.114A3.114 3.114 0 000 5.977V7.16a.75.75 0 101.5 0V5.977a1.614 1.614 0 011.614-1.613h6.462L8.492 5.447a.75.75 0 101.061 1.06l2.36-2.359a.761.761 0 00.153-.216.747.747 0 00-.152-.851L9.553.72zM3.644 8.992a.75.75 0 010 1.061l-1.083 1.083h6.462a1.614 1.614 0 001.613-1.613V8.34a.75.75 0 011.5 0v1.182a3.114 3.114 0 01-3.113 3.113H2.56l1.083 1.084a.75.75 0 01-1.06 1.06L.224 12.422a.762.762 0 01-.156-.221.747.747 0 01.15-.845l2.364-2.364a.75.75 0 011.061 0z"}})]):t._e(),t._v(" "),"lineTrash"===t.type?s("svg",{class:t.svgClass,attrs:{width:"14",height:"15",viewBox:"0 0 14 15",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M5.548 2.161A.55.55 0 015.937 2h2.6a.55.55 0 01.55.55v.55h-3.7v-.55a.55.55 0 01.16-.389zM3.887 3.1v-.55A2.05 2.05 0 015.937.5h2.6a2.05 2.05 0 012.05 2.05v.55h2.5a.75.75 0 010 1.5h-.55v8.35a2.05 2.05 0 01-2.05 2.05h-6.5a2.05 2.05 0 01-2.05-2.05V4.6h-.55a.75.75 0 010-1.5h2.5zm.75 1.5h6.4v8.35a.55.55 0 01-.55.55h-6.5a.55.55 0 01-.55-.55V4.6h1.2zm1.3 1.75a.75.75 0 01.75.75V11a.75.75 0 01-1.5 0V7.1a.75.75 0 01.75-.75zm3.35.75a.75.75 0 10-1.5 0V11a.75.75 0 001.5 0V7.1z"}})]):t._e(),t._v(" "),"chevronLeft"===t.type?s("svg",{class:t.svgClass,attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M9.021 3.057L4 8l5.021 4.943c.528.52 1.388.52 1.916 0a1.32 1.32 0 000-1.886L7.83 8l3.106-3.058a1.314 1.314 0 000-1.885 1.372 1.372 0 00-1.916 0z"}})]):t._e(),t._v(" "),"chevronRight"===t.type?s("svg",{class:t.svgClass,attrs:{width:"12",height:"12",viewBox:"0 0 12 12",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M4.47 1.154a.814.814 0 00-1.149 0 .806.806 0 000 1.143l3.636 3.62-3.636 3.62a.806.806 0 000 1.143.814.814 0 001.148 0L8.667 6.5a.817.817 0 00.224-.381.806.806 0 00-.228-.79L4.47 1.155z"}})]):t._e(),t._v(" "),"videoLine"===t.type?s("svg",{class:t.svgClass,attrs:{width:"20",height:"20",viewBox:"0 0 20 20",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M2 2.5h16a.5.5 0 01.5.5v14a.5.5 0 01-.5.5H2a.5.5 0 01-.5-.5V3a.5.5 0 01.5-.5zM0 3a2 2 0 012-2h16a2 2 0 012 2v14a2 2 0 01-2 2H2a2 2 0 01-2-2V3zm8.25 5.151L11.771 10l-3.523 1.849V8.15zm5.062.964a1 1 0 010 1.77l-5.098 2.676a1 1 0 01-1.465-.885V7.324a1 1 0 011.465-.885l5.098 2.676z"}})]):t._e(),t._v(" "),"imageLine"===t.type?s("svg",{class:t.svgClass,attrs:{width:"20",height:"20",viewBox:"0 0 20 20",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M16 1.5H4A2.5 2.5 0 001.5 4v12a2.5 2.5 0 001.265 2.174l9.29-9.29a2.75 2.75 0 013.89 0l2.555 2.555V4A2.5 2.5 0 0016 1.5zm2.5 12.059a.741.741 0 01-.03-.029l-3.586-3.586a1.25 1.25 0 00-1.768 0L4.561 18.5H16a2.5 2.5 0 002.5-2.5v-2.441zM4 0a4 4 0 00-4 4v12a4 4 0 004 4h12a4 4 0 004-4V4a4 4 0 00-4-4H4zm2.5 5.75a.75.75 0 100 ********* 0 000-1.5zm-2.25.75a2.25 2.25 0 114.5 0 2.25 2.25 0 01-4.5 0z"}})]):t._e(),t._v(" "),"singleShotLine"===t.type?s("svg",{class:t.svgClass,attrs:{width:"20",height:"20",viewBox:"0 0 20 20",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M.75 0a.75.75 0 100 1.5h4.5a.75.75 0 000-1.5H.75zM2 4.5h16a.5.5 0 01.5.5v10a.5.5 0 01-.5.5H2a.5.5 0 01-.5-.5V5a.5.5 0 01.5-.5zM0 5a2 2 0 012-2h16a2 2 0 012 2v10a2 2 0 01-2 2H2a2 2 0 01-2-2V5zm0 14.25a.75.75 0 01.75-.75h10.5a.75.75 0 010 1.5H.75a.75.75 0 01-.75-.75zm13.75-.75a.75.75 0 000 1.5h2.5a.75.75 0 000-1.5h-2.5z"}})]):t._e(),t._v(" "),"leftAlign"===t.type?s("svg",{class:t.svgClass,attrs:{width:"13",height:"13",viewBox:"0 0 13 13",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M0 1.25A.75.75 0 01.75.5h6.5a.75.75 0 010 1.5H.75A.75.75 0 010 1.25zm0 5a.75.75 0 01.75-.75h11.5a.75.75 0 010 1.5H.75A.75.75 0 010 6.25zm.75 4.25a.75.75 0 000 1.5h9.5a.75.75 0 000-1.5H.75z"}})]):t._e(),t._v(" "),"centerAlign"===t.type?s("svg",{class:t.svgClass,attrs:{width:"13",height:"13",viewBox:"0 0 13 13",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M3 1.25A.75.75 0 013.75.5h5.5a.75.75 0 010 1.5h-5.5A.75.75 0 013 1.25zm-3 5a.75.75 0 01.75-.75h11.5a.75.75 0 010 1.5H.75A.75.75 0 010 6.25zm3.75 4.25a.75.75 0 000 1.5h5.5a.75.75 0 000-1.5h-5.5z"}})]):t._e(),t._v(" "),"rightAlign"===t.type?s("svg",{class:t.svgClass,attrs:{width:"13",height:"13",viewBox:"0 0 13 13",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M13 1.25a.75.75 0 00-.75-.75h-6.5a.75.75 0 000 1.5h6.5a.75.75 0 00.75-.75zm0 5a.75.75 0 00-.75-.75H.75a.75.75 0 000 1.5h11.5a.75.75 0 00.75-.75zm-.75 4.25a.75.75 0 010 1.5h-9.5a.75.75 0 010-1.5h9.5z"}})]):t._e(),t._v(" "),"link"===t.type?s("svg",{class:t.svgClass,attrs:{width:"18",height:"13",viewBox:"0 0 18 13",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M2.555 4.555A2.75 2.75 0 014.5 3.75h2.1a.75.75 0 000-1.5H4.5a4.25 4.25 0 000 8.5h2.1a.75.75 0 000-1.5H4.5a2.75 2.75 0 01-1.945-4.695zM10.8 2.25a.75.75 0 000 1.5h2.1a2.75 2.75 0 010 5.5h-2.1a.75.75 0 000 1.5h2.1a4.25 4.25 0 100-8.5h-2.1zm-4.9 3.5a.75.75 0 000 1.5h5.6a.75.75 0 000-1.5H5.9z"}})]):t._e(),t._v(" "),"bold"===t.type?s("svg",{class:t.svgClass,attrs:{width:"13",height:"13",viewBox:"0 0 13 13",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M2 .25a.75.75 0 00-.75.75v11c0 .414.336.75.75.75h5.712c.892 0 1.734-.385 2.346-1.047A3.62 3.62 0 0011 9.25a3.62 3.62 0 00-.942-2.453 3.324 3.324 0 00-.669-.558 3.62 3.62 0 00.976-2.49 3.62 3.62 0 00-.942-2.452A3.193 3.193 0 007.077.25H2zm5.077 5.5c.454 0 .903-.195 1.244-.564a2.12 2.12 0 00.544-1.436 2.12 2.12 0 00-.544-1.436c-.341-.37-.79-.564-1.244-.564H2.75v4h4.327zM2.75 7.25v4h4.962c.454 0 .903-.195 1.243-.564A2.12 2.12 0 009.5 9.25a2.12 2.12 0 00-.545-1.436c-.34-.37-.79-.564-1.243-.564H2.75z"}})]):t._e(),t._v(" "),"underline"===t.type?s("svg",{class:t.svgClass,attrs:{width:"13",height:"13",viewBox:"0 0 13 13",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M2.5 0a.75.75 0 01.75.75v3.556c0 .968.356 1.887.973 2.555.615.666 1.435 1.028 2.277 1.028.842 0 1.662-.362 2.277-1.028.617-.668.973-1.587.973-2.555V.75a.75.75 0 011.5 0v3.556c0 1.33-.487 2.615-1.37 3.572-.886.96-2.1 1.51-3.38 1.51-1.28 0-2.494-.55-3.38-1.51-.883-.957-1.37-2.243-1.37-3.572V.75A.75.75 0 012.5 0zM.25 12.25A.75.75 0 011 11.5h11a.75.75 0 010 1.5H1a.75.75 0 01-.75-.75z"}})]):t._e(),t._v(" "),"info"===t.type?s("svg",{class:t.svgClass,attrs:{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",fill:"currentColor","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M12 0C5.373 0 0 5.37 0 12c0 6.627 5.373 12 12 12s12-5.373 12-12c0-6.63-5.373-12-12-12zm1 18a1 1 0 01-2 0v-7a1 1 0 012 0zM12 8a1.5 1.5 0 11.001-3.001A1.5 1.5 0 0112 8z"}})]):t._e(),t._v(" "),"italic"===t.type?s("svg",{class:t.svgClass,attrs:{width:"13",height:"13",viewBox:"0 0 13 13",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M8.893.25h2.732a.75.75 0 010 1.5h-2.23l-3.563 9.5h2.356a.75.75 0 110 1.5H2a.75.75 0 010-1.5h2.23l3.563-9.5H5.437a.75.75 0 010-1.5h3.456z"}})]):t._e(),t._v(" "),"text"===t.type?s("svg",{class:t.svgClass,attrs:{width:"20",height:"20",viewBox:"0 0 20 20",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M1.25 2A.75.75 0 012 1.25h16a.75.75 0 01.75.75v3a.75.75 0 01-1.5 0V2.75h-6.5v14.773h1.978a.75.75 0 010 1.5H7.273a.75.75 0 010-1.5H9.25V2.75h-6.5V5a.75.75 0 01-1.5 0V2z"}})]):t._e(),t._v(" "),"multishot"===t.type?s("svg",{class:t.svgClass,attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M1.25 1.333c-.69 0-1.25.56-1.25 1.25v7.5c0 .69.56 1.25 1.25 1.25h10.833c.69 0 1.25-.56 1.25-1.25v-7.5c0-.69-.56-1.25-1.25-1.25H1.25z"}}),s("path",{attrs:{d:"M2.667 12.75v-.083h10.75c.69 0 1.25-.56 1.25-1.25V4h.083c.69 0 1.25.56 1.25 1.25v7.5c0 .69-.56 1.25-1.25 1.25H3.917c-.69 0-1.25-.56-1.25-1.25z"}})]):t._e(),t._v(" "),"expand"===t.type?s("svg",{class:t.svgClass,attrs:{width:"13",height:"13",viewBox:"0 0 13 13",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M12.537.462c.18.18.25.426.213.659v3.546a.75.75 0 01-1.5 0V2.824L8.26 5.815A.76.76 0 117.183 4.74l2.99-2.99h-1.84a.75.75 0 110-1.5h3.542a.759.759 0 01.661.212zM1.75 8.333a.75.75 0 10-1.5 0V12c0 .414.336.75.75.75h3.667a.75.75 0 000-1.5H2.81l2.998-2.998a.75.75 0 10-1.06-1.06L1.75 10.188V8.333z"}})]):t._e(),t._v(" "),"compress"===t.type?s("svg",{class:t.svgClass,attrs:{width:"13",height:"13",viewBox:"0 0 13 13",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M12.53.47a.75.75 0 010 1.06L9.534 4.527h1.856a.75.75 0 010 1.5h-3.64a.748.748 0 01-.777-.75V1.61a.75.75 0 011.5 0v1.857L11.47.47a.75.75 0 011.06 0zM1.61 6.972a.75.75 0 000 1.5h1.857L.47 11.47a.75.75 0 001.06 1.06l2.997-2.997v1.856a.75.75 0 001.5 0v-3.64a.748.748 0 00-.75-.777m-3.667 0h3.667z"}})]):t._e(),t._v(" "),"volumeMute"===t.type?s("svg",{class:t.svgClass,attrs:{height:"24",fill:"currentColor",viewBox:"0 0 24 24",width:"24",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M21.414 12l2.293-2.293a1 1 0 00-1.414-1.414L20 10.586l-2.293-2.293a1 1 0 00-1.414 1.414L18.586 12l-2.293 2.293a1 1 0 101.414 1.414L20 13.414l2.293 2.293a1 1 0 001.414-1.414zM3 6a3 3 0 00-3 3v6a3 3 0 003 3h2.649L12 23V1L5.649 6z"}})]):t._e(),t._v(" "),"volumeHigh"===t.type?s("svg",{class:t.svgClass,attrs:{height:"24",fill:"currentColor",viewBox:"0 0 24 24",width:"24",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M3 6a3 3 0 00-3 3v6a3 3 0 003 3h2.649L12 23V1L5.649 6zm17 6a8.251 8.251 0 00-2.271-5.686 1 1 0 00-1.454 1.374 6.253 6.253 0 010 8.626 1 1 0 101.453 1.374A8.25 8.25 0 0020 12zM18.6 1.2a1 1 0 10-1.2 1.6 11.5 11.5 0 010 18.4 1 1 0 101.2 1.6 13.5 13.5 0 000-21.6z"}})]):t._e(),t._v(" "),"check"===t.type?s("svg",{class:t.svgClass,attrs:{width:"12",height:"11",viewBox:"0 0 12 11",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M11.568.284a1.184 1.184 0 00-1.69.157l-5.31 6.501-2.703-1.838a1.185 1.185 0 00-1.664.34A1.24 1.24 0 00.534 7.14l4.497 3.06 6.691-8.193a1.242 1.242 0 00-.154-1.724z"}})]):t._e(),t._v(" "),"cross"===t.type?s("svg",{class:t.svgClass,attrs:{width:"12",height:"12",viewBox:"0 0 12 12",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M2.229.815A1 1 0 00.814 2.229L4.586 6 .814 9.771a1 1 0 101.415 1.414l3.77-3.77 3.772 3.77a1 1 0 101.414-1.414L7.414 6l3.771-3.771A1 1 0 009.771.815L6 4.585 2.229.816z"}})]):t._e(),t._v(" "),"caretDown"===t.type?s("svg",{class:t.svgClass,attrs:{width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M21.526 8.772a1.629 1.629 0 000-2.296 1.612 1.612 0 00-2.286 0L12 13.746l-7.24-7.27a1.612 1.612 0 00-2.286 0 1.628 1.628 0 000 2.296l8.36 8.395a1.606 1.606 0 00.763.448 1.611 1.611 0 001.577-.455l8.352-8.388z"}})]):t._e(),t._v(" "),"caretRight"===t.type?s("svg",{class:t.svgClass,attrs:{width:"5",height:"10",viewBox:"0 0 5 10",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M1.155.962a.679.679 0 00-.957 0 .672.672 0 000 .952l3.03 3.017-3.03 3.017a.672.672 0 000 .952.679.679 0 00.957 0l3.498-3.483a.67.67 0 00-.003-.975L1.155.962z"}})]):t._e(),t._v(" "),"caretLeft"===t.type?s("svg",{class:t.svgClass,attrs:{xmlns:"http://www.w3.org/2000/svg",width:"6",height:"10",viewBox:"0 0 6 10",fill:"currentColor","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M4.4 9.446c.29.294.76.294 1.05 0a.763.763 0 000-1.067L2.124 5 5.45 1.621a.763.763 0 000-1.067.734.734 0 00-1.05 0L.562 4.456a.767.767 0 00-.205.356.763.763 0 00.208.736L4.4 9.446z"}})]):t._e(),t._v(" "),"caretLeftCircle"===t.type?s("svg",{class:t.svgClass,attrs:{width:"22",height:"22",viewBox:"0 0 22 22",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M11 22C4.925 22 0 17.075 0 11S4.925 0 11 0s11 4.925 11 11-4.925 11-11 11zM7.5 11l3.766 3.707c.395.39 1.04.39 1.436 0a.985.985 0 000-1.413L10.372 11l2.33-2.293a.99.99 0 000-1.415 1.03 1.03 0 00-1.436 0L7.5 11z"}})]):t._e(),t._v(" "),"caretRightCircle"===t.type?s("svg",{class:t.svgClass,attrs:{width:"22",height:"22",viewBox:"0 0 22 22",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M11 22c6.075 0 11-4.925 11-11S17.075 0 11 0 0 4.925 0 11s4.925 11 11 11zm3.5-11l-3.766 3.707c-.395.39-1.04.39-1.436 0a.985.985 0 010-1.413L11.628 11l-2.33-2.293a.99.99 0 010-1.415 1.03 1.03 0 011.436 0L14.5 11z"}})]):t._e(),t._v(" "),"apple"===t.type?s("svg",{class:t.svgClass,attrs:{width:"18",height:"18",viewBox:"0 0 18 18",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M11.056 4.014c.445-.538.76-1.271.76-2.014 0-.102-.009-.204-.027-.287-.724.028-1.596.482-2.115 1.095-.409.463-.789 1.206-.789 1.948 0 .***************.*************.************** 0 1.465-.436 1.948-1.02zm.51 1.178c-1.085 0-1.967.659-2.533.659-.603 0-1.391-.622-2.337-.622-1.79 0-3.61 1.484-3.61 4.277 0 1.744.669 3.58 1.504 4.768.714 1.002 1.336 1.828 2.235 1.828.891 0 1.28-.594 2.385-.594 1.122 0 1.373.576 2.356.576.974 0 1.624-.891 2.236-1.772.687-1.012.974-1.995.984-2.041-.056-.019-1.921-.78-1.921-2.914 0-1.846 1.466-2.671 1.55-2.736-.965-1.392-2.44-1.43-2.849-1.43zM18.006 16H20.4v-4.425h2.95c2.718 0 4.555-1.8 4.555-4.472v-.019c0-2.68-1.837-4.471-4.555-4.471h-5.344V16zm4.76-11.43c1.716 0 2.718.919 2.718 2.524v.018c0 1.605-1.002 2.533-2.719 2.533H20.4V4.57h2.365zm8.712 11.597c1.336 0 2.412-.575 2.969-1.559h.158V16h2.282V9.07c0-2.143-1.466-3.405-4.073-3.405-2.412 0-4.045 1.132-4.267 2.839l-.01.083h2.18l.01-.037c.232-.668.918-1.048 1.976-1.048 1.243 0 1.902.557 1.902 1.568v.872l-2.607.158c-2.468.148-3.85 1.206-3.85 3.015v.018c0 1.847 1.4 3.034 3.33 3.034zm-1.048-3.145v-.019c0-.835.603-1.308 1.892-1.391l2.283-.149v.817c0 1.206-1.03 2.115-2.422 2.115-1.02 0-1.753-.51-1.753-1.373zm8.778 6.54c2.245 0 3.404-.807 4.277-3.349L47.12 5.86h-2.43l-2.357 7.923h-.176L39.792 5.86H37.27l3.627 10.15-.12.463c-.25.919-.817 1.308-1.763 1.308-.241 0-.501-.009-.687-.028v1.772c.279.028.603.038.882.038z"}})]):t._e(),t._v(" "),"paypal"===t.type?s("svg",{class:t.svgClass,attrs:{width:"61",height:"18",viewBox:"0 0 61 18",fill:"none",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M7.613.918H2.894a.656.656 0 00-.648.553L.338 13.57a.393.393 0 00.39.454H2.98a.656.656 0 00.648-.554l.514-3.264a.655.655 0 01.648-.554h1.493c3.109 0 4.903-1.504 5.371-4.485.211-1.304.01-2.328-.601-3.046-.671-.788-1.86-1.204-3.44-1.204zm.544 4.419C7.9 7.03 6.606 7.03 5.355 7.03h-.712l.5-3.162a.393.393 0 01.388-.332h.326c.852 0 1.656 0 2.071.486.248.29.324.72.23 1.315zM21.718 5.282h-2.26a.394.394 0 00-.388.332l-.1.632-.159-.229c-.489-.71-1.58-.947-2.668-.947-2.497 0-4.63 1.891-5.045 4.544-.216 1.323.09 2.589.841 3.471.689.812 1.674 1.15 2.847 1.15 2.011 0 3.127-1.294 3.127-1.294l-.1.628a.393.393 0 00.387.455h2.036a.656.656 0 00.648-.554l1.22-7.734a.392.392 0 00-.386-.454zm-3.15 4.398c-.218 1.291-1.243 2.158-2.55 2.158-.656 0-1.18-.21-1.517-.61-.334-.396-.46-.96-.354-1.587.203-1.28 1.245-2.175 2.532-2.175.642 0 1.163.213 1.507.616.344.406.48.973.382 1.598zM33.752 5.282h-2.27a.658.658 0 00-.543.288l-3.132 4.613-1.328-4.433a.658.658 0 00-.629-.468H23.62a.393.393 0 00-.373.52l2.5 7.34-2.35 3.32a.393.393 0 00.32.621h2.268a.655.655 0 00.539-.281L34.075 5.9a.393.393 0 00-.323-.618z",fill:"#253B80"}}),s("path",{attrs:{d:"M41.27.918h-4.72a.655.655 0 00-.646.553l-1.909 12.1a.393.393 0 00.388.454h2.422a.459.459 0 00.452-.388l.542-3.43a.655.655 0 01.647-.554h1.493c3.11 0 4.903-1.504 5.372-4.485.212-1.304.008-2.328-.603-3.046-.67-.788-1.858-1.204-3.438-1.204zm.545 4.419c-.258 1.693-1.551 1.693-2.803 1.693h-.711l.5-3.162a.392.392 0 01.388-.332h.326c.851 0 1.656 0 2.071.486.248.29.323.72.229 1.315zM55.375 5.282h-2.259a.391.391 0 00-.387.332l-.1.632-.16-.229c-.488-.71-1.578-.947-2.667-.947-2.497 0-4.63 1.891-5.045 4.544-.215 1.323.09 2.589.841 3.471.69.812 1.674 1.15 2.847 1.15 2.011 0 3.127-1.294 3.127-1.294l-.1.628a.393.393 0 00.389.455h2.034a.655.655 0 00.648-.554l1.221-7.734a.394.394 0 00-.39-.454zm-3.15 4.398c-.217 1.291-1.243 2.158-2.55 2.158-.654 0-1.18-.21-1.517-.61-.334-.396-.46-.96-.354-1.587.204-1.28 1.245-2.175 2.532-2.175.642 0 1.163.213 1.507.616.345.406.482.973.382 1.598zM58.04 1.25L56.103 13.57a.393.393 0 00.388.454h1.947a.655.655 0 00.648-.554l1.91-12.1a.393.393 0 00-.388-.454h-2.18a.394.394 0 00-.388.332z",fill:"#179BD7"}})]):t._e(),t._v(" "),"thinArrowLeft"===t.type?s("svg",{class:t.svgClass,attrs:{width:"70",height:"48",viewBox:"0 0 70 48",fill:"none",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M69.5 1.49a1 1 0 00-2 .02l2-.02zM.981 38.146a1 1 0 00-.336 1.374l4.671 7.693a1 1 0 101.71-1.038l-4.152-6.838 6.838-4.152a1 1 0 10-1.038-1.71L.981 38.145zM67.5 1.51c.082 8.798-3.104 21.157-12.846 29.689-9.7 8.493-26.12 13.38-52.917 6.83l-.474 1.943c27.203 6.65 44.366 1.788 54.708-7.269 10.3-9.018 13.614-22.009 13.529-31.211l-2 .018z",fill:"#4D44C6"}})]):t._e(),t._v(" "),"thinArrowRight"===t.type?s("svg",{class:t.svgClass,attrs:{width:"61",height:"44",viewBox:"0 0 61 44",fill:"none",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M2.363.995a1 1 0 00-1.726 1.01L2.363.995zm57.844 35.712a1 1 0 000-1.414l-6.364-6.364a1 1 0 00-1.414 1.414L58.086 36l-5.657 5.657a1 1 0 001.414 1.414l6.364-6.364zM.637 2.005c2.515 4.3 6.251 13.145 14.822 20.803C24.079 30.511 37.532 37 59.5 37v-2c-21.532 0-34.495-6.344-42.709-13.683C8.528 13.933 5.015 5.528 2.363.995L.637 2.005z",fill:"#4D44C6"}})]):t._e(),t._v(" "),"exclamation"===t.type?s("svg",{class:t.svgClass,attrs:{width:"4",height:"14",viewBox:"0 0 4 14",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M1 1a1 1 0 012 0v7a1 1 0 01-1 1h-.001a1 1 0 01-1-1V1zM2 14a1.5 1.5 0 11.001-3.001A1.5 1.5 0 012 14z",fill:"#fff"}})]):t._e(),t._v(" "),"calendar"===t.type?s("svg",{class:t.svgClass,attrs:{fill:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M0 22a2 2 0 002 2h20a2 2 0 002-2V11H0zM22 2h-3V1a1 1 0 00-2 0v1H7V1a1 1 0 00-2 0v1H2a2 2 0 00-2 2v5h24V4a2 2 0 00-2-2zM6 7a1 1 0 110-2 1 1 0 010 2zm12 0a1 1 0 110-2 1 1 0 010 2z"}})]):t._e(),t._v(" "),"mail"===t.type?s("svg",{class:t.svgClass,attrs:{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M12 13.595c-.715 0-1.43-.153-2.095-.46L0 8.563V20a2 2 0 002 2h20a2 2 0 002-2V8.563l-9.905 4.572c-.665.307-1.38.46-2.095.46zM22 2H2a2 2 0 00-2 2v2.36l10.743 4.958a2.999 2.999 0 002.515 0L24 6.36V4a2 2 0 00-2-2z"}})]):t._e(),t._v(" "),"closeCircle"===t.type?s("svg",{class:t.svgClass,attrs:{fill:"currentColor",width:"25",height:"25",viewBox:"0 0 25 25",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{fill:"#fff",d:"M3 5h18v16H3z"}}),s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M12.505.135a12.375 12.375 0 11.001 24.751 12.375 12.375 0 010-24.75zm-5.37 16.28a1.032 1.032 0 001.46 1.46L12.5 13.97l3.91 3.91a1.032 1.032 0 001.46-1.459l-3.91-3.91 3.925-3.926a1.032 1.032 0 00-1.46-1.46L12.5 11.052l-3.92-3.92a1.032 1.032 0 00-1.46 1.46l3.92 3.92-3.905 3.905z"}})]):t._e(),t._v(" "),"markdown"===t.type?s("svg",{class:t.svgClass,attrs:{width:"22",height:"17",viewBox:"0 0 22 17",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M18 .5a3.333 3.333 0 013.333 3.333v9.334A3.333 3.333 0 0118 16.5H3.333A3.333 3.333 0 010 13.167V3.833A3.333 3.333 0 013.333.5H18zM6.764 11.348c-.08-.192-.188-.436-.324-.732a132.97 132.97 0 00-.42-.96A30.16 30.16 0 005.528 8.6a75.205 75.205 0 00-.48-1.032c-.152-.328-.296-.62-.432-.876a10.447 10.447 0 00-.312-.588c-.088.944-.16 1.968-.216 3.072a216.748 216.748 0 00-.144 3.324h-1.14a207.619 207.619 0 01.24-4.332c.056-.712.112-1.404.168-2.076.064-.672.132-1.308.204-1.908h1.02c.216.352.448.768.696 ************.496.984.744 1.512.248.52.488 1.044.72 1.572l.636 1.428.636-1.428c.232-.528.472-1.052.72-1.572.248-.528.496-1.032.744-1.512s.48-.896.696-1.248h1.02c.272 2.68.476 5.452.612 8.316h-1.14c-.04-1.12-.088-2.228-.144-3.324a73.174 73.174 0 00-.216-3.072c-.08.136-.188.332-.324.588-.128.256-.268.548-.42.876L8.924 8.6c-.168.36-.328.712-.48 1.056-.152.344-.296.664-.432.96-.128.296-.232.54-.312.732h-.936zm6.57-1.239a.667.667 0 01.942-.942l.983.983V4.574a.667.667 0 011.334 0v5.576l.983-.983a.667.667 0 01.943.943l-2.121 2.12a.667.667 0 01-.943 0l-2.122-2.12z"}})]):t._e(),t._v(" "),"eye"===t.type?s("svg",{class:t.svgClass,attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 14 12",fill:"currentColor","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M7 3.958a2.042 2.042 0 100 4.084 2.042 2.042 0 000-4.084zM7 .75C3.134.75 0 5.125 0 6s3.134 5.25 7 5.25S14 6.875 14 6 10.866.75 7 .75zm0 8.458a3.208 3.208 0 110-6.416 3.208 3.208 0 010 6.416z"}})]):t._e(),t._v(" "),"heart"===t.type?s("svg",{class:t.svgClass,attrs:{width:"14",height:"12",viewBox:"0 0 14 12",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M10.616.19C9.096.025 8.13.75 7 1.945 5.807.75 4.904.025 3.384.19 1.316.417-.304 2.774.048 4.833c.564 3.302 3.468 5.25 6.952 7 3.484-1.75 6.388-3.698 6.951-7C14.303 2.774 12.683.417 10.616.19z"}})]):t._e(),t._v(" "),"share"===t.type?s("svg",{class:t.svgClass,attrs:{viewBox:"0 0 16 16",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M0 12.782c0 .85.1 1.65.3 *********.35.45.5 0 1.05-2.65 2.75-5.15 5.55-5.65H8v2.2c0 1 .6 1.3 1.3.7l6.4-5.5c.35-.3.35-.8 0-1.15L9.3.332c-.7-.65-1.3-.3-1.3.65v2.35c-4.8.8-8 4.7-8 9.45z"}})]):t._e(),t._v(" "),"creativeMarketLogo"===t.type?s("svg",{class:t.svgClass,attrs:{width:"85",height:"32",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M26.582 26.12h-.93a.194.194 0 00-.152.076l-1.667 2.739c-.076.1-.139.1-.212 0l-1.614-2.736a.191.191 0 00-.153-.077h-.913a.198.198 0 00-.************* 0 00-.055.133v5.5a.183.183 0 **************.192 0 00.136.056h.91a.195.195 0 00.136-.056.187.187 0 00.056-.133v-2.829c0-.182.176-.259.286-.111l1.25 2.008a.189.189 0 **************.194 0 00.154-.076l1.29-2.015c.11-.145.287-.066.287.114v2.83a.182.182 0 **************.192 0 00.136.056h.927c.05 0 .1-.02.136-.056a.186.186 0 00.057-.133v-5.499c0-.05-.02-.098-.057-.133a.197.197 0 00-.136-.056l.001.002zm6.488 3.575h-1.215a.196.196 0 01-.154-.086.185.185 0 01-.017-.172l.606-1.578a.176.176 0 01.065-.085.183.183 0 01.208 0c.**************.066.085l.61 1.578a.183.183 0 01-.************* 0 01-.153.087zm.03-3.464a.177.177 0 00-.064-.081.183.183 0 00-.1-.032h-.945a.185.185 0 00-.*************** 0 00-.066.081l-2.361 5.5a.184.184 0 **************.197 0 00.155.089h.95a.185.185 0 00.104-.034.178.178 0 00.065-.086l.35-.92a.179.179 0 01.066-.085.187.187 0 01.104-.034h2.392c.037 0 .073.012.103.034.03.02.053.05.066.084l.357.923a.178.178 0 00.066.085c.03.02.066.032.103.033h.934a.196.196 0 00.155-.09.184.184 0 00.012-.175l-2.362-5.499h.006zm16.229 2.677a.177.177 0 010-.218l1.718-2.089a.176.176 0 00-.041-.264.187.187 0 00-.098-.028h-.944a.184.184 0 00-.14.067l-1.933 2.417a.183.183 0 01-.29-.008.172.172 0 01-.03-.103v-2.195a.173.173 0 00-.053-.125.182.182 0 00-.128-.053h-.857a.185.185 0 00-.128.053.175.175 0 00-.051.125v5.332a.18.18 0 00.051.125c.034.033.08.053.128.053h.851a.188.188 0 00.129-.053.177.177 0 00.052-.125v-.934c0-.04.014-.08.04-.112l.725-.88a.178.178 0 01.147-.067.18.18 0 01.14.077l1.406 2.013a.18.18 0 00.15.076h1.057a.187.187 0 00.156-.102.175.175 0 00-.017-.181l-2.04-2.801zm9.96 1.992h-2.804a.215.215 0 01-.15-.06.205.205 0 01-.064-.144v-.835a.2.2 0 01.064-.145.213.213 0 01.15-.058h1.878c.056 0 .11-.021.15-.06a.206.206 0 00.064-.146v-.623a.204.204 0 00-.064-.145.216.216 0 00-.15-.06h-1.878a.215.215 0 01-.15-.058.206.206 0 01-.064-.146v-.812a.209.209 0 01.064-.146.216.216 0 01.15-.059h2.609a.215.215 0 00.2-.123.203.203 0 00.018-.079v-.688a.204.204 0 00-.064-.145.216.216 0 00-.15-.06h-3.89a.216.216 0 00-.15.06.204.204 0 00-.063.145v5.282a.2.2 0 00.064.146c.04.038.094.06.15.059h4.08c.055 0 .11-.02.15-.06a.204.204 0 00.063-.145v-.688a.204.204 0 00-.064-.145.215.215 0 00-.15-.06l.002-.002zm6.806-4.777h-4.341a.185.185 0 00-.126.059.176.176 0 00-.046.128v.76a.173.173 0 00.046.128.183.183 0 00.126.059h1.413a.187.187 0 01.125.058.177.177 0 01.047.129v4.37a.176.176 0 00.047.128.185.185 0 00.125.058h.818a.185.185 0 00.125-.058.176.176 0 00.047-.128v-4.373a.175.175 0 01.104-.17.188.188 0 01.069-.017h1.42a.186.186 0 00.127-.059.177.177 0 00.046-.128v-.76a.176.176 0 00-.047-.128.185.185 0 00-.125-.059v.003zm-25.13 3.217a9.313 9.313 0 01-.702.012.193.193 0 01-.132-.057.183.183 0 01-.053-.13V27.42a.18.18 0 01.053-.13.188.188 0 01.133-.055c.211 0 .417 0 .65.016.763.032 1.34.385 1.34 1.038 0 .575-.402.992-1.29 1.052zm2.544-1.02c0-1.28-.973-2.19-2.66-2.2-.604 0-1.2.015-1.84.025a.19.19 0 00-.132.056.182.182 0 00-.053.13v5.48c0 .05.019.096.053.13a.19.19 0 00.133.056h.882c.05 0 .097-.02.132-.055a.182.182 0 00.054-.13v-1.164c0-.146.03-.187.187-.187h.819c.207 0 .262-.017.32.097l.671 1.332a.182.182 0 00.167.103h1.013a.193.193 0 00.16-.09.182.182 0 00.005-.18l-.776-1.527a.184.184 0 01.06-.237c.262-.188.473-.436.614-.722.141-.285.209-.6.196-.917h-.005zM79.064 9.878c1.528 0 .657 6.054-2.021 6.455a5.62 5.62 0 01-.195-1.365c0-1.977.764-5.09 2.223-5.09h-.007zM41.075 19.16c-2.77 0-.833-9.294 1.403-9.294 1.486 0 1.25 9.294-1.403 9.294zm-9.65-9.325c1.588 0 .645 5.87-1.966 6.833a5.911 5.911 0 01-.29-1.7c0-1.977.808-5.133 2.26-5.133h-.005zm52.559 5.348c-.078.075-.797 4.298-3.902 4.298-1.206 0-2.057-.883-2.582-1.938 3.798-.813 5.07-3.722 5.07-5.712-.006-2.317-1.704-3.158-3.15-3.158-2.362 0-5.438 2.126-5.438 6.29 0 .363.02.725.064 1.084-.819-.24-1.529-.662-2.01-1.381.71-3.075.584-6.015-.885-6.015-.825 0-1.528 1.05-1.528 3.338.012.987.265 1.957.737 2.83-.301 1.394-1.337 4.799-2.654 4.799-1.817 0-1.25-8.361-1.25-9.596 0-1.008-2.516-2.065-3.067-1.53-.24.233-.278 1.596-.278 3.36a24.49 24.49 0 00.44 4.74c-.175.86-.922 3.068-2.32 3.068-2.168 0-1.07-7.918-1.07-9.193 0-.91-2.42-1.76-3.01-1.193-.234.226-.395 1.827-.395 4.32a21.728 21.728 0 00.218 3.182c-.228 1.105-1.126 2.884-2.583 2.884-2.317 0-1.292-8.678-.675-14.028a17.73 17.73 0 011.158-.165c1.014-.11 2.258-.301 2.258.296 0 1.409.966 1.842 1.681 1.842.78 0 1.486-.598 1.486-1.743 0-1.144-1.008-2.191-2.814-2.191a22.902 22.902 0 00-3.59.325c.095-.896.16-1.604.16-2.032 0-1.193-2.53-2.334-3.029-1.85-.096.093-.434 1.961-.726 4.548-1.756.34-3.495.638-5.213.638-.595 0-.518 1.742 1.517 1.742 1.17 0 2.315-.25 3.511-.557a65.622 65.622 0 00-.278 5.747 26.892 26.892 0 00.33 4.522c-.228.962-.95 2.903-2.255 2.903-2.545 0-1.337-8.52-1.337-9.795 0-.606-2.316-1.911-2.795-1.45-.067.067-.107.426-.107.426s-.309-.22-1.163-.22c-3.249 0-5.856 3.885-5.856 7.43 0 .17.008.333.018.495-.503 1.14-1.775 3.115-4.08 3.115-1.147 0-1.987-.746-2.55-1.69 3.56-.975 4.973-4.01 4.973-5.946 0-2.269-1.99-3.386-3.435-3.386-2.509 0-5.279 2.438-5.279 6.545a7.348 7.348 0 00.637 3.103c-.445.69-1.144 1.38-2.084 1.38-2.602 0-.858-8.466-.858-9.369 0-.223 0-.645-.41-.928-.545-.37-3.68-.773-4.742-.976.008-.225.01-.43.01-.615 0-1.636-1.059-2.013-1.741-2.013-1.154 0-1.396 1.254-1.396 1.558.005.413.122.817.34 1.17.218.355.529.646.9.844 0 3.057-2.01 10.27-7.409 10.27-4.071 0-5.556-4.063-5.556-7.09 0-5.094 3.085-9.038 5.084-9.038 2.5 0 .296 4.678 2.802 4.678 1.767 0 2.012-1.655 2.012-2.416 0-1.425-1.537-4.093-4.804-4.093C3.864 1.643 0 7.79 0 13.06c0 5.064 3.59 9.142 8.63 9.142 7.779 0 9.723-8.002 10.129-12.48.499.136 1.73.343 1.851.461.203.197-.392 2.914-.392 5.527 0 3.278 1.11 5.902 3.775 5.902 1.667 0 3.013-1.173 3.717-2.075a5.311 5.311 0 001.844 1.482c.722.352 1.516.54 2.323.55 2.625 0 4.352-1.587 5.223-2.863.732 1.917 2.23 2.863 3.585 2.863 1.892 0 3.032-2.462 3.097-2.858.573 1.716 1.908 2.897 3.413 2.897 1.909 0 3.056-1.674 3.522-2.721.676 1.769 1.757 2.72 3.307 2.72 1.776 0 2.95-1.562 3.472-2.612.556 1.532 1.492 2.613 2.98 2.613 1.99 0 3.227-1.966 3.64-2.968.653 1.69 1.68 2.908 3.25 2.908 1.754 0 3.276-2.43 4.186-5.18a5.264 5.264 0 002.87 1.33c.885 2.469 2.953 3.849 5.132 3.849 3.81 0 5.279-3.793 5.429-4.677.15-.884-.72-1.975-1.013-1.692"}})]):t._e(),t._v(" "),"creativeMarketIcon"===t.type?s("svg",{class:t.svgClass,attrs:{width:"16",height:"16",fill:"currentColor",viewBox:"0 0 16 16",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M.195 8.242l7.543 7.542c.26.26.682.26.943 0l7.103-7.103c.13-.13.2-.308.195-.49L16 .856C15.99.504 15.476.01 15.122 0H7.79a.667.667 0 00-.49.195L.195 7.3a.667.667 0 000 .943zm13.2-5.657a.667.667 0 11-.943.943.667.667 0 01.943-.943zM8.217 4.496C7.698 6.193 6.53 7.258 4.883 7.77c1.648.512 2.815 1.577 3.334 3.274.518-1.697 1.685-2.762 3.333-3.274-1.648-.512-2.815-1.577-3.333-3.274z"}})]):t._e(),t._v(" "),"slack"===t.type?s("svg",{class:t.svgClass,attrs:{width:"25",height:"24",viewBox:"0 0 25 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("g",{attrs:{"clip-path":"url(#clip0_952_1312)"}},[s("path",{attrs:{d:"M4.742 15.164a1.728 1.728 0 01-1.72 1.724A1.728 1.728 0 011.3 15.165a1.727 1.727 0 011.723-1.72h1.719v1.72zm2.871 6.313v-6.31a1.727 1.727 0 011.721-1.722 1.727 1.727 0 011.721 1.721v6.311A1.728 1.728 0 019.334 23.2a1.727 1.727 0 01-1.721-1.723zM9.335 4.242a1.728 1.728 0 01-1.722-1.72A1.728 1.728 0 019.334.8a1.728 1.728 0 011.721 1.723v1.719h-1.72zM3.023 7.113h6.31a1.728 1.728 0 011.722 1.721 1.728 1.728 0 01-1.722 1.721h-6.31A1.728 1.728 0 011.3 8.834a1.728 1.728 0 011.723-1.721zm17.233 1.723s0 0 0 0a1.728 1.728 0 011.722-1.723A1.728 1.728 0 0123.7 8.834a1.727 1.727 0 01-1.723 1.721h-1.721V8.836zm-2.868-.004a1.728 1.728 0 01-1.723 1.723 1.727 1.727 0 01-1.72-1.722v-6.31A1.727 1.727 0 0115.665.8a1.728 1.728 0 011.723 1.723s0 0 0 0v6.309s0 0 0 0zm-1.725 10.924a1.728 1.728 0 011.725 1.722 1.728 1.728 0 01-1.723 1.722 1.727 1.727 0 01-1.72-1.723v-1.721h1.718zm.004-2.868a1.727 1.727 0 01-1.722-1.723 1.726 1.726 0 011.72-1.72h6.312a1.727 1.727 0 011.723 1.72 1.728 1.728 0 01-1.723 1.723s0 0 0 0h-6.31s0 0 0 0z",stroke:"#4D44C6","stroke-width":"1.6"}})]),s("defs",[s("clipPath",{attrs:{id:"clip0_952_1312"}},[s("path",{attrs:{fill:"#fff",transform:"translate(.5)",d:"M0 0h24v24H0z"}})])])]):t._e(),t._v(" "),"connections"===t.type?s("svg",{class:t.svgClass,attrs:{width:"25",height:"24",viewBox:"0 0 25 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("circle",{attrs:{cx:"12.5",cy:"13.1",r:"9.15",stroke:"#4D44C6","stroke-width":"1.5"}}),s("circle",{attrs:{cx:"12.5",cy:"4.3",r:"2.55",fill:"#F7F9FD",stroke:"#4D44C6","stroke-width":"1.5"}}),s("circle",{attrs:{cx:"20.2",cy:"18.6",r:"2.55",fill:"#F7F9FD",stroke:"#4D44C6","stroke-width":"1.5"}}),s("circle",{attrs:{cx:"4.8",cy:"18.6",r:"2.55",fill:"#F7F9FD",stroke:"#4D44C6","stroke-width":"1.5"}})]):t._e(),t._v(" "),"mentorship"===t.type?s("svg",{class:t.svgClass,attrs:{width:"25",height:"25",viewBox:"0 0 25 25",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M8.5 15l-2.648 7.944a.5.5 0 00.671.617L12.5 21l5.977 2.561a.5.5 0 00.67-.617L16.5 15",stroke:"#4D44C6","stroke-width":"1.5"}}),s("path",{attrs:{d:"M13.934 3.529l.************* 1.592.051.947 1.282.*************** 1.387.785.242 1.575.***************.863 1.34-.517 1.508-.*************.142 1.587-1.159 1.095-.116.11-.061.147-.613 1.471-1.534.43-.154.044-.123.102-1.226 1.018-1.559-.332-.156-.033-.156.033-1.56.332L9.56 15.94l-.123-.102-.154-.043-1.534-.43-.613-1.472-.061-.148-.116-.11-1.159-1.094.142-1.587.014-.16-.052-.15-.517-1.508.863-1.34.086-.134.025-.158.242-1.575 1.387-.785.139-.08.095-.128.947-1.282 1.593-.05.16-.006.143-.07 1.434-.695 1.434.696z",stroke:"#4D44C6","stroke-width":"1.5"}})]):t._e(),t._v(" "),"actionable"===t.type?s("svg",{class:t.svgClass,attrs:{width:"25",height:"24",viewBox:"0 0 25 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("rect",{attrs:{x:"3.25",y:"4.75",width:"18.5",height:"16.5",rx:"2.25",stroke:"#4D44C6","stroke-width":"1.5"}}),s("path",{attrs:{d:"M7.534 2v4.345M17.466 2v4.345M7.225 10.448h10.551",stroke:"#4F3CC9","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}})]):t._e(),t._v(" "),"plus"===t.type?s("svg",{class:t.svgClass,attrs:{viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M5.625 1.042a.625.625 0 10-1.25 0v3.333H1.042a.625.625 0 100 1.25h3.333v3.333a.625.625 0 101.25 0V5.625h3.333a.625.625 0 100-1.25H5.625V1.042z",fill:"currentColor"}})]):t._e(),t._v(" "),"minus"===t.type?s("svg",{class:t.svgClass,attrs:{viewBox:"0 0 10 2",fill:"none",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M4.375.375H1.042a.625.625 0 100 1.25h7.916a.625.625 0 100-1.25H4.375z",fill:"currentColor"}})]):t._e(),t._v(" "),"close"===t.type?s("svg",{class:t.svgClass,attrs:{width:"10",height:"10",viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M6.768 5l2.866-2.866A1.25 1.25 0 107.866.366L5 3.232 2.134.366A1.25 1.25 0 10.366 2.134L3.232 5 .366 7.866a1.25 1.25 0 101.768 1.768L5 6.767l2.866 2.867c.244.244.564.366.884.366a1.25 1.25 0 00.884-2.134L6.768 5z",fill:"currentColor"}})]):t._e()])}),[],!1,null,null,null);e.a=n.exports},229:function(t,e,s){"use strict";function a(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var s=0;s<e.length;s++){var a=e[s];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,a.key,a)}}s.d(e,"a",(function(){return n}));var n=function(){function t(){var e=this,s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};a(this,t),this.utm=s.utm,this.area=s.area,this.shareUrl=s.shareUrl||window.location.href,this.outlets=s.outlets||["twitter","facebook","linkedin","pinterest","email"],this.attach=s.attach||!1,this.demo=s.demo||!1,this.trackShotShare=s.trackShotShare||null,this.trackCollectionShare=s.trackCollectionShare||null,this.container=s.container||document.querySelector(s.selector),this.delegateEvents=s.delegateEvents||!1,this.body={raw:s.body,encoded:encodeURIComponent(s.body)},s.imageUrl&&(this.imageUrl={raw:s.imageUrl,encoded:encodeURIComponent(s.imageUrl)}),this.bodies={},s.bodies&&Object.keys(s.bodies).forEach((function(t){e.bodies[t]={raw:s.bodies[t],encoded:encodeURIComponent(s.bodies[t])}})),this.clipboardLink=s.clipboardLink,this.attachShareSheet()}var e,s,n;return e=t,(s=[{key:"attachShareSheet",value:function(){this.attach.twitter&&this.outlets.indexOf("twitter")>-1&&this.attachAction("twitter"),this.attach.facebook&&this.outlets.indexOf("facebook")>-1&&this.attachAction("facebook"),this.attach.linkedin&&this.outlets.indexOf("linkedin")>-1&&this.attachAction("linkedin"),this.attach.pinterest&&this.outlets.indexOf("pinterest")>-1&&this.imageUrl&&this.attachAction("pinterest"),this.attach.email&&this.outlets.indexOf("email")>-1&&this.attachAction("email"),this.attach.clipboard&&this.outlets.indexOf("clipboard")>-1&&this.attachAction("clipboard")}},{key:"createAction",value:function(t,e){var s=document.createElement("a"),a=document.createTextNode(t);return s.appendChild(a),s.title=e,s.class="share-".concat(t),s.href="#",s.addEventListener("click",this.shareTo.bind(this,t)),s}},{key:"attachAction",value:function(t){var e=this;this.delegateEvents?(this["attach".concat(t)]&&this.container.removeEventListener("click",this.attach[t]),this["attach".concat(t)]=function(s){e.shareTo(t,s)},document.addEventListener("click",(function(s){var a=e.attach[t].substring(1);if(s.target.classList.contains(a))e["attach".concat(t)].call(s.target,s);else{var o=s.target.closest(e.attach[t]);o&&e["attach".concat(t)].call(o,s)}}),!1)):this.container.querySelector(this.attach[t]).addEventListener("click",this.shareTo.bind(this,t))}},{key:"shareTo",value:function(t,e){if(e.preventDefault(),this.demo)this.demo();else{var s=null,a=null;switch(t){case"twitter":var o=this.bodies.twitter?this.bodies.twitter.encoded:this.body.encoded;s="https://twitter.com/intent/tweet?url=".concat(this.url("Twitter",e.currentTarget).encoded,"&text=").concat(o),a="status=0,toolbar=0,height=250,width=600";break;case"facebook":s="https://www.facebook.com/sharer/sharer.php?u=".concat(this.url("Facebook",e.currentTarget).encoded),a="status=0,toolbar=0,height=350,width=600";break;case"linkedin":s="https://www.linkedin.com/shareArticle?mini=true&url=".concat(window.location.href,"&title=").concat(this.body.encoded,"&summary=").concat(this.body.encoded,"&source=Dribbble"),a="status=0,toolbar-0,height=350,width=600";break;case"pinterest":s="https://pinterest.com/pin/create/button/?url=".concat(this.url("Pinterest",e.currentTarget).encoded,"&description=").concat(this.body.encoded,"&media=").concat(this.imageUrl.encoded),a="status=0,toolbar=0,height=260,width=600";break;case"email":s="mailto:?&body=".concat(this.body.encoded).concat(encodeURIComponent("\n\n")).concat(this.url("Email",e.currentTarget).encoded);break;case"clipboard":var n=document.createElement("input");if(n.value=this.url("Clipboard",e.currentTarget).raw,n.style.position="fixed",n.style.top="0px",n.style.left="0px",n.style.zIndex="-1",document.body.appendChild(n),n.focus(),n.select(),document.execCommand("Copy")){var i="A link to this page has been copied to your clipboard!";Dribbble.EventBus?Dribbble.EventBus.$emit("siteNotifications:add",{title:"Link Copied",message:i,type:"success",id:"clipboard-copy-success".concat((new Date).getTime())}):alert(i)}else{var r='It looks like we couldn\'t copy this link. Please right click on the button and click "Copy Link Address".';Dribbble.EventBus?Dribbble.EventBus.$emit("siteNotifications:add",{title:"Error",messageText:r,id:"clipboard-copy-success".concat((new Date).getTime())}):alert(r)}document.body.removeChild(n);break;default:console.warn("".concat(t," does not have a handler"))}this.trackShotShare&&Dribbble.Itly.trackShotEvent({eventName:"shotShared",shotId:this.trackShotShare.shotId,additionalData:{social_platform:t}}),s&&(a?window.open(s,"",a):window.location.href=s)}}},{key:"url",value:function(t,e){var s,a,o=this.shareUrl;return this.utm&&(s=e&&e.dataset&&e.dataset.shareSource?e.dataset.shareSource:"".concat(t,"_").concat(this.utm.source),a=e&&e.dataset&&e.dataset.shareCampaign?e.dataset.shareCampaign:this.utm.campaign,o=Dribbble.Url.addParams(o,[["utm_source",s],["utm_campaign",a],["utm_content",this.utm.content],["utm_medium","Social_Share"]])),{raw:o,encoded:encodeURIComponent(o)}}}])&&o(e.prototype,s),n&&o(e,n),t}()},393:function(t,e,s){"use strict";function a(t,e){for(var s=0;s<e.length;s++){var a=e[s];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,a.key,a)}}s.r(e),s.d(e,"default",(function(){return o}));var o=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.actionsMenu=e}var e,s,o;return e=t,(s=[{key:"initialize",value:function(){this.actionsMenu&&(this.actionMenuTrigger=this.actionsMenu.querySelector(".action-menu-trigger"),this.menuLinks=this.actionsMenu.querySelectorAll(".action-menu-items a"),this.menuActive=!1,this.bindEvents())}},{key:"bindEvents",value:function(){var t=this;this.actionMenuTrigger.addEventListener("click",this.onClickMenuTrigger.bind(this)),this.menuLinks.forEach((function(e){e.addEventListener("click",t.onDropdownClick.bind(t))}));var e=document.querySelector(".overlay-content");e&&e.hasChildNodes()?e.addEventListener("click",this.onClickDocument.bind(this)):document.addEventListener("click",this.onClickDocument.bind(this))}},{key:"onClickMenuTrigger",value:function(t){t.preventDefault(),this.menuActive?this.hide():this.show()}},{key:"onDropdownClick",value:function(){this.hide()}},{key:"onClickDocument",value:function(t){!t.target.closest(".js-actions-menu")&&this.menuActive&&this.hide()}},{key:"show",value:function(){this.actionsMenu.classList.add("active"),this.menuActive=!0}},{key:"hide",value:function(){this.actionsMenu.classList.remove("active"),this.menuActive=!1}}])&&a(e.prototype,s),o&&a(e,o),t}()},41:function(t,e,s){"use strict";var a={name:"VButton",props:{type:{type:String,default:"primary",validator:function(t){return["primary","secondary","tertiary","gradient","text"].includes(t)}},text:{type:String,default:null},isDisabled:{type:Boolean,required:!1,default:!1}},computed:{buttonClass:function(){switch(this.type){case"secondary":return"form-btn";case"tertiary":return"form-btn outlined";case"text":return"form-btn text-btn text-btn-dark";case"gradient":return"form-gradient";case"primary":default:return"form-sub"}}}},o=s(0),n=Object(o.a)(a,(function(){var t=this,e=t.$createElement;return(t._self._c||e)("button",{class:[{disabled:t.isDisabled},t.buttonClass],attrs:{disabled:t.isDisabled},on:{click:function(e){return t.$emit("click",e)}}},[t._v("\n  "+t._s(t.text)+"\n  "),t._t("default")],2)}),[],!1,null,null,null);e.a=n.exports},46:function(t,e,s){"use strict";s.d(e,"c",(function(){return i})),s.d(e,"b",(function(){return r})),s.d(e,"a",(function(){return l}));var a=s(97),o=s.n(a),n=s(79),i=function(t){return function(t){return t.replace(/([^A-Z])([A-Z])/g,"$1 $2").replace(/[_\-]+/g," ").toLowerCase()}(t).replace(/(^\w|\b\w)/g,(function(t){return t.toUpperCase()}))},r=function(t,e){return"".concat(Object(n.a)(e)," ").concat(o()(t,e))},l=function(t){return"undefined"!==typeof t&&null!==t}},559:function(t,e,s){"use strict";var a={name:"LazyImg",props:{id:{type:[Number],required:!0},src:{type:String,required:!0},errorFallback:{type:String,required:!1,default:null},type:{type:String,required:!1,default:"image"},imgClass:{type:String,required:!1,default:""},alt:{type:String,required:!1,default:""}},data:function(){return{isLoaded:!1}},mounted:function(){this.$refs.lazyImg.addEventListener("lazyloaded",this.onLoaded)},beforeDestroy:function(){this.$refs.lazyImg.removeEventListener("lazyloaded",this.onLoaded)},methods:{onLoaded:function(t){this.isLoaded=!0,this.$emit("image-loaded",{id:this.id,el:t.target})}}},o=s(0),n=Object(o.a)(a,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"lazy-img-wrapper",class:{loaded:t.isLoaded}},["image"===t.type?[s("img",{ref:"lazyImg",staticClass:"lazyload",class:t.imgClass,attrs:{"data-src":t.src,"data-id":t.id,alt:t.alt}})]:t._e(),t._v(" "),"background"===t.type?[s("figure",{ref:"lazyImg",staticClass:"lazyload lazy-img-background",class:t.imgClass,attrs:{"data-bg":t.src,"data-id":t.id}},[t._t("img-content")],2)]:t._e(),t._v(" "),t.isLoaded?t._e():[t._t("default")]],2)}),[],!1,null,null,null);e.a=n.exports},567:function(t,e,s){"use strict";var a={name:"DribbbleLoader",props:{color:{type:0,required:!1,default:"deepBlueSea"}},computed:{strokeColor:function(){switch(this.color){case"white":return"#ffffff";case"deepBlueSea":default:return"#0d0c22"}}}},o=s(0),n=Object(o.a)(a,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("svg",{staticClass:"dribbble-loader",attrs:{width:"53",height:"53",viewBox:"0 0 53 53",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[s("path",{attrs:{d:"M26.2739 50.14C39.5289 50.14 50.2743 39.3948 50.2743 26.14C50.2743 12.8851 39.5289 2.13995 26.2739 2.13995C13.0188 2.13995 2.2735 12.8851 2.2735 26.14C2.2735 39.3948 13.0188 50.14 26.2739 50.14Z","stroke-width":"3",stroke:t.strokeColor}}),t._v(" "),s("path",{attrs:{id:"dd-pl-path-1",d:"M36.9492 47.641C34.5295 31.1135 27.4759 16.091 17.1498 3.93506",stroke:t.strokeColor,"stroke-width":"3","stroke-dasharray":"96","stroke-dashoffset":"0"}}),t._v(" "),s("path",{attrs:{id:"dd-pl-path-2",d:"M2.39471 23.7131C4.30685 23.8299 6.27005 23.8732 8.27093 23.8372C26.1713 23.5146 40.828 16.9487 42.8353 8.76953",stroke:t.strokeColor,"stroke-width":"3","stroke-dasharray":"96","stroke-dashoffset":"0"}}),t._v(" "),s("path",{attrs:{id:"dd-pl-path-3",d:"M10.2392 43.998C15.2675 33.5505 25.9537 26.34 38.3241 26.34C42.5077 26.34 46.4988 27.1647 50.1436 28.6605",stroke:t.strokeColor,"stroke-width":"3","stroke-dasharray":"96","stroke-dashoffset":"0"}}),t._v(" "),s("animate",{attrs:{"xlink:href":"#dd-pl-path-1",attributeName:"stroke-dashoffset",from:"0",to:"192",dur:"1s",repeatCount:"indefinite"}}),t._v(" "),s("animate",{attrs:{"xlink:href":"#dd-pl-path-2",attributeName:"stroke-dashoffset",from:"0",to:"192",dur:"2s",begin:"1s",repeatCount:"indefinite"}}),t._v(" "),s("animate",{attrs:{"xlink:href":"#dd-pl-path-3",attributeName:"stroke-dashoffset",from:"0",to:"192",dur:"2.75",begin:"1.75s",repeatCount:"indefinite"}}),t._v(" "),s("defs",[s("linearGradient",{attrs:{id:"dd-radial",x1:"25.5",y1:"5.5",x2:"25.5",y2:"45.5",gradientUnits:"userSpaceOnUse"}},[s("stop",{attrs:{"stop-color":"currentColor",offset:"0","stop-opacity":"0"}}),t._v(" "),s("stop",{attrs:{"stop-color":"currentColor",offset:"0.33"}}),t._v(" "),s("stop",{attrs:{"stop-color":"currentColor",offset:"0.66"}}),t._v(" "),s("stop",{attrs:{"stop-color":"currentColor",offset:"1","stop-opacity":"0"}})],1)],1)])}),[],!1,null,null,null);e.a=n.exports},568:function(t,e,s){"use strict";var a={name:"ProgressRing",props:{radius:{type:Number,required:!0},progress:{type:Number,required:!0},stroke:{type:Number,required:!1,default:4},strokeColor:{type:String,required:!1,default:"#db5788"},fillColor:{type:String,required:!1,default:"transparent"},innerCircle:{type:Boolean,required:!1,default:!1},innerCircleSpacing:{type:Number,required:!1},innerCircleFillColor:{type:String,required:!1,default:"transparent"},hasBaseCircle:{type:Boolean,required:!1,default:!1},baseStrokeColor:{type:String,required:!1,default:"#E7E7E9"},rotateDegrees:{type:Number,required:!1,default:90}},data:function(){var t=this.radius-2*this.stroke,e=2*t*Math.PI;return{normalizedRadius:t,normalizedRadiusInner:this.radius-2*this.innerCircleSpacing,circumference:e}},computed:{strokeDashoffset:function(){return this.circumference-this.progress/100*this.circumference}}},o=s(0),n=Object(o.a)(a,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("svg",{staticClass:"progress-ring",attrs:{height:2*t.radius,width:2*t.radius}},[t.hasBaseCircle?s("circle",{attrs:{fill:"none",stroke:t.baseStrokeColor,"stroke-width":t.stroke,r:t.normalizedRadius,cx:t.radius,cy:t.radius}}):t._e(),t._v(" "),s("circle",{staticClass:"progress-ring-circle",style:{strokeDashoffset:t.strokeDashoffset},attrs:{stroke:t.strokeColor,"stroke-dasharray":t.circumference+" "+t.circumference,"stroke-width":t.stroke,fill:t.fillColor,r:t.normalizedRadius,cx:t.radius,cy:t.radius,transform:"rotate("+t.rotateDegrees+", "+t.radius+", "+t.radius+")"}}),t._v(" "),t.innerCircle?s("circle",{staticClass:"progress-ring-inner-circle",attrs:{r:t.normalizedRadiusInner,cx:t.radius,cy:t.radius,fill:t.innerCircleFillColor}}):t._e()])}),[],!1,null,null,null);e.a=n.exports},577:function(t,e,s){"use strict";var a=s(2),o=s.n(a),n={name:"VConfirmModal",components:{Modal:s(82).a},props:{show:{type:Boolean,default:!1},title:{type:String,required:!0},message:{type:String,required:!1,default:""},buttons:{type:Object,default:function(){return{no:"No",yes:"Yes"}}}},methods:{_emit:function(t,e){this.$root.$emit(t,e)},saveChanges:function(){this._emit("save",!0)}}},i=s(0),r=Object(i.a)(n,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("modal",{staticClass:"confirm-modal",class:{"confirm-modal-simple":!t.message},attrs:{id:"confirm-modal","show-modal":t.show,"without-sections-borders":!0,closeable:!1,"max-width":"450px"},on:{"modal-close":function(e){return t._emit("close")}}},[s("template",{slot:"header"},[s("h4",{domProps:{innerHTML:t._s(t.title)}})]),t._v(" "),t.message?s("p",{staticClass:"confirm-modal-message",domProps:{innerHTML:t._s(t.message)}}):t._e(),t._v(" "),s("template",{slot:"footer"},[s("div",{staticClass:"flex justify-end"},[s("button",{staticClass:"form-btn margin-r-16",on:{click:function(e){return e.preventDefault(),t._emit("close")}}},[t._v("\n        "+t._s(t.buttons.no)+"\n      ")]),t._v(" "),s("button",{staticClass:"form-sub",on:{click:function(e){return e.preventDefault(),t.saveChanges()}}},[t._v("\n        "+t._s(t.buttons.yes)+"\n      ")])])])],2)}),[],!1,null,null,null).exports,l={name:"VConfirmTemplate",components:{ConfirmModal:r},computed:{state:function(){return this.$root.state},modal:function(){return this.$root.modal}}},c=Object(i.a)(l,(function(){var t=this.$createElement;return(this._self._c||t)("v-confirm-modal",{attrs:{show:this.state.isShow,title:this.modal.title,message:this.modal.message,buttons:this.modal.buttons}})}),[],!1,null,null,null).exports;function d(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,a)}return s}function u(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?d(Object(s),!0).forEach((function(e){h(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):d(Object(s)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}function h(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}function p(t,e,s,a,o,n,i){try{var r=t[n](i),l=r.value}catch(c){return void s(c)}r.done?e(l):Promise.resolve(l).then(a,o)}function m(t){return function(){var e=this,s=arguments;return new Promise((function(a,o){var n=t.apply(e,s);function i(t){p(n,a,o,i,r,"next",t)}function r(t){p(n,a,o,i,r,"throw",t)}i(void 0)}))}}var v={data:{state:{isShow:!1,isConfirmed:!1,isNotConfirm:!1,time:0,interval:null},modal:{title:"",message:"",buttons:{no:"No",yes:"Yes"}},confirm:function(t,e){var s=this;return m(o.a.mark((function a(){return o.a.wrap((function(a){for(;;)switch(a.prev=a.next){case 0:return s.state.isShow=!0,Object.keys(t).forEach((function(e){(t[e]||s.modal[e])&&(s.modal[e]=t[e])})),a.next=4,s.callback().then((function(t){"function"===typeof e&&(e(t),s.resetState())}));case 4:case"end":return a.stop()}}),a)})))()},callback:function(){var t=this;return m(o.a.mark((function e(){return o.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e){t.state.interval=setInterval((function(){t.state.time+=1,t.state.isConfirmed&&(clearInterval(t.state.interval),e(!0)),t.state.isNotConfirm&&(clearInterval(t.state.interval),e(!1),t.close()),t.state.time>120&&(clearInterval(t.state.interval),e(!1),t.close())}),500)})));case 1:case"end":return e.stop()}}),e)})))()},resetState:function(){this.state={isConfirmed:!1,isNotConfirm:!1,isShow:!1,time:0}},close:function(){this.state.isNotConfirm=!0},_close:function(){clearInterval(this.state.interval),this.resetState()},updateConfirm:function(){this.state.isConfirmed=!0},isConfirm:function(){return this.state.isConfirmed}}};e.a={install:function(t,e){var s=u(u({},v),e);t.component(r.name,r);var a=new t({data:{state:s.data.state,modal:s.data.modal},render:function(t){return t(c)}});a.$mount(document.body.appendChild(document.createElement("div"))),a.callback=s.data.callback,a.isConfirm=s.data.isConfirm,a.resetState=s.data.resetState,a.confirm=s.data.confirm,a.close=s.data._close,a.$on("close",s.data.close),a.$on("save",s.data.updateConfirm),t.prototype.$vConfirm=a}}},79:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return o}));var a=function(t){return Number(t).toLocaleString()},o=function(t){return new Intl.NumberFormat("en-CA",{maximumSignificantDigits:3}).format(t)}},82:function(t,e,s){"use strict";var a={name:"Modal",components:{ActionIndicator:s(92).a},props:{id:{type:String,required:!0},showModal:{type:Boolean,required:!0},closeable:{type:Boolean,default:!0},backdropCloseable:{type:Boolean,default:!0},disableBodyScroll:{type:Boolean,default:!0},maxWidth:{type:String,required:!1,default:"480px"},maxHeight:{type:String,required:!1,default:""},isModalTopAligned:{type:Boolean,required:!1,default:!1},shouldScrollIntoView:{type:Boolean,required:!1,default:!1},withMainPadding:{type:Boolean,required:!1,default:!1},withSectionsPadding:{type:Boolean,required:!1,default:!0},withoutSectionsBorders:{type:Boolean,required:!1,default:!1},mobileFullScreen:{type:Boolean,required:!1,default:!1},shouldCloseOnEsc:{type:Boolean,required:!1,default:!1}},data:function(){return{headerSlot:!!this.$slots.header,footerSlot:!!this.$slots.footer}},computed:{styles:function(){return{maxWidth:this.maxWidth,maxHeight:this.maxHeight}},modalClass:function(){return{"is-small":this.isSmall,"mobile-full-screen":this.mobileFullScreen}},modalContainerClasses:function(){return{"constrain-height":""!==this.maxHeight}},modalContentClasses:function(){return{"with-main-padding":this.withMainPadding,"with-sections-padding":this.withSectionsPadding,"without-sections-borders":this.withoutSectionsBorders}}},watch:{showModal:function(t,e){t&&!e&&this.onOpened(),!t&&e&&this.onClosed()}},beforeDestroy:function(){this.onClosed()},mounted:function(){this.showModal&&this.onOpened(),this.shouldCloseOnEsc&&window.addEventListener("keyup",this.onKeyUp)},destroyed:function(){this.shouldCloseOnEsc&&window.removeEventListener("keyup",this.onKeyUp)},methods:{shouldTriggerClose:function(t){this.$refs.modal===t.target&&this.closeable&&this.backdropCloseable&&this.triggerClose()},triggerClose:function(){this.$emit("modal-close",{id:this.id})},onAfterLeave:function(){this.$emit("after-leave")},onKeyUp:function(t){"Escape"===t.key&&this.triggerClose()},onOpened:function(){this.shouldScrollIntoView&&this.scrollIntoView(),this.disableBodyScroll&&document.body.classList.add("noscroll"),this.$emit("modal-opened",{id:this.id})},onClosed:function(){this.disableBodyScroll&&document.body.classList.remove("noscroll")},scrollIntoView:function(){var t=this;this.$nextTick((function(){t.$refs.modal.scrollIntoView({behavior:"smooth"})}))}}},o=s(0),n=Object(o.a)(a,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("transition",{attrs:{appear:"",name:"fade-in"},on:{afterLeave:t.onAfterLeave}},[t.showModal?s("section",{ref:"modal",staticClass:"modal",class:t.modalClass,attrs:{id:t.id},on:{click:t.shouldTriggerClose}},[s("div",{staticClass:"modal-inner",class:{"top-align":t.isModalTopAligned}},[s("div",{staticClass:"modal-container",class:t.modalContainerClasses,style:t.styles},[t.closeable?s("action-indicator",{attrs:{"extra-classes":"modal-action-indicator"},on:{clicked:t.triggerClose}}):t._e(),t._v(" "),s("div",{staticClass:"modal-content-wrapper",class:t.modalContentClasses},[t.headerSlot?s("header",{staticClass:"modal-header"},[t._t("header")],2):t._e(),t._v(" "),s("div",{staticClass:"modal-content"},[t._t("default")],2),t._v(" "),t.footerSlot?s("footer",{staticClass:"modal-footer"},[t._t("footer")],2):t._e()])],1)])]):t._e()])}),[],!1,null,null,null);e.a=n.exports},92:function(t,e,s){"use strict";var a={name:"ActionIndicator",components:{Icon:s(17).a},props:{type:{type:String,required:!1,default:"close"},size:{type:String,required:!1,default:"medium"},hoverType:{type:String,required:!1,default:""},tooltip:{type:String,required:!1,default:null},tooltipOptions:{type:Object,required:!1,default:function(){return{}}},extraClasses:{type:String,required:!1,default:""}},data:function(){return{hover:!1}},computed:{indicatorType:function(){return this.hover&&this.hoverType?this.hoverType:this.type}}},o=s(0),n=Object(o.a)(a,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return t.tooltip?s("a",{directives:[{name:"tippy",rawName:"v-tippy",value:t.tooltipOptions,expression:"tooltipOptions"}],staticClass:"action-indicator",class:"action-indicator-"+t.indicatorType+" action-indicator-"+t.size+" "+t.extraClasses,attrs:{href:"#",title:t.tooltip},on:{click:function(e){return e.preventDefault(),t.$emit("clicked",e)},mouseover:function(e){t.hover=!0},mouseleave:function(e){t.hover=!1}}},["delete"===t.indicatorType||"delete-alt"===t.indicatorType?s("svg",{attrs:{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M4.667 2.917V3.5H2.333a.583.583 0 100 1.167h.584V10.5c0 .966.783 1.75 1.75 1.75h4.667a1.75 1.75 0 001.75-1.75V4.667h.583a.583.583 0 000-1.167H9.334v-.583a1.75 1.75 0 00-1.75-1.75H6.417a1.75 1.75 0 00-1.75 1.75zm1.75-.584a.583.583 0 00-.584.584V3.5h2.334v-.583a.583.583 0 00-.583-.584H6.417zm0 4.084a.583.583 0 00-1.167 0v2.916a.583.583 0 001.167 0V6.417zM8.75 9.333a.583.583 0 01-1.166 0V6.417a.583.583 0 011.166 0v2.916z",fill:"#fff"}})]):t._e(),t._v(" "),"close"===t.indicatorType?s("icon",{attrs:{"svg-class":"color-white",type:"close"}}):t._e(),t._v(" "),"error"===t.indicatorType?s("icon",{attrs:{type:"exclamation","svg-class":"color-white"}}):t._e(),t._v(" "),"locked"===t.indicatorType?s("svg",{attrs:{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M2.333 6.91c0-.594.523-1.077 1.167-1.077h7c.644 0 1.167.483 1.167 1.077v4.846c0 .595-.523 1.077-1.167 1.077h-7c-.644 0-1.167-.482-1.167-1.077V6.91z",fill:"#fff"}}),s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M7 2.625c-.886 0-1.604.718-1.604 1.604v2.333H3.938V4.23a3.063 3.063 0 016.124 0v2.333H8.604V4.23c0-.886-.718-1.604-1.604-1.604z",fill:"#fff"}})]):t._e(),t._v(" "),"rebound"===t.indicatorType?s("svg",{attrs:{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"clip-rule":"evenodd",d:"M12 24C5.373 24 0 18.627 0 12S5.373 0 12 0s12 5.373 12 12-5.373 12-12 12zm.248-14.626V7.592a.895.895 0 00-.333-.686 1.275 1.275 0 00-1.605 0l-5.344 4.57 5.344 4.57c.222.189.512.284.802.284.291 0 .581-.095.803-.285a.896.896 0 00.333-.686v-1.887c3.988.196 6.067 2.706 6.067 2.706 0-4.152-2.54-6.53-6.067-6.804z"}})]):t._e()],1):s("a",{staticClass:"action-indicator",class:"action-indicator-"+t.indicatorType+" action-indicator-"+t.size+" "+t.extraClasses,attrs:{href:"#"},on:{click:function(e){return e.preventDefault(),t.$emit("clicked",e)},mouseover:function(e){t.hover=!0},mouseleave:function(e){t.hover=!1}}},["delete"===t.indicatorType||"delete-alt"===t.indicatorType?s("svg",{attrs:{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M4.667 2.917V3.5H2.333a.583.583 0 100 1.167h.584V10.5c0 .966.783 1.75 1.75 1.75h4.667a1.75 1.75 0 001.75-1.75V4.667h.583a.583.583 0 000-1.167H9.334v-.583a1.75 1.75 0 00-1.75-1.75H6.417a1.75 1.75 0 00-1.75 1.75zm1.75-.584a.583.583 0 00-.584.584V3.5h2.334v-.583a.583.583 0 00-.583-.584H6.417zm0 4.084a.583.583 0 00-1.167 0v2.916a.583.583 0 001.167 0V6.417zM8.75 9.333a.583.583 0 01-1.166 0V6.417a.583.583 0 011.166 0v2.916z",fill:"#fff"}})]):t._e(),t._v(" "),"close"===t.indicatorType?s("svg",{attrs:{width:"10",height:"10",viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M6.768 5l2.866-2.866A1.25 1.25 0 107.866.366L5 3.232 2.134.366A1.25 1.25 0 10.366 2.134L3.232 5 .366 7.866a1.25 1.25 0 101.768 1.768L5 6.767l2.866 2.867c.244.244.564.366.884.366a1.25 1.25 0 00.884-2.134L6.768 5z",fill:"currentColor"}})]):t._e(),t._v(" "),"error"===t.indicatorType?s("svg",{attrs:{width:"4",height:"14",viewBox:"0 0 4 14",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M1 1a1 1 0 012 0v7a1 1 0 01-1 1h-.001a1 1 0 01-1-1V1zM2 14a1.5 1.5 0 11.001-3.001A1.5 1.5 0 012 14z",fill:"#fff"}})]):t._e(),t._v(" "),"locked"===t.indicatorType?s("svg",{attrs:{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M2.333 6.91c0-.594.523-1.077 1.167-1.077h7c.644 0 1.167.483 1.167 1.077v4.846c0 .595-.523 1.077-1.167 1.077h-7c-.644 0-1.167-.482-1.167-1.077V6.91z",fill:"#fff"}}),s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M7 2.625c-.886 0-1.604.718-1.604 1.604v2.333H3.938V4.23a3.063 3.063 0 016.124 0v2.333H8.604V4.23c0-.886-.718-1.604-1.604-1.604z",fill:"#fff"}})]):t._e(),t._v(" "),"rebound"===t.indicatorType?s("svg",{attrs:{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"clip-rule":"evenodd",d:"M12 24C5.373 24 0 18.627 0 12S5.373 0 12 0s12 5.373 12 12-5.373 12-12 12zm.248-14.626V7.592a.895.895 0 00-.333-.686 1.275 1.275 0 00-1.605 0l-5.344 4.57 5.344 4.57c.222.189.512.284.802.284.291 0 .581-.095.803-.285a.896.896 0 00.333-.686v-1.887c3.988.196 6.067 2.706 6.067 2.706 0-4.152-2.54-6.53-6.067-6.804z"}})]):t._e()])}),[],!1,null,null,null);e.a=n.exports},952:function(t,e,s){"use strict";var a=s(559),o=s(953),n={name:"Avatar",components:{LazyImg:a.a},props:{userId:{type:Number,required:!0},imageUrl:{type:String,required:!0},name:{type:String,required:!0},path:{type:String,required:!0},title:{type:String,required:!1,default:""},size:{type:String,required:!1,default:"regular",validator:function(t){return-1!==["tiny","small","regular","medium","large"].indexOf(t)}}},data:function(){return{imageSizes:{tiny:16,small:24,regular:32,medium:40,large:60}}},computed:{sizeClass:function(){return"avatar-".concat(this.size)},sizeParam:function(){var t=Object(o.a)()?2*this.imageSizes[this.size]:this.imageSizes[this.size],e=this.imageUrl.indexOf("?")>-1?"&":"?";return"".concat(e,"compress=1&resize=").concat(t,"x").concat(t)},imageSrc:function(){return"".concat(this.imageUrl).concat(this.sizeParam)}}},i=s(0),r=Object(i.a)(n,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("span",{staticClass:"profile-avatar"},[s("a",{staticClass:"avatar-link",attrs:{href:t.path,title:t.title}},[s("lazy-img",{key:t.imageUrl,class:"avatar-image-wrapper "+t.sizeClass,attrs:{id:t.userId,src:t.imageSrc,alt:t.name+"'s avatar","img-class":"avatar-image","error-fallback":"data:image/gif;base64,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"}},[s("div",{staticClass:"loading-template avatar animate-translate",class:t.sizeClass})]),t._v(" "),s("span",{staticClass:"accessibility-text"},[t._v("\n      Link to "+t._s(t.name)+"'s profile'\n    ")])],1)])}),[],!1,null,null,null);e.a=r.exports},953:function(t,e,s){"use strict";s.d(e,"a",(function(){return a}));var a=function(){return!!(window.devicePixelRatio>1||window.matchMedia&&window.matchMedia("(-webkit-min-device-pixel-ratio: 1.25), (min-resolution: 120dpi), (-webkit-min-device-pixel-ratio: 1.3), (min-resolution: 124.8dpi), (-webkit-min-device-pixel-ratio: 1.5), (min-resolution: 144dpi), (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi)").matches)}},954:function(t,e,s){},955:function(t,e,s){},96:function(t,e,s){"use strict";s.d(e,"a",(function(){return a}));var a=function(t,e){var s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Dribbble;return t.reduce((function(t,s){return t&&(t[s]||0===t[s]||!1===t[s])?t[s]:e||null}),s)}},963:function(t,e,s){"use strict";s(954)},964:function(t,e,s){"use strict";s(955)},972:function(t,e,s){"use strict";var a=s(6),o=s.n(a),n=s(10),i=s(952),r=s(559),l=s(9),c=s.n(l),d={name:"FollowButton",props:{username:{type:String,required:!0},isFollowing:{type:Boolean,required:!0},canFollow:{type:Boolean,required:!0},isBlocking:{type:Boolean,required:!1,default:!1}},data:function(){return{isProcessing:!1,isLoggedIn:c()(Dribbble,"JsConfig.user.isLoggedIn")}},methods:{onClick:function(){this.isLoggedIn&&(this.isProcessing=!0,this.isFollowing?this.unfollow():this.follow())},follow:function(){var t=this;o.a.post("/".concat(this.username,"/followers"),{},Object(n.axiosOptions)()).then((function(e){t.isProcessing=!1,t.$emit("userFollow",{isFollowing:!0,username:t.username});var s=c()(e,"response.data.data",null);s&&Dribbble.EventBus.$emit("track:userFollowed",s)})).catch((function(){t.isProcessing=!1,Dribbble.EventBus.$emit("siteNotifications:add",{title:"Error following ".concat(t.username),id:"follow-notification",isCloseable:!0,willAutoClose:!0,autoCloseDuration:5e3})}))},unfollow:function(){var t=this;o.a.delete("/".concat(this.username,"/followers/").concat(this.username),{headers:Object(n.axiosOptions)().headers}).then((function(){t.isProcessing=!1,t.$emit("userFollow",{isFollowing:!1,username:t.username})})).catch((function(){t.isProcessing=!1,Dribbble.EventBus.$emit("siteNotifications:add",{title:"Error unfollowing ".concat(t.username),id:"unfollow-notification",isCloseable:!0,willAutoClose:!0,autoCloseDuration:5e3})}))}}},u=s(0),h=Object(u.a)(d,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return t.canFollow&&t.isLoggedIn?s("button",{staticClass:"form-btn follow-btn",class:{outlined:t.isFollowing},on:{click:t.onClick}},[t.isProcessing?s("span",{staticClass:"spin-loading-icon dark"}):s("span",{staticClass:"follow-buttton-icon"},[t.isFollowing?s("svg",{class:"fill-current",attrs:{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M21.28 4.473a1.973 1.973 0 00-2.817.262L9.614 15.57 5.11 12.506c-.918-.626-2.161-.372-2.773.566s-.364 2.205.555 2.83L10.386 21 21.537 7.347a2.069 2.069 0 00-.257-2.874z"}})]):t._e(),t._v(" "),t.isFollowing?t._e():s("svg",{class:"fill-current",attrs:{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M20 10h-6V4a2 2 0 00-4 0v6H4a2 2 0 000 4h6v6a2 2 0 004 0v-6h6a2 2 0 000-4z"}})])]),t._v(" "),s("span",{staticClass:"follow-button-text"},[t._v("\n    "+t._s(t.isFollowing?"Following":"Follow")+"\n  ")])]):t._e()}),[],!1,null,null,null).exports,p=s(953),m={name:"DesignerHoverCard",components:{Avatar:i.a,LazyImg:r.a,FollowButton:h},props:{id:{type:[String,Number],required:!0},username:{type:String,required:!0},updateShotHeader:{type:Boolean,required:!1,default:!1},cardDelay:{type:Number,required:!1,default:200}},data:function(){return{isCardPendingDisplay:null,cardData:null,following:null,tippy:null}},computed:{isFollowing:function(){return null!==this.following?this.following:!!this.cardData.isFollowing},hasShots:function(){return this.cardData&&this.cardData.shots&&this.cardData.shots.length},hasTeams:function(){return this.cardData&&this.cardData.teams&&this.cardData.teams.length},headerClasses:function(){return{"margin-b-0":!this.hasShots&&!this.hasTeams,"margin-b-16":this.hasShots||this.hasTeams}}},mounted:function(){Dribbble.EventBus.$on("userFollow",this.updateCardDataFollowing)},destroyed:function(){Dribbble.EventBus.$off("userFollow",this.updateCardDataFollowing)},methods:{onTippyInit:function(t){this.tippy=t,this.tippy.options.hideOnClick=!0,this.tippy.options.wait=this.shouldShowCard,this.tippy.options.popperOptions={modifiers:{preventOverflow:{enabled:!1},hide:{enabled:!1}}}},shouldShowCard:function(t){var e=this;this.isCardPendingDisplay&&clearTimeout(this.isCardPendingDisplay),this.isCardPendingDisplay=setTimeout((function(){t(),e.$hoverCards.getCardData(e.username).then((function(t){e.cardData=t}))}),this.cardDelay)},onMouseLeave:function(){this.tippy.state.visible||(clearTimeout(this.isCardPendingDisplay),this.isCardPendingDisplay=null)},onUserFollow:function(t){var e=t.isFollowing,s=t.username;if(this.updateCardDataFollowing({isFollowing:e,username:s}),this.updateShotHeader){var a=document.querySelector(".js-shot-header-action-links");a&&(e?a.classList.add("followed-by-current-user"):a.classList.remove("followed-by-current-user"))}},updateCardDataFollowing:function(t){var e=t.username,s=t.isFollowing;this.cardData&&e===this.username&&(this.cardData.isFollowing=s,this.$hoverCards.updateCardDataFollowing({username:e,isFollowing:s}))},getShotSrc:function(t){var e=Object(p.a)()?"200x150":"140x105",s=t.indexOf("?")>-1?"&":"?";return"".concat(t).concat(s,"compress=1&resize=").concat(e)}}},v=Object(u.a)(m,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"designer-hovercard",attrs:{name:t.id},on:{mouseleave:t.onMouseLeave}},[s("tippy",{attrs:{to:t.id,reactive:!0,interactive:!0,arrow:!0,theme:"hover-card",distance:16,placement:"left-start"},on:{init:t.onTippyInit}},[s("div",{staticClass:"v-hover-card text-align-left"},[t.cardData?[s("div",{staticClass:"hover-card-header display-flex align-center",class:t.headerClasses},[s("div",{staticClass:"hover-card-avatar"},[s("avatar",{staticClass:"hover-card-user-avatar flex-grow-0",attrs:{"user-id":t.cardData.id,"image-url":t.cardData.avatarUrl,path:t.cardData.path,name:t.cardData.name,size:"large"}}),t._v(" "),t.cardData.isPro?s("a",{staticClass:"badge-link",attrs:{href:"/pro"}},[s("span",{staticClass:"badge badge-pro"},[t._v("Pro")])]):t._e(),t._v(" "),t.cardData.isDribbbleTeam?s("a",{staticClass:"badge-link",attrs:{href:"/dribbble/about"}},[s("span",{staticClass:"badge badge-pro"},[t._v("Dribbble")])]):t._e(),t._v(" "),t.cardData.isTeam?s("a",{staticClass:"badge-link",attrs:{href:"/teams"}},[s("span",{staticClass:"badge badge-pro"},[t._v("Team")])]):t._e()],1),t._v(" "),s("div",{staticClass:"hover-card-user-info flex-grow-1 padding-h-16"},[s("a",{staticClass:"hover-card-user-name font-label link-deep-blue-sea",attrs:{href:t.cardData.path}},[t._v("\n              "+t._s(t.cardData.name)+"\n            ")]),t._v(" "),s("p",{staticClass:"hover-card-user-location font-body-small color-deep-blue-sea-light-40"},[t._v("\n              "+t._s(t.cardData.location)+"\n            ")])]),t._v(" "),s("div",{staticClass:"hover-card-user-follow flex-grow-0 flex-shrink-0"},[s("follow-button",{attrs:{username:t.cardData.username,"is-following":t.isFollowing,"can-follow":t.cardData.canFollow,"is-blocking":t.cardData.isBlocking},on:{userFollow:t.onUserFollow}})],1)]),t._v(" "),t.hasShots?s("div",{staticClass:"hover-card-shots margin-b-16"},t._l(t.cardData.shots,(function(e,a){return s("div",{key:"item-"+a,staticClass:"hover-card-shot"},[s("a",{attrs:{href:e.path}},[s("lazy-img",{attrs:{id:a,src:t.getShotSrc(e.imageUrl),"img-class":"hover-card-shot-image"}},[s("div",{staticClass:"loading-template shot animate-translate"})])],1)])})),0):t._e(),t._v(" "),t.hasTeams?s("div",{staticClass:"hover-card-teams display-flex align-center"},[s("span",{staticClass:"hover-card-team-label font-sublabel-bold color-deep-blue-sea"},[t._v("\n            Teams:\n          ")]),t._v(" "),t._l(t.cardData.teams,(function(t){return s("avatar",{key:t.id,staticClass:"hover-card-team-avatar ml10",attrs:{"user-id":t.id,"image-url":t.avatarUrl,path:t.path,name:t.name,title:t.name}})}))],2):t._e()]:[s("div",{staticClass:"loading-template-hover-card display-flex align-center"},[s("div",{staticClass:"loading-template avatar large animate-translate flex-grow-0"}),t._v(" "),s("div",{staticClass:"flex-grow-1 padding-h-16"},[s("div",{staticClass:"loading-template bar h-medium margin-b-8 w-50 animate-translate"}),t._v(" "),s("div",{staticClass:"loading-template bar h-small w-75 animate-translate"})]),t._v(" "),s("div",{staticClass:"loading-template button animate-translate"})])]],2)]),t._v(" "),t._t("default")],2)}),[],!1,null,null,null).exports,f={loadedCardsData:{},fetchCardData:function(t){return new Promise((function(e,s){var a="/client_app/profile/hover_card/".concat(t);o.a.get(a,{params:{format:"json"}},Object(n.axiosOptions)()).then((function(t){if(t&&t.data){var s=t.data.data||t.data;e(s)}})).catch((function(t){s(t)}))}))},getCardData:function(t){var e=this;return new Promise((function(s){e.loadedCardsData[t]?s(e.loadedCardsData[t]):e.fetchCardData(t).then((function(a){e.loadedCardsData[t]=a,s(a)}))}))},updateCardDataFollowing:function(t){var e=t.username,s=t.isFollowing;this.loadedCardsData[e]&&(this.loadedCardsData[e].isFollowing=s)}};e.a={install:function(t){t.component("designer-hover-card",v),t.prototype.$hoverCards=f}}},973:function(t,e,s){"use strict";var a=s(46),o={name:"DetailsModal",components:{Modal:s(82).a},props:{isModalOpen:{type:Boolean,required:!0,default:!1},subjectId:{type:Number,required:!1,default:null},tags:{type:Array,required:!1,default:function(){return[]}},shouldLinkTags:{type:Boolean,required:!1,default:!1},viewsCount:{type:Number,required:!1,default:null},likesCount:{type:Number,required:!1,default:null},savesCount:{type:Number,required:!1,default:null},commentsCount:{type:Number,required:!1,default:null},postedOn:{type:String,required:!1,default:null},canCurrentUserRebound:{type:Boolean,required:!1,default:!1},reboundPath:{type:String,required:!1,default:null},doesShotHaveRebounds:{type:Boolean,required:!1,default:!1},subject:{type:String,required:!0,default:"shot"}},computed:{heading:function(){return"".concat(Object(a.c)(this.subject)," details")}}},n=(s(963),s(0)),i=Object(n.a)(o,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("modal",{attrs:{id:"details-modal","show-modal":t.isModalOpen,"max-width":"488px","with-sections-padding":!1,"with-main-padding":"","should-close-on-esc":""},on:{"modal-close":function(e){return t.$emit("close-modal")}}},[s("h3",[t._v("\n    "+t._s(t.heading)+"\n  ")]),t._v(" "),s("div",{staticClass:"font-body-small margin-t-12 color-deep-blue-sea-light-20"},[t._v("\n    Posted "+t._s(t.postedOn)+"\n  ")]),t._v(" "),s("div",{staticClass:"stats-container"},[s("div",{staticClass:"stat-group"},[s("div",{staticClass:"margin-r-40"},[s("div",{staticClass:"font-sublabel"},[t._v("\n          Views\n        ")]),t._v(" "),s("div",{staticClass:"font-body-large margin-t-8"},[t._v("\n          "+t._s(Number(t.viewsCount).toLocaleString())+"\n        ")])]),t._v(" "),null!==t.savesCount?s("a",{staticClass:"link-deep-blue-sea margin-r-40",attrs:{href:"/shots/"+t.subjectId+"/buckets"}},[s("div",{staticClass:"font-sublabel"},[t._v("\n          Saves\n        ")]),t._v(" "),s("div",{staticClass:"font-body-large margin-t-8"},[t._v("\n          "+t._s(Number(t.savesCount).toLocaleString())+"\n        ")])]):t._e()]),t._v(" "),s("div",{staticClass:"stat-group"},[null!==t.likesCount?s("a",{staticClass:"link-deep-blue-sea margin-r-40",attrs:{href:"/shots/"+t.subjectId+"/fans"}},[s("div",{staticClass:"font-sublabel"},[t._v("\n          Likes\n        ")]),t._v(" "),s("div",{staticClass:"font-body-large margin-t-8"},[t._v("\n          "+t._s(Number(t.likesCount).toLocaleString())+"\n        ")])]):t._e(),t._v(" "),null!==t.commentsCount?s("a",{staticClass:"link-deep-blue-sea",attrs:{href:"#"},on:{click:function(e){return e.preventDefault(),t.$emit("click-comments")}}},[s("div",{staticClass:"font-sublabel"},[t._v("\n          Comments\n        ")]),t._v(" "),s("div",{staticClass:"font-body-large margin-t-8"},[t._v("\n          "+t._s(Number(t.commentsCount).toLocaleString())+"\n        ")])]):t._e()])]),t._v(" "),t.tags.length?s("div",{staticClass:"margin-t-24"},[s("div",{staticClass:"font-sublabel"},[t._v("\n      Tags\n    ")]),t._v(" "),s("div",{staticClass:"margin-t-8 display-flex flex-wrap"},[t.shouldLinkTags?t._l(t.tags,(function(e){return s("a",{key:e,staticClass:"tag",attrs:{href:"/tags/"+e}},[t._v("\n          "+t._s(e)+"\n        ")])})):t._l(t.tags,(function(e){return s("span",{key:e,staticClass:"tag"},[t._v("\n          "+t._s(e)+"\n        ")])}))],2)]):t._e(),t._v(" "),t.doesShotHaveRebounds||t.canCurrentUserRebound?[s("div",{staticClass:"divider"}),t._v(" "),s("div",{staticClass:"margin-t-24 font-label"},[t._v("\n      Community engagement\n    ")]),t._v(" "),s("div",{staticClass:"display-flex margin-t-16"},[t.doesShotHaveRebounds?s("a",{staticClass:"form-btn margin-r-16",attrs:{href:"/shots/"+t.subjectId+"/rebounds"}},[t._v("View rebounds")]):t._e(),t._v(" "),t.canCurrentUserRebound?s("a",{staticClass:"form-btn",attrs:{href:t.reboundPath}},[t._v("Rebound shot")]):t._e()])]:t._e()],2)}),[],!1,null,"2b63fed8",null);e.a=i.exports},974:function(t,e,s){"use strict";var a=s(46),o=s(82),n=s(41),i=s(229),r={name:"ShareModal",components:{Modal:o.a,vButton:n.a},props:{subject:{type:String,required:!0},subjectData:{type:Object,required:!0},isModalOpen:{type:Boolean,required:!0},subjectId:{type:Number,required:!1,default:null},shareUtms:{type:Object,required:!0},subjectUser:{type:Object,required:!0},featureImageUrl:{type:String,required:!1,default:null},isBoostFooterVisible:{type:Boolean,required:!1,default:!1}},data:function(){return{subjectUrl:window.location.href}},computed:{clipboardShareUrl:function(){return Dribbble.Url.addParams(this.subjectUrl,[["utm_source","Clipboard_".concat(this.shareUtms.source)],["utm_campaign",this.shareUtms.campaign],["utm_content",this.shareUtms.content],["utm_medium","Social_Share"]])}},mounted:function(){this.setShareEvents()},methods:{onInputFocus:function(t){t.currentTarget.select()},setShareEvents:function(){new i.a({area:Object(a.c)(this.subject),trackShotShare:{shotId:this.subjectId,subjectUserId:this.subjectUser.id,location:"".concat(this.subject,"_description")},selector:"body",body:"".concat(this.subjectData.title," by ").concat(this.subjectUser.name),bodies:{twitter:"".concat(this.subjectData.title," by ").concat(this.subjectUser.twitter_username?"@#".concat(this.subjectUser.twitter_username):this.subjectUser.name)},imageUrl:this.featureImageUrl,outlets:["pinterest","twitter","clipboard"],attach:{pinterest:".js-share-pinterest",twitter:".js-share-twitter",clipboard:".js-share-clipboard"},shareUrl:this.clipboardShareUrl,utm:{source:Object(a.c)(this.subject),campaign:this.subjectUser.username,content:this.subjectData.title},delegateEvents:!0})}}},l=(s(964),s(0)),c=Object(l.a)(r,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("modal",{staticClass:"share-modal",attrs:{id:"share-modal","show-modal":t.isModalOpen,"max-width":"488px","with-sections-padding":!1,"should-close-on-esc":""},on:{"modal-close":function(e){return t.$emit("close-modal")}}},[s("div",{staticClass:"share-modal-header"},[s("div",{staticClass:"share-image-placeholder"},[s("img",{staticClass:"share-modal-image",attrs:{src:t.featureImageUrl,alt:"Featured image"}})])]),t._v(" "),s("div",{staticClass:"share-modal-body"},[s("h3",{staticClass:"share-modal-title"},[t._v("\n      Share this with your social Community\n    ")]),t._v(" "),s("div",{staticClass:"share-modal-buttons"},[s("a",{staticClass:"share-button-round pinterest js-share-pinterest",attrs:{href:"#","data-share-source":"pinterest","data-share-campaign":"pinterest_"+t.subject,title:"Share on Pinterest"}},[s("svg",{class:"icon-24 fill-current",attrs:{"aria-labelledby":"simpleicons-pinterest-icon",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.162-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.401.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.354-.629-2.758-1.379l-.749 2.848c-.269 1.045-1.004 2.352-1.498 3.146 1.123.345 2.306.535 3.55.535 6.607 0 11.985-5.365 11.985-11.987C23.97 5.39 18.592.026 11.985.026L12.017 0z"}})])]),t._v(" "),s("a",{staticClass:"share-button-round twitter js-share-twitter",attrs:{href:"#","data-share-source":"twitter","data-share-campaign":"twitter_"+t.subject,title:"Share on Twitter"}},[s("svg",{class:"icon-24 fill-current",attrs:{"aria-labelledby":"simpleicons-twitter-icon",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M23.954 4.569a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.691 8.094 4.066 6.13 1.64 3.161a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.061a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.937 4.937 0 004.604 3.417 9.868 9.868 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.054 0 13.999-7.496 13.999-13.986 0-.209 0-.42-.015-.63a9.936 9.936 0 002.46-2.548l-.047-.02z"}})])])]),t._v(" "),s("div",{staticClass:"share-modal-copy-action"},[s("label",{staticClass:"font-body-small color-deep-blue-sea-light-40",attrs:{for:"copy-url"}},[t._v("or copy link")]),t._v(" "),s("div",{staticClass:"position-relative"},[s("input",{staticClass:"share-modal-copy-input",attrs:{id:"copy-url",type:"text",readonly:""},domProps:{value:t.clipboardShareUrl},on:{focus:t.onInputFocus}}),t._v(" "),s("a",{staticClass:"share-modal-copy-link js-share-clipboard",attrs:{href:"#"}},[t._v("\n          Copy\n        ")])])])]),t._v(" "),t.isBoostFooterVisible?s("div",{staticClass:"boost-shot-footer"},[s("div",{staticClass:"text-container margin-t-4"},[s("div",{staticClass:"icon-container"},[s("svg",{class:"icon-20",attrs:{width:"20",height:"21",viewBox:"0 0 20 21",fill:"none",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M10 20.27c5.523 0 10-4.478 10-10 0-5.523-4.477-10-10-10s-10 4.477-10 10c0 5.522 4.477 10 10 10zm1.617-12.114a.693.693 0 010-1.386h3.467c.23 0 .416.186.416.416v3.466a.693.693 0 11-1.386 0V9.137l-3.415 3.415a1.25 1.25 0 01-1.768 0l-.763-.763a.417.417 0 00-.59 0l-1.607 1.607a.693.693 0 11-.98-.98l1.999-1.999a1.25 1.25 0 011.767 0l.763.763a.417.417 0 00.59 0l3.023-3.024h-1.515z",fill:"currentColor"}})])]),t._v(" "),s("span",{staticClass:"font-body-small color-white"},[t._v("Want more people to see your work?")])]),t._v(" "),s("v-button",{staticClass:"boost-btn js-boost-shot-button",attrs:{type:"primary",text:"Boost your shot","data-test":"boost-btn","data-referrer":"ShotShare"},on:{click:function(e){return t.$emit("click-boost",e)}}})],1):t._e()])}),[],!1,null,"91c0f7b4",null);e.a=c.exports},975:function(t,e,s){"use strict";var a=s(3);function o(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,a)}return s}function n(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?o(Object(s),!0).forEach((function(e){i(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):o(Object(s)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}function i(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}var r={name:"ActionButtonShare",props:{text:{type:String,required:!1,default:""}},methods:n(n({},Object(a.e)(["setShowShareModal"])),{},{onClickShare:function(){this.setShowShareModal(!0)}})},l=s(0),c=Object(l.a)(r,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("button",{staticClass:"btn-icon tertiary share-action",on:{click:t.onClickShare}},[s("svg",{class:"icon-16",attrs:{viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M0 12.782c0 .85.1 1.65.3 *********.35.45.5 0 1.05-2.65 2.75-5.15 5.55-5.65H8v2.2c0 1 .6 1.3 1.3.7l6.4-5.5c.35-.3.35-.8 0-1.15L9.3.332c-.7-.65-1.3-.3-1.3.65v2.35c-4.8.8-8 4.7-8 9.45z",fill:"currentColor"}})]),t._v(" "),s("span",{staticClass:"accessibility-text"},[t._v("\n    Share actions\n  ")]),t._v(" "),t.text?s("span",{staticClass:"btn-text-right"},[t._v("\n    "+t._s(t.text)+"\n  ")]):t._e()])}),[],!1,null,null,null);e.a=c.exports},976:function(t,e,s){"use strict";var a=s(3);function o(t,e){var s=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),s.push.apply(s,a)}return s}function n(t){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?o(Object(s),!0).forEach((function(e){i(t,e,s[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(s)):o(Object(s)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(s,e))}))}return t}function i(t,e,s){return e in t?Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!0}):t[e]=s,t}var r={name:"ActionButtonShotDetails",props:{text:{type:String,required:!1,default:""}},methods:n(n({},Object(a.e)(["setShowShotDetailsModal"])),{},{onClickShotDetails:function(){this.setShowShotDetailsModal(!0)}})},l=s(0),c=Object(l.a)(r,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("button",{staticClass:"btn-icon tertiary",on:{click:t.onClickShotDetails}},[s("svg",{class:"icon-16",attrs:{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M12 0C5.373 0 0 5.37 0 12c0 6.627 5.373 12 12 12s12-5.373 12-12c0-6.63-5.373-12-12-12zm1 18a1 1 0 01-2 0v-7a1 1 0 012 0zM12 8a1.5 1.5 0 11.001-3.001A1.5 1.5 0 0112 8z"}})]),t._v(" "),s("span",{staticClass:"accessibility-text"},[t._v("\n    Detail actions\n  ")]),t._v(" "),t.text?s("span",{staticClass:"btn-text-right"},[t._v("\n    "+t._s(t.text)+"\n  ")]):t._e()])}),[],!1,null,null,null);e.a=c.exports},977:function(t,e,s){"use strict";var a={name:"ActionButtonCollection",props:{isSaved:{type:Boolean,required:!0}},methods:{onClickButton:function(){var t=document.querySelector(".shot-header-content a.bucket-shot");t&&t.click()}}},o=s(0),n=Object(o.a)(a,(function(){var t=this.$createElement,e=this._self._c||t;return e("button",{staticClass:"btn-icon collection-action",class:this.isSaved?"highlighted":"tertiary",on:{click:this.onClickButton}},[e("svg",{attrs:{width:"16",height:"16",fill:"none",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[e("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M7.334 3.333h7.333c.736 0 1.333.598 1.333 1.334v8.666c0 .736-.597 1.334-1.333 1.334H1.333A1.334 1.334 0 010 13.334V2.667c0-.736.597-1.333 1.333-1.333H6l1.334 2zM8.667 10h2c.367 0 .667-.3.667-.666 0-.367-.3-.667-.667-.667h-2v-2C8.667 6.3 8.367 6 8 6c-.366 0-.666.3-.666.667v2h-2c-.367 0-.667.3-.667.667 0 .366.3.666.666.666h2v2c0 .367.3.667.667.667.367 0 .667-.3.667-.667v-2z",fill:"currentColor"}})]),this._v(" "),e("span",{staticClass:"accessibility-text"},[this._v("\n    Save this shot to a collection\n  ")])])}),[],!1,null,null,null);e.a=n.exports},978:function(t,e,s){"use strict";var a={name:"ActionButtonLike",props:{isLiked:{type:Boolean,required:!0},isLikeProcessing:{type:Boolean,required:!1,default:!1},hasText:{type:Boolean,required:!1,default:!1}},computed:{animationName:function(){return this.isLiked?"like-icon":"unlike-icon"}},methods:{onClickButton:function(){var t=document.querySelector(".shot-header-content a.like-shot");t&&t.click()}}},o=(s(1026),s(0)),n=Object(o.a)(a,(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("button",{staticClass:"btn-icon like-action like-button",class:t.isLiked?"is-liked highlighted":"tertiary",on:{click:function(e){return e.preventDefault(),t.onClickButton.apply(null,arguments)}}},[s("span",{staticClass:"like-button-icon"},[t.isLikeProcessing?s("span",{staticClass:"like-button-icon-loading"}):t._e(),t._v(" "),s("transition",{attrs:{name:t.animationName}},[t.isLikeProcessing?t._e():s("svg",{class:"like-button-icon-heart",attrs:{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg","svg-inline":"",role:"presentation",focusable:"false",tabindex:"-1"}},[s("path",{attrs:{d:"M18.199 2.04c-2.606-.284-4.262.961-6.199 3.008C9.955 3.001 8.407 1.756 5.801 2.04 2.257 2.428-.52 6.47.083 10 1.049 15.659 6.027 19 12 22c5.973-3 10.951-6.341 11.917-12 .603-3.53-2.174-7.572-5.718-7.96z"}})])])],1),t._v(" "),t.hasText?s("span",{staticClass:"like-button-text"},[t._v("\n    "+t._s(t.isLiked?"Liked":"Like")+"\n  ")]):t._e(),t._v(" "),s("span",{staticClass:"accessibility-text"},[t._v("\n    Like this shot\n  ")])])}),[],!1,null,"3249d1f6",null);e.a=n.exports},979:function(t,e,s){},980:function(t,e,s){},981:function(t,e,s){},982:function(t,e,s){}}]);
//# sourceMappingURL=95-5d9107ad04c0b819c57d.chunk.js.map