import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:localguide/Model/catnames.dart';
import 'package:localguide/Screens/user/Pages/Homescreen/Listshow.dart';
import 'package:localguide/Util/constants.dart';
class homescreen extends StatefulWidget {



  @override
  _homescreenState createState() => _homescreenState();
}

class _homescreenState extends State<homescreen> {
  FirebaseFirestore firebaseFirestore=FirebaseFirestore.instance;
  FirebaseAuth auth=FirebaseAuth.instance;
  List<catnames>listdata=[
    catnames("1","Excursions Sites Events Activities","images/activities.jpg"),
    catnames("1","Souvenirs Shops Markets","images/shops.jpg"),
    catnames("1","Restaurants Snacks","images/rest.jpg"),
    catnames("1","Bars Coffee","images/bars.jpg"),
    catnames("1","Boats Ferries","images/boats.jpg"),
    catnames("1","Transfers Rent a Vehicle","images/rentcar.jpg"),
    catnames("1","Hotels","images/hotels.jpg"),
    catnames("1","Services Banks Health","images/fire.jpg")

  ];
  late Position _currentPosition;
  String? longitude;
  String? latitude ;
  String? city;
  String? country;
  String? address ;

String name='';
  @override
  void initState() {
    getuserdata();

    super.initState();
  }

  Future<void> getuserdata()
  async {
    final User? user = auth.currentUser;
    final uid = user?.uid;

    var snapshot = await FirebaseFirestore.instance
        .collection("Users")
        .doc(uid)
        .get();
    Map<String, dynamic>? data = snapshot.data();

    setState(() {

      name= data!['name'];

    });


  }
  @override
  Widget build(BuildContext context) {

           return SafeArea(
             child: Padding(
               padding: const EdgeInsets.all(20.0),
               child: Column(
                 crossAxisAlignment: CrossAxisAlignment.start,
                 children: [


                   Row(
                     children: [
                       Text("Hello ",style:headingstyle,),
                       Text("$name !",style:headingstyle2,),
                     ],
                   ),
                   smallgap,
                   Text("Find The Best Nearby yours",style:subheadingstyle,),
                   smallgap,
                   smallgap,
                   smallgap,
                   smallgap,
                   Expanded(
                     child: StaggeredGridView.countBuilder(
                       crossAxisCount: 2,
                       crossAxisSpacing: 10,
                       mainAxisSpacing: 10,
                       itemCount:listdata.length,
                       itemBuilder: (context, index) {
                         return GestureDetector(
                           onTap:(){
                            log(listdata[index].name!.length);

                             Navigator.push(
                                 context,
                                 new MaterialPageRoute(builder: (context) => listshowplaces(listdata[index].name.toString())));

                           },
                           child: Container(

                             decoration: BoxDecoration(
                                 color: Colors.transparent,
                                 borderRadius: BorderRadius.all(
                                     Radius.circular(15))
                             ),
                             child: Stack(
                               fit: StackFit.expand,
                               children: [
                                 ClipRRect(
                                   borderRadius: BorderRadius.all(
                                       Radius.circular(15)),
                                   child:Image.asset(listdata[index].image.toString(),fit: BoxFit.cover,
                                   ),

                                 ),
                                 Padding(
                                   padding: const EdgeInsets.all(8.0),
                                   child: Text(
                                     listdata[index].name.toString(),
                                     style: TextStyle(
                                       fontSize: 23,
                                       color: Colors.white,
                                       fontWeight: FontWeight.bold,
                                     ), ),
                                 )
                               ],
                             ),
                           ),
                         );
                       }, staggeredTileBuilder: (int index) {
                         return StaggeredTile.count(1, index.isEven ? 2 :1.5); },
                     ),
                   ),

                 ],
               ),
             ),

          );
        }

}