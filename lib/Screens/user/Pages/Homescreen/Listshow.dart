import 'dart:developer';
import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:localguide/Controller/Services.dart';
import 'package:localguide/Model/PlacesModel.dart';
import 'package:localguide/Screens/user/Pages/Homescreen/Detailedscreen.dart';
import 'package:localguide/Screens/user/Pages/Homescreen/Searchcity.dart';
import 'package:localguide/Screens/user/Pages/Homescreen/Showdetail.dart';
import 'package:localguide/Util/constants.dart';

class listshowplaces extends StatefulWidget {
  String data;

  listshowplaces(this.data);

  @override
  _listshowplacesState createState() => _listshowplacesState();
}

class _listshowplacesState extends State<listshowplaces> {
  //states
  var templist = [];
  List placelist = [];
  bool isloading = true;
  bool isloading2 = false;

  String cityy='';
  late Position currentposition;

  //behaviours
  @override
  void initState() {
    _getCurrentLocation();
  }

  _getCurrentLocation() async {
    try {
      Position position = await Geolocator.getCurrentPosition(
        locationSettings: LocationSettings(accuracy: LocationAccuracy.best)
      );
      setState(() {
        currentposition = position;
      });

      _getAddressFromLatLng(
          currentposition.latitude, currentposition.longitude);
    } catch (e) {
      print(e);
    }
  }

  _getAddressFromLatLng(double lat, double long) async {
    try {
      List<Placemark> p = await placemarkFromCoordinates(lat, long);
      Placemark place = p[0];

      cityy=place.locality ?? '';
      getallnearby(place.locality ?? '');
    } catch (e) {
      print(e);
    }
  }

  double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    var p = 0.017453292519943295;
    var c = cos;
    var a = 0.5 -
        c((lat2 - lat1) * p) / 2 +
        c(lat1 * p) * c(lat2 * p) * (1 - c((lon2) - lon1 * p)) / 2;
    return 12742 * asin(sqrt(a));
  }

  getallnearby(String city) async {
    var collection =
        FirebaseFirestore.instance.collection("${widget.data}").get();
    var querySnapshot = await collection;
    for (var queryDocumentSnapshot in querySnapshot.docs) {
      Map<String, dynamic> data = queryDocumentSnapshot.data();

     print(data.toString());
double inmeter=calculateDistance(currentposition.latitude,
  currentposition.longitude,double.parse(data['latitude']),
    double.parse(data['longitude']));
      double currentInMeters = Geolocator.distanceBetween(
          currentposition.latitude,
          currentposition.longitude,
          double.parse(data['latitude']),
          double.parse(data['longitude']));
      double distanceinkm = currentInMeters / 1000.round();
      print("distanceInMeters:${distanceinkm.toInt()}");
      print("latitude: ${data['latitude']}");
print(distanceinkm.toString());
      if(distanceinkm.toInt()>=0 && distanceinkm.toInt()<100)
        {
          setState(() {
            placelist.add(data);
            templist.add(queryDocumentSnapshot.id);

          });
        }
    }

    if( placelist.length>0)
      {
        setState(() {
          isloading=false;
        });
      }
    else{
      setState(() {
        isloading=false;
        isloading2=true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Stack(
            children: [
              Container(
                width:MediaQuery.of(context).size.width,
              height:MediaQuery.of(context).size.height,

                child: Column(
                  children: [
                    Row(
                      children: [
                        GestureDetector(
                            onTap: () {
                              Navigator.of(context).pop();
                            },
                            child: Icon(
                              FontAwesomeIcons.arrowCircleLeft,
                              color: Colors.white,
                              size: 20,
                            )),
                        SizedBox(
                          width: 15,
                        ),
                        Container(
                            width: MediaQuery.of(context).size.width*0.7,
                            child: Text("${widget.data}", style: headingstyle,overflow:TextOverflow.ellipsis,))                      ],
                    ),
                    smallgap,
                    smallgap,
                    smallgap,
                    GestureDetector(
                      onTap:(){

                        Navigator.push(
                            context,
                            new MaterialPageRoute(
                                builder: (context) =>
                                    searchcity(widget.data,cityy)));

                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black38,
                              blurRadius: 25,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: TextField(
                          enabled:false,
                          decoration: InputDecoration(
                              prefixIcon: Icon(
                                Icons.search,
                                size: 25,
                              ),
                              border: InputBorder.none,
                              hintText: "Search within City"),
                          style: TextStyle(fontSize: 17),
                        ),
                      ),
                    ),
                    smallgap,
                    smallgap,
                    smallgap,
                    Expanded(
                      child:
                      ListView.builder(
                                  itemCount:placelist.length,
                                  shrinkWrap: true,
                                  itemBuilder: (context, index) {
                                    return GestureDetector(
                                      onTap: () {},
                                      child: Container(
                                        width: MediaQuery.of(context).size.width,
                                        margin: EdgeInsets.symmetric(
                                            horizontal: 0, vertical: 6),
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 0, vertical: 10),
                                        decoration: BoxDecoration(
                                          color: Colors.white12,
                                          borderRadius: BorderRadius.circular(
                                              10), //border corner radius
                                          boxShadow: const [
                                            BoxShadow(
                                              color: Colors.black12,
                                              //color of shadow
                                              spreadRadius: 1,
                                              //spread radius
                                              blurRadius: 1,
                                              // blur radius
                                              offset: Offset(
                                                  0, 2), // changes position of shadow
                                              //first paramerter of offset is left-right
                                              //second parameter is top to down
                                            ),
                                            //you can set more BoxShadow() here
                                          ],
                                        ),
                                        child: ListTile(
                                          onTap: () {
                                            Navigator.push(
                                                context,
                                                new MaterialPageRoute(
                                                    builder: (context) =>
                                                        showdetail(placelist[index],widget.data,templist[index])));

                                            // Services c=Services();
                                            // c.getLocation(ds['longitude'].toString(),ds['latitude'].toString());
                                          },
                                          leading: ClipRRect(
                                            borderRadius: BorderRadius.circular(10),
                                            child: FadeInImage.assetNetwork(
                                              placeholder: "images/img.png",
                                              image: "${placelist[index]['images'][0]}",
                                              width: 60,
                                              imageErrorBuilder:
                                                  (context, error, stackTrace) {
                                                return Image.asset("images/img.png",
                                                    height: 60,
                                                    width: 60,
                                                    fit: BoxFit.cover);
                                              },
                                              height: 60,
                                              fit: BoxFit.cover,
                                            ),
                                          ),

                                          title: Text(
                                            '${placelist[index]['title']}',
                                            style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 18,
                                                fontWeight: FontWeight.bold),
                                          ),
                                          subtitle: Padding(
                                            padding: const EdgeInsets.symmetric(vertical: 10.0),
                                            child: Row(
                                              children: [
                                                Icon(
                                                  Icons.add_location,
                                                  color: Colors.red,
                                                  size:14,
                                                ),
                                                SizedBox(
                                                  width: 5,
                                                ),
                                                Text(
                                                  '${placelist[index]['city']}',
                                                  style: TextStyle(
                                                      color: Colors.white70,
                                                  )
                                                ),
                                              ],
                                            ),
                                          ),
                                          trailing: Icon(
                                            FontAwesomeIcons.arrowCircleRight,
                                            color: Colors.redAccent,
                                          ),
                                          //
                                          // subtitle: Text('${ds['title']}',
                                          //     style: TextStyle(
                                          //         color: Colors.white70)),
                                          selected: true,
                                        ),
                                        //BoxDecoration
                                      ),
                                    );
                                  })


                    )
                  ],
                ),
              ),
              Visibility(
                visible:isloading,
                child: Center(
                  child:CircularProgressIndicator(color:Colors.white)
                ),
              ),
              Visibility(
                visible:isloading2,
                child: Center(
                  child:Text("Not Found",style:TextStyle(color:Colors.white,fontWeight: FontWeight.bold),),
                ),
              ),

            ],
          ),
        ),
      ),
    );
  }
}
