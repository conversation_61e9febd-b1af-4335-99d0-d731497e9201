# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      url: "https://pub.dartlang.org"
    source: hosted
    version: "22.0.0"
  analyzer:
    dependency: "direct dev"
    description:
      name: analyzer
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.7.2"
  android_intent:
    dependency: "direct main"
    description:
      name: android_intent
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.2"
  archive:
    dependency: transitive
    description:
      name: archive
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.3.1"
  args:
    dependency: transitive
    description:
      name: args
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.1"
  async:
    dependency: transitive
    description:
      name: async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.8.2"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  characters:
    dependency: transitive
    description:
      name: characters
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  charcode:
    dependency: transitive
    description:
      name: charcode
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.1"
  cli_util:
    dependency: transitive
    description:
      name: cli_util
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.5"
  clock:
    dependency: transitive
    description:
      name: clock
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  cloud_firestore:
    dependency: "direct main"
    description:
      name: cloud_firestore
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.2.0"
  cloud_firestore_platform_interface:
    dependency: transitive
    description:
      name: cloud_firestore_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.7.4"
  cloud_firestore_web:
    dependency: transitive
    description:
      name: cloud_firestore_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.6.18"
  collection:
    dependency: transitive
    description:
      name: collection
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.16.0"
  convert:
    dependency: transitive
    description:
      name: convert
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.2"
  crypto:
    dependency: transitive
    description:
      name: crypto
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.2"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.5"
  equatable:
    dependency: transitive
    description:
      name: equatable
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.6"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0"
  ffi:
    dependency: transitive
    description:
      name: ffi
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  file:
    dependency: transitive
    description:
      name: file
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.4"
  firebase_auth:
    dependency: "direct main"
    description:
      name: firebase_auth
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  firebase_auth_platform_interface:
    dependency: transitive
    description:
      name: firebase_auth_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.0.0"
  firebase_auth_web:
    dependency: transitive
    description:
      name: firebase_auth_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  firebase_core:
    dependency: "direct main"
    description:
      name: firebase_core
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.22.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.5.1"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.7.2"
  firebase_storage:
    dependency: "direct main"
    description:
      name: firebase_storage
      url: "https://pub.dartlang.org"
    source: hosted
    version: "8.1.3"
  firebase_storage_platform_interface:
    dependency: transitive
    description:
      name: firebase_storage_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.2"
  firebase_storage_web:
    dependency: transitive
    description:
      name: firebase_storage_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.2"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_image_slideshow:
    dependency: "direct main"
    description:
      name: flutter_image_slideshow
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.4"
  flutter_launcher_icons:
    dependency: "direct main"
    description:
      name: flutter_launcher_icons
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.9.3"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.7"
  flutter_staggered_grid_view:
    dependency: "direct main"
    description:
      name: flutter_staggered_grid_view
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.1"
  flutter_test:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  fluttertoast:
    dependency: "direct main"
    description:
      name: fluttertoast
      url: "https://pub.dartlang.org"
    source: hosted
    version: "8.0.9"
  font_awesome_flutter:
    dependency: "direct main"
    description:
      name: font_awesome_flutter
      url: "https://pub.dartlang.org"
    source: hosted
    version: "9.2.0"
  geocode:
    dependency: "direct main"
    description:
      name: geocode
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.2"
  geolocator:
    dependency: "direct main"
    description:
      name: geolocator
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.3.2+2"
  glob:
    dependency: transitive
    description:
      name: glob
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  google_api_availability:
    dependency: transitive
    description:
      name: google_api_availability
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  google_maps_flutter:
    dependency: "direct main"
    description:
      name: google_maps_flutter
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.0"
  google_maps_flutter_android:
    dependency: transitive
    description:
      name: google_maps_flutter_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.0"
  google_maps_flutter_ios:
    dependency: transitive
    description:
      name: google_maps_flutter_ios
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.11"
  google_maps_flutter_platform_interface:
    dependency: transitive
    description:
      name: google_maps_flutter_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.2"
  google_sign_in:
    dependency: "direct main"
    description:
      name: google_sign_in
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.4.1"
  google_sign_in_android:
    dependency: transitive
    description:
      name: google_sign_in_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.1"
  google_sign_in_ios:
    dependency: transitive
    description:
      name: google_sign_in_ios
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.5.0"
  google_sign_in_platform_interface:
    dependency: transitive
    description:
      name: google_sign_in_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.0"
  google_sign_in_web:
    dependency: transitive
    description:
      name: google_sign_in_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.10.2"
  http:
    dependency: transitive
    description:
      name: http
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.13.5"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.1"
  image:
    dependency: transitive
    description:
      name: image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.2.0"
  intl:
    dependency: transitive
    description:
      name: intl
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.17.0"
  js:
    dependency: transitive
    description:
      name: js
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.4"
  like_button:
    dependency: "direct main"
    description:
      name: like_button
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  location:
    dependency: "direct main"
    description:
      name: location
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.4.0"
  location_permissions:
    dependency: transitive
    description:
      name: location_permissions
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.0+1"
  location_platform_interface:
    dependency: transitive
    description:
      name: location_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.0"
  location_web:
    dependency: transitive
    description:
      name: location_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.1"
  matcher:
    dependency: transitive
    description:
      name: matcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.12.11"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.4"
  meta:
    dependency: transitive
    description:
      name: meta
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.7.0"
  modal_progress_hud_nsn:
    dependency: "direct main"
    description:
      name: modal_progress_hud_nsn
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.0-nullsafety-1"
  package_config:
    dependency: transitive
    description:
      name: package_config
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  path:
    dependency: transitive
    description:
      name: path
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.7"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.3"
  pedantic:
    dependency: transitive
    description:
      name: pedantic
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.11.1"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.0.0"
  platform:
    dependency: transitive
    description:
      name: platform
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.0"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.3"
  process:
    dependency: transitive
    description:
      name: process
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.2.4"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  quiver:
    dependency: transitive
    description:
      name: quiver
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.0"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.15"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.13"
  shared_preferences_ios:
    dependency: transitive
    description:
      name: shared_preferences_ios
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  shared_preferences_macos:
    dependency: transitive
    description:
      name: shared_preferences_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  source_span:
    dependency: transitive
    description:
      name: source_span
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.2"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.10.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  test_api:
    dependency: transitive
    description:
      name: test_api
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.9"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.1"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.5"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.0.19"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.0.17"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.13"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.2"
  watcher:
    dependency: transitive
    description:
      name: watcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  win32:
    dependency: transitive
    description:
      name: win32
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.0"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0+2"
  xml:
    dependency: transitive
    description:
      name: xml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.0"
  yaml:
    dependency: transitive
    description:
      name: yaml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.1"
sdks:
  dart: ">=2.17.0 <3.0.0"
  flutter: ">=3.0.0"
